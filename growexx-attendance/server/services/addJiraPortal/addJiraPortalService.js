const AddJiraPortalValidator = require('./addJiraPortalValidator');
const Portal = require('../../models/portal.model');
const GeneralError = require('../../util/GeneralError');
const JiraClient = require('jira.js');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for  add jira portal.
 */
class AddJiraPortalService {

    /**
     * @desc This function is being used to add jira portal
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addJiraPortal (req, user, locale) {
        const Validator = new AddJiraPortalValidator(req.body, locale);
        Validator.validationAddJiraPortal();

        const url = Utils.extractHostname(req.body.url);

        const portal = await Portal.findOne({ url }, { _id: 1 });

        if (portal && portal.id) {
            throw new GeneralError(locale('PORTAL_EXISTS'), 400);
        }
        const jira = new JiraClient.Version2Client({
            host: `https://${url}`,
            authentication: {
                basic: {
                    email: req.body.email,
                    apiToken: req.body.token
                }
            }
        });

        try {
            await jira.issueSearch.searchForIssuesUsingJql({
                maxResults: 1
            });
        } catch (error) {
            CONSOLE_LOGGER.error(error);
            throw new GeneralError(locale('INVALID_JIRA_CREDENTIALS'), 400);
        }

        return await Portal.create({
            url,
            name: req.body.name,
            email: req.body.email,
            token: req.body.token
        });
    }

}

module.exports = AddJiraPortalService;
