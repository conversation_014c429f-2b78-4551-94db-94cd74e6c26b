const pass = '1e879ae4d53496184ed3ce2ec1f1d0f4:8249253cdbbe7462afccd5a0cb3b42dcfb9ede391da36e2af7e3739ba34c77b7d25a37806b4b930affd846c6ca7b981b0a4c20c6e516e2c85ceb27e040a285bec3026bdcb8c68f18907be1bf23f30c00';

module.exports = {
    async up (db) {
        const employeeId = [46, 204];
        const designationsWebAndMobile = [
            'Technical Lead',
            'Sr. Software Engineer',
            'Sr. Executive - Lead Generation',
            'Sr. Technical Lead',
            'Product Owner',
            'Sr. Technical Project Manager',
            'Technical Lead – DevOps',
            'Sr. Software Engineer – QA',
            'Sr. Product Owner',
            'Software Engineer',
            'Sr. Software Engineer – DevOps',
            'Executive - Lead Generation',
            'Technical Project Manager',
            'Executive – Web Research',
            'Jr. Software Engineer',
            'Software Engineer – QA',
            'Sr. Business Analyst',
            'Sr. Graphic Designer',
            'Scrum Master',
            'Product Owner'
        ];
        const designationsDataScience = [
            'Jr. Data Engineer',
            'Jr. Software Engineer',
            'Lead - Data Engineering',
            'Manager - Data Engineering'
        ];
        for (var i = 0 ;i < employeeId.length;i++) {
            let addUserObj = {};
            let buAddObj = {};
            if (employeeId[i] === 46) {
                buAddObj = {
                    name: 'Data Science',
                    designations: designationsDataScience
                };
            } else if (employeeId[i] === 204) {
                buAddObj = {
                    name: 'Web & Mobile',
                    designations: designationsWebAndMobile
                };
            }
            const userData = await db.collection('users').find({ employeeId: employeeId[i] }).toArray();
            if (userData && userData.length > 0) {
                await db.collection('businessunits').insertOne({ ...buAddObj, userId: userData[0]._id });
                await db.collection('users').updateOne({ employeeId: employeeId[i] }, {
                    $set: { role: 5 }
                });
            } else {
                if (employeeId[i] === 204) {
                    addUserObj = {
                        employeeId: employeeId[i],
                        firstName: 'Jignesh',
                        lastName: 'Pandya',
                        department: 'Web & mobile',
                        designation: 'Business Unit Head',
                        countryCode: '+91',
                        phoneNumber: '978465231',
                        email: '<EMAIL>',
                        password: pass,
                        isActive: 1,
                        label: ['jignesh.pandya'],
                        role: 5
                    };
                } else if (employeeId[i] === 46) {
                    addUserObj = {
                        employeeId: employeeId[i],
                        firstName: 'Jaydeep',
                        lastName: 'Dosi',
                        department: 'Data Science',
                        designation: 'Business Unit Head',
                        countryCode: '+91',
                        phoneNumber: '978465231',
                        email: '<EMAIL>',
                        password: pass,
                        isActive: 1,
                        label: ['jaydeep.dosi'],
                        role: 5
                    };
                }
                const addUser = await db.collection('users').insertOne(addUserObj);
                await db.collection('businessunits').insertOne({ ...buAddObj, userId: addUser.insertedId });
            }
        }
    }
};
