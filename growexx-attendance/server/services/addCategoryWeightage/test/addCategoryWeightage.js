module.exports = {
    addCategoryWeightage: [{
        it: 'As a admin I should validate if category is not pass',
        options: {
            weightage: 40,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category is pass as blank',
        options: {
            category: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category is pass as invalid',
        options: {
            category: 40,
            weightage: 40,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category must be passed from defined list',
        options: {
            category: 'ABC',
            weightage: 40,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is not pass',
        options: {
            category: 'Business Result',
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is pass as blank',
        options: {
            weightage: '',
            category: 'Business Result',
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is pass as invalid',
        options: {
            category: 'Business Result',
            weightage: 'ABC',
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is pass less than zero',
        options: {
            category: 'Business Result',
            weightage: -1,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is pass greater than hundred',
        options: {
            category: 'Business Result',
            weightage: 101,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is not pass',
        options: {
            category: 'Business Result',
            weightage: 40
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is pass as blank',
        options: {
            designation: '',
            category: 'Business Result',
            weightage: 40

        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is pass as invalid',
        options: {
            category: 'Business Result',
            weightage: 40,
            designation: 40
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if weightage is  not more than hundred',
        options: {
            'categoryWeightages': [
                {
                    designation: 'Data Scientist',
                    weightage: 100,
                    category: 'Business Result',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Value Add',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Impact to customer',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Process Adherence',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 10,
                    category: 'Development Goals',
                    quarter: 'Q1',
                    year: 2022

                }
            ]
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation  is  not same for all',
        options: {
            'categoryWeightages': [
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Business Result',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Jr. Software Engineer',
                    weightage: 20,
                    category: 'Value Add',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Impact to customer',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Process Adherence',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 10,
                    category: 'Development Goals',
                    quarter: 'Q1',
                    year: 2022

                }
            ]
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if quarter  is  not same for all',
        options: {
            'categoryWeightages': [
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Business Result',
                    quarter: 'Q2',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Value Add',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Impact to customer',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Process Adherence',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 10,
                    category: 'Development Goals',
                    quarter: 'Q1',
                    year: 2022

                }
            ]
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if year  is  not same for all',
        options: {
            'categoryWeightages': [
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Business Result',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Value Add',
                    quarter: 'Q1',
                    year: 2021

                },
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Impact to customer',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Process Adherence',
                    quarter: 'Q1',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 10,
                    category: 'Development Goals',
                    quarter: 'Q1',
                    year: 2022

                }
            ]
        },
        status: 0
    }

    ]
};
