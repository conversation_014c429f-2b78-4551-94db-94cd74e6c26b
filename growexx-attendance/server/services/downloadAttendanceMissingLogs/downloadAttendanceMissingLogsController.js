const DownloadAttendanceMissingLogsService = require('./downloadAttendanceMissingLogsService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for download user attendance missing logs report
 */
class DownloadAttendanceMissingLogsController {

    /**
     * @desc This function is being used to download user attendance missing logs report
     * <AUTHOR>
     * @since 25/03/2021
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async downloadAttendanceMissingLogs (req, res) {
        const data = await DownloadAttendanceMissingLogsService.downloadAttendanceMissingLogs(req, res.locals.user);
        Utils.downloadFile(res, data);
    }
}

module.exports = DownloadAttendanceMissingLogsController; 