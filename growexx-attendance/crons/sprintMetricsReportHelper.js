/* eslint-disable max-len */
/* eslint-disable guard-for-in */
const { Octokit } = require('octokit');
const axios = require('axios');
const moment = require('moment');
const CONSTANTS = require('../server/util/constants');
const CONSOLE_LOGGER = require('../server/util/logger');
const sprintMetrics = require('../server/models/sprintMetrics.model');
const ragModel = require('../server/models/rag.model');
const projects = require('../projectData');

async function downloadSprintMetrics (body) {
    const endDate = body.endDate ?? moment().format('YYYY-MM-DD');
    const startDate =
        body.startDate ??
        goBackExcludingWeekends(9, endDate).format(
            'YYYY-MM-DD'
        );
    const data = await getReportData({ ...body, endDate, startDate });
    const downloadData = getDownloadData(data);
    const dataArrayWithDates = downloadData.map(obj => ({
        ...obj, startDate, endDate, projectName: body.projectName, sprintNumber: body.sprintNumber
    }));
    try {
        for (let i = 0; i < dataArrayWithDates.length; i++) {
            const currentMetric = dataArrayWithDates[i];
            await sprintMetrics.findOneAndUpdate(
                {
                    $and: [
                        { projectName: currentMetric.projectName },
                        { resource: currentMetric.resource },
                        { repo: currentMetric.repo },
                        { sprintNumber: currentMetric.sprintNumber }
                    ]
                },
                currentMetric,
                {
                    upsert: true
                }
            );
        }
        CONSOLE_LOGGER.debug('Successful');
    } catch (error) {
        CONSOLE_LOGGER.debug('Error:', error);
    }
    CONSOLE_LOGGER.debug(
        '======================Completed=============================='
    );
}

function getDownloadData ({ report }) {
    let users = {};
    const projects = [];
    const backend = getFinalReport(report, users);
    users = backend.users;
    projects.push(...backend.projects);

    const data = Object.values(users).map((row) => {
        const prEfficiency = parseFloat(
            (
                ((row.prCount - row.rejectedPrCount) / row.prCount || 1) * 100
            ).toFixed(2)
        );
        const swaggerRating =
            row.apiCreated !== CONSTANTS.NOT_AVAILABLE_TEXT
                ? ((row.availableAPI / (row.apiCreated || 1)) * 100).toFixed(2)
                : CONSTANTS.NOT_AVAILABLE_TEXT;
        const prRating = getPRrating(prEfficiency.toFixed(0));
        const sonarRating = getSonarRating(row);
        const coverageRating = getCoverageRating(row.coverage);
        const swaggerFinalRating =
            getSwaggerRatings(swaggerRating);
        const sprintAverage = getSprintAverageRating([
            prRating,
            sonarRating,
            coverageRating,
            swaggerFinalRating
        ]);
        return {
            ...row,
            prEfficiency,
            swaggerRating,
            prRating,
            sonarRating,
            swaggerFinalRating,
            coverageRating,
            sprintAverage
        };
    });
    return [...data, ...projects].sort((first, second) => {
        if (
            first[CONSTANTS.SORTING.FIRST_PRIORITY].toLowerCase() >
            second[CONSTANTS.SORTING.FIRST_PRIORITY].toLowerCase()
        ) {
            return 1;
        } else if (
            first[CONSTANTS.SORTING.FIRST_PRIORITY].toLowerCase() <
            second[CONSTANTS.SORTING.FIRST_PRIORITY].toLowerCase()
        ) {
            return -1;
        } else if (
            first[CONSTANTS.SORTING.SECOND_PRIORITY] >
            second[CONSTANTS.SORTING.SECOND_PRIORITY]
        ) {
            return 1;
        } else if (
            first[CONSTANTS.SORTING.SECOND_PRIORITY] <
            second[CONSTANTS.SORTING.SECOND_PRIORITY]
        ) {
            return -1;
        } else if (
            first[CONSTANTS.SORTING.THIRD_PRIORITY] >
            second[CONSTANTS.SORTING.THIRD_PRIORITY]
        ) {
            return -1;
        } else if (
            first[CONSTANTS.SORTING.THIRD_PRIORITY] <
            second[CONSTANTS.SORTING.THIRD_PRIORITY]
        ) {
            return 1;
        } else {
            return 0;
        }
    });
}

function getFinalReport (report, users) {
    const projects = [];
    for (const project in report) {
        for (const user in report[project].users) {
            const totalUsers = Object.keys(report[project].users).length;
            projects.push({
                repo: project,
                resource: user,
                prCount: report[project].users[user].totalPRs,
                rejectedPrCount: report[project].users[user].rejectedPRs,
                apiCreated:
                    report[project].users[user].swaggerAPICreated ===
                        CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : report[project].users[user].swaggerAPICreated,
                availableAPI:
                    report[project].users[user].swaggerAPICreatedAvailable ===
                        CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : report[project].users[user].swaggerAPICreatedAvailable,
                codeSmell:
                    report[project].sonar.codeSmell === CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : parseFloat(
                            (report[project].sonar.codeSmell / totalUsers).toFixed(2)
                        ),
                vulnerabilities:
                    report[project].sonar.vulnerability === CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : parseFloat(
                            (report[project].sonar.vulnerability / totalUsers).toFixed(2)
                        ),
                bugs:
                    report[project].sonar.bugs === CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : parseFloat(
                            (report[project].sonar.bugs / totalUsers).toFixed(2)
                        ),
                coverage:
                    report[project].sonar.coverage === CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : parseFloat(report[project].sonar.coverage.toFixed(2)),
                duplication:
                    report[project].sonar.duplication === CONSTANTS.NOT_AVAILABLE_TEXT
                        ? CONSTANTS.NOT_AVAILABLE_TEXT
                        : parseFloat(report[project].sonar.duplication.toFixed(2))
            });
            if (users[user]) {
                users[user] = {
                    repo: users[user].repo,
                    resource: user,
                    prCount: users[user].prCount + report[project].users[user].totalPRs,
                    rejectedPrCount:
                        users[user].rejectedPrCount +
                        report[project].users[user].rejectedPRs,
                    apiCreated: getFilteredSum(
                        users[user].apiCreated,
                        report[project].users[user].swaggerAPICreated
                    ),
                    availableAPI: getFilteredSum(
                        users[user].availableAPI,
                        report[project].users[user].swaggerAPICreatedAvailable
                    ),
                    codeSmell: getFilteredSum(
                        users[user].codeSmell,
                        parseFloat(report[project].sonar.codeSmell / totalUsers)
                    ),
                    vulnerabilities: getFilteredSum(
                        users[user].vulnerabilities,
                        parseFloat(report[project].sonar.vulnerability / totalUsers)
                    ),
                    bugs: getFilteredSum(
                        users[user].bugs,
                        parseFloat(report[project].sonar.bugs / totalUsers)
                    ),
                    coverage: getFilteredAverage(
                        users[user].coverage,
                        report[project].sonar.coverage,
                        users[user].applicableCoverageCount
                    ),
                    duplication: getFilteredAverage(
                        users[user].duplication,
                        report[project].sonar.duplication,
                        users[user].applicableDuplicationCount
                    ),
                    noOfRepos: users[user].noOfRepos + 1,
                    applicableCoverageCount:
                        report[project].sonar.coverage === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? users[user].applicableCoverageCount
                            : users[user].applicableCoverageCount + 1,
                    applicableDuplicationCount:
                        report[project].sonar.duplication === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? users[user].applicableDuplicationCount
                            : users[user].applicableDuplicationCount + 1
                };
            } else {
                users[user] = {
                    repo: 'Overall',
                    resource: user,
                    prCount: report[project].users[user].totalPRs,
                    rejectedPrCount: report[project].users[user].rejectedPRs,
                    apiCreated:
                        report[project].users[user].swaggerAPICreated ===
                            CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : report[project].users[user].swaggerAPICreated,
                    availableAPI:
                        report[project].users[user].swaggerAPICreatedAvailable ===
                            CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : report[project].users[user].swaggerAPICreatedAvailable,
                    codeSmell:
                        report[project].sonar.codeSmell === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : parseFloat(
                                (report[project].sonar.codeSmell / totalUsers).toFixed(2)
                            ),
                    vulnerabilities:
                        report[project].sonar.vulnerability ===
                            CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : parseFloat(
                                (report[project].sonar.vulnerability / totalUsers).toFixed(
                                    2
                                )
                            ),
                    bugs:
                        report[project].sonar.bugs === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : parseFloat(
                                (report[project].sonar.bugs / totalUsers).toFixed(2)
                            ),
                    coverage:
                        report[project].sonar.coverage === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : parseFloat(report[project].sonar.coverage.toFixed(2)),
                    duplication:
                        report[project].sonar.duplication === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? CONSTANTS.NOT_AVAILABLE_TEXT
                            : parseFloat(report[project].sonar.duplication.toFixed(2)),
                    noOfRepos: 1,
                    applicableCoverageCount:
                        report[project].sonar.coverage === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? 0
                            : 1,
                    applicableDuplicationCount:
                        report[project].sonar.duplication === CONSTANTS.NOT_AVAILABLE_TEXT
                            ? 0
                            : 1
                };
            }
        }
    }
    return {
        users,
        projects
    };
}

function getFilteredSum (first, second) {
    if (
        first === CONSTANTS.NOT_AVAILABLE_TEXT &&
        second === CONSTANTS.NOT_AVAILABLE_TEXT
    ) {
        return CONSTANTS.NOT_AVAILABLE_TEXT;
    } else if (second === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return first;
    } else if (first === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return second;
    } else {
        return parseFloat((first + second).toFixed(2));
    }
}

function getFilteredAverage (first, second, count) {
    if (
        first === CONSTANTS.NOT_AVAILABLE_TEXT &&
        second === CONSTANTS.NOT_AVAILABLE_TEXT
    ) {
        return CONSTANTS.NOT_AVAILABLE_TEXT;
    } else if (second === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return first;
    } else if (first === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return second;
    } else {
        return parseFloat(((first * count + second) / (count + 1)).toFixed(2));
    }
}

function getSprintAverageRating (ratings) {
    let applicable = 0;
    let total = 0;
    for (let i = 0; i < ratings.length; i++) {
        if (ratings[i] !== CONSTANTS.NOT_AVAILABLE_TEXT) {
            applicable += 1;
            total += ratings[i];
        }
    }
    return parseFloat((total / applicable).toFixed(2));
}

function getSwaggerRatings (efficiency) {
    if (efficiency === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return efficiency;
    }
    if (efficiency === CONSTANTS.SWAGGER_RATINGS.FIVE) {
        return 5;
    } else if (
        efficiency > CONSTANTS.SWAGGER_RATINGS.FOUR &&
        efficiency < CONSTANTS.SWAGGER_RATINGS.FIVE
    ) {
        return 4;
    } else if (
        efficiency > CONSTANTS.SWAGGER_RATINGS.THREE &&
        efficiency <= CONSTANTS.SWAGGER_RATINGS.FOUR
    ) {
        return 3;
    } else if (
        efficiency > CONSTANTS.SWAGGER_RATINGS.TWO &&
        efficiency <= CONSTANTS.SWAGGER_RATINGS.THREE
    ) {
        return 2;
    } else {
        return 1;
    }
}

function getCoverageRating (coverage) {
    if (coverage === CONSTANTS.NOT_AVAILABLE_TEXT) {
        return coverage;
    } else if (coverage >= CONSTANTS.COVERAGE_RATINGS.FIVE) {
        return 5;
    } else if (
        coverage >= CONSTANTS.COVERAGE_RATINGS.FOUR &&
        coverage < CONSTANTS.COVERAGE_RATINGS.FIVE
    ) {
        return 4;
    } else if (
        coverage >= CONSTANTS.COVERAGE_RATINGS.THREE &&
        coverage < CONSTANTS.COVERAGE_RATINGS.FOUR
    ) {
        return 3;
    } else if (
        coverage >= CONSTANTS.COVERAGE_RATINGS.TWO &&
        coverage < CONSTANTS.COVERAGE_RATINGS.THREE
    ) {
        return 2;
    } else if (
        coverage >= CONSTANTS.COVERAGE_RATINGS.ONE &&
        coverage < CONSTANTS.COVERAGE_RATINGS.TWO
    ) {
        return 1;
    } else {
        return 0;
    }
}
function getSonarRating ({ codeSmell, bugs, duplication, vulnerabilities }) {
    if (
        codeSmell === CONSTANTS.NOT_AVAILABLE_TEXT ||
        bugs === CONSTANTS.NOT_AVAILABLE_TEXT ||
        vulnerabilities === CONSTANTS.NOT_AVAILABLE_TEXT ||
        duplication === CONSTANTS.NOT_AVAILABLE_TEXT
    ) {
        return CONSTANTS.NOT_AVAILABLE_TEXT;
    } else if (
        bugs === 0 &&
        vulnerabilities === 0 &&
        codeSmell === 0 &&
        duplication === 0
    ) {
        return 5;
    } else if (
        bugs === 0 &&
        vulnerabilities === 0 &&
        codeSmell < 5 &&
        duplication < 5
    ) {
        return 4;
    } else if (
        bugs === 0 &&
        vulnerabilities === 0 &&
        codeSmell < 10 &&
        duplication < 10
    ) {
        return 3;
    } else if (
        bugs === 0 &&
        vulnerabilities === 0 &&
        ((codeSmell > 10 && codeSmell < 20) ||
            (duplication > 10 && duplication < 20))
    ) {
        return 2;
    } else {
        return 1;
    }
}
function getPRrating (efficiency) {
    if (efficiency == CONSTANTS.PR_RATINGS.FIVE) {
        return 5;
    } else if (
        efficiency > CONSTANTS.PR_RATINGS.FOUR &&
        efficiency < CONSTANTS.PR_RATINGS.FIVE
    ) {
        return 4;
    } else if (
        efficiency > CONSTANTS.PR_RATINGS.THREE &&
        efficiency <= CONSTANTS.PR_RATINGS.FOUR
    ) {
        return 3;
    } else if (
        efficiency > CONSTANTS.PR_RATINGS.TWO &&
        efficiency <= CONSTANTS.PR_RATINGS.THREE
    ) {
        return 2;
    } else {
        return 1;
    }
}

async function getPRCommits (owner, repo, prNumber, octokit) {
    const user = await octokit.request(
        `GET /repos/${owner}/${repo}/pulls/${prNumber}`
    );
    return { committer: user.data.user.login };
}

async function getPRs (query, page = 1, limit = 100, octokit) {
    const result = [];
    const pullClosedPRs = await octokit.request(
        `GET /search/issues?q=${query}&per_page=${limit}&page=${page}`
    );
    result.push(...pullClosedPRs.data.items);
    if (pullClosedPRs.data.total_count > page * limit) {
        result.push(
            ...(await getPRs(query, page + 1, limit, octokit))
        );
    }
    return result;
}

async function getReportData (data) {
    const { githubToken, repositories } = data;
    const octokit = new Octokit({
        auth: githubToken
    });
    const report = await getRepositoryData(
        repositories,
        data,
        octokit
    );
    return { report };
}
async function getRepositoryData (repository, data, octokit) {
    const report = {};
    const { organization, endDate, startDate } = data;
    for (const repo of repository) {
        const { repository, branch, sonarKey } = repo;
        const isSwaggerApplicable = repo.isSwaggerApplicable ?? false;
        const isSonarApplicable = repo.isSonarApplicable ?? true;
        const isCoverageApplicable = repo.isCoverageApplicable ?? true;
        report[repo.repository] = {};
        report[repo.repository].users = {};
        report[repo.repository].sonar = {
            codeSmell: 0,
            bugs: 0,
            vulnerability: 0,
            coverage: 0,
            duplication: 0
        };
        CONSOLE_LOGGER.debug('Currently processing', repo.repository);
        const queryString = encodeURIComponent(
            `repo:${organization}/${repository} is:pr is:closed merged:${startDate}..${endDate} base:${branch}`
        );
        const pullClosedPRs = await getPRs(
            queryString,
            CONSTANTS.DEFAULT_PR_FETCH_PAGE,
            CONSTANTS.DEFAULT_PR_FETCH_LIMIT,
            octokit
        );
        for (const closedPR of pullClosedPRs) {
            CONSOLE_LOGGER.debug('Current PR no:', closedPR.number);
            const pullReviews = await octokit.request(
                `GET /repos/${organization}/${repository}/pulls/${closedPR.number}/reviews`
            );
            let noOfAPICreated = 0;
            let noOfAPIAvailable = 0;
            let totalPrs = 1;
            let reject = 0;
            if (pullReviews.data.length === 0) {
                CONSOLE_LOGGER.debug('                        ', 'No Records Found');
            }
            for (const review of pullReviews.data) {
                CONSOLE_LOGGER.debug('                        ', review.state);
                if (review.state === 'CHANGES_REQUESTED') {
                    reject++;
                    totalPrs++;
                }
                if (review.body !== '') {
                    if (review.body.indexOf('API_CREATED') > -1) {
                        noOfAPICreated = parseInt(review.body.split('##')[1]);
                    }
                    if (review.body.indexOf('API_SWAGGER') > -1) {
                        noOfAPIAvailable = parseInt(review.body.split('##')[3]);
                    }
                }
            }
            const { committer } = await getPRCommits(
                organization,
                repository,
                closedPR.number,
                octokit
            );
            CONSOLE_LOGGER.debug('                        ', committer);
            if (report[repo.repository].users[committer]) {
                report[repo.repository].users[committer].totalPRs += totalPrs || 1;
                report[repo.repository].users[committer].rejectedPRs += reject;

                if (isSwaggerApplicable) {
                    report[repo.repository].users[committer].swaggerAPICreated +=
                        noOfAPICreated;
                    report[repo.repository].users[
                        committer
                    ].swaggerAPICreatedAvailable += noOfAPIAvailable;
                }
            } else {
                report[repo.repository].users[committer] = {
                    user: committer,
                    totalPRs: totalPrs || 1,
                    rejectedPRs: reject,
                    swaggerAPICreated: isSwaggerApplicable
                        ? noOfAPICreated
                        : CONSTANTS.NOT_AVAILABLE_TEXT,
                    swaggerAPICreatedAvailable: isSwaggerApplicable
                        ? noOfAPIAvailable
                        : CONSTANTS.NOT_AVAILABLE_TEXT
                };
            }
        }
        let sonar = {};
        if (Array.isArray(repo.sonarKey)) {
            let multiSonarData = {};
        
            for (let key of repo.sonarKey) {
                let resData = await getSonarReport(
                    data,
                    key,
                    isSonarApplicable,
                    isCoverageApplicable
                );
        
                for (let metric in resData) {
                    if (!multiSonarData[metric]) {
                        multiSonarData[metric] = resData[metric]; // Initialize if key is not present
                    } else {
                        multiSonarData[metric] += resData[metric]; // Accumulate values
                    }
                }
            }
            sonar = multiSonarData;
        }
        else if (typeof repo.sonarKey === "string") {
          sonar = await getSonarReport(
            data,
            sonarKey,
            isSonarApplicable,
            isCoverageApplicable
          );
        }
        // const sonar = await getSonarReport(
        //     data,
        //     sonarKey,
        //     isSonarApplicable,
        //     isCoverageApplicable
        // );
        report[repo.repository].sonar.codeSmell = sonar?.codeSmell;
        report[repo.repository].sonar.bugs = sonar?.bugs;
        report[repo.repository].sonar.vulnerability = sonar?.vulnerability;
        report[repo.repository].sonar.coverage = sonar?.coverage;
        report[repo.repository].sonar.duplication = sonar?.duplications;
    }
    return report;
}

async function getSonarReport (
    data,
    sonarKey,
    isSonarApplicable = true,
    isCoverageApplicable = true
) {
    const { sonarToken, startDate, endDate } = data;
    if (!isSonarApplicable && isCoverageApplicable) {
        const { coverage } = await getCoverageAndDuplication(
            sonarKey,
            startDate,
            endDate,
            sonarToken
        );
        return {
            coverage,
            duplications: CONSTANTS.NOT_AVAILABLE_TEXT,
            codeSmell: CONSTANTS.NOT_AVAILABLE_TEXT,
            vulnerability: CONSTANTS.NOT_AVAILABLE_TEXT,
            bugs: CONSTANTS.NOT_AVAILABLE_TEXT
        };
    } else if (!isSonarApplicable && !isCoverageApplicable) {
        return {
            coverage: CONSTANTS.NOT_AVAILABLE_TEXT,
            duplications: CONSTANTS.NOT_AVAILABLE_TEXT,
            codeSmell: CONSTANTS.NOT_AVAILABLE_TEXT,
            vulnerability: CONSTANTS.NOT_AVAILABLE_TEXT,
            bugs: CONSTANTS.NOT_AVAILABLE_TEXT
        };
    } else {
        const coverageAndDuplication =
            await getCoverageAndDuplication(
                sonarKey,
                startDate,
                endDate,
                sonarToken
            );
        const issues = await getSonarIssues(
            sonarKey,
            startDate,
            endDate,
            sonarToken
        );
        return {
            ...coverageAndDuplication,
            ...issues,
            coverage: isCoverageApplicable
                ? coverageAndDuplication.coverage
                : CONSTANTS.NOT_AVAILABLE_TEXT
        };
    }
}

async function getCoverageAndDuplication (
    sonarKey,
    startDate,
    endDate,
    sonarToken
) {
    const sonar = {};
    const params = {
        component: sonarKey,
        metrics: 'coverage,duplicated_lines_density',
        from: startDate,
        to: endDate
    };
    const reports = await axios.get(CONSTANTS.SONAR_COVERAGE_URL, {
        params,
        auth: {
            username: sonarToken,
            password: ''
        }
    });
    for (const report of reports.data.measures) {
        if (report.metric === 'coverage') {
            sonar.coverage =
                report.history.length > 0
                    ? parseFloat(report.history[report.history.length - 1].value ?? 0)
                    : 0;
        } else if (report.metric === 'duplicated_lines_density') {
            sonar.duplications =
                report.history.length > 0
                    ? parseFloat(report.history[report.history.length - 1].value ?? 0)
                    : 0;
        }
    }
    return sonar;
}

async function getSonarIssues (sonarKey, startDate, endDate, sonarToken) {
    const sonar = {
        codeSmell: 0,
        vulnerability: 0,
        bugs: 0
    };
    const smellParams = {
        types: 'VULNERABILITY,CODE_SMELL,BUG',
        createdAfter: MOMENT(startDate).format('YYYY-MM-DD'),
        createdBefore: MOMENT(endDate).add(1, 'day').format('YYYY-MM-DD'),
        componentKeys: sonarKey,
        statuses: 'OPEN'
    };
    const codeSmell = await axios.get(
        `${CONSTANTS.SONAR_SEARCH_ISSUES_URL}?types=${smellParams.types}&createdAfter=${smellParams.createdAfter}&createdBefore=${smellParams.createdBefore}&componentKeys=${smellParams.componentKeys}&statuses=${smellParams.statuses}`,
        {
            auth: {
                username: sonarToken,
                password: ''
            }
        }
    );
    for (const { type } of codeSmell.data.issues) {
        if (type === 'CODE_SMELL') {
            sonar.codeSmell = sonar.codeSmell + 1;
        } else if (type === 'VULNERABILITY') {
            sonar.vulnerability = sonar.vulnerability + 1;
        } else if (type === 'BUG') {
            sonar.bugs = sonar.bugs + 1;
        }
    }
    return sonar;
}

async function getSprintMetrics (syncAllData, projectName) {
    let rags = [];
    const projectParams = {};
    if (projectName) {
        projectParams.project = projectName;
    }
    if (syncAllData === 'all') {
        rags = await ragModel.find({ ...projectParams });
    } else {
        rags = await ragModel.aggregate([
            {
                $match: { ...projectParams }
            },
            {
                $group: {
                    _id: {
                        project: '$project',
                        boardId: '$boardId'
                    },
                    latestSprintEndDate: { $max: '$sprintEnd' },
                    rag: { $max: '$$ROOT' }
                }
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: [
                            '$rag',
                            { latestSprintEndDate: '$latestSprintEndDate' }
                        ]
                    }
                }
            }
        ]);
    }

    for (let i = 0; i < rags.length; i++) {
        const rag = rags[i];
        const projectName = rag.project;
        const data = projects[projectName];
        if (data) {
            data.endDate = MOMENT(rag.sprintEnd).format('YYYY-MM-DD');
            data.sprintNumber = rag.sprintNumber;
            await downloadSprintMetrics(data);
        }
    }
}

function goBackExcludingWeekends (days, date) {
    let currentDate = date ? moment(date) : moment();
    for (let i = 0; i < days; i++) {
        currentDate = currentDate.subtract(1, 'day');
        if (currentDate.day() === 6) {
            currentDate = currentDate.subtract(1, 'day');
        } else if (currentDate.day() === 0) {
            currentDate = currentDate.subtract(2, 'day');
        }
    }
    return currentDate;
}
module.exports = getSprintMetrics;
