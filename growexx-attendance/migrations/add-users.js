// const pass = 'd1e7e544d77aeca847d93b701cfea44d:dace4ac5d07245eb29114a3f9f17e6efcd1ae0caeaf7fdf88bee36fa111d1fb78f0fdae1b802095719e6eef683926c47571a0c16389078692c67d393757fe178a2e98d0cd194e359e6766da5f452ff5b';
const pass = '1e879ae4d53496184ed3ce2ec1f1d0f4:8249253cdbbe7462afccd5a0cb3b42dcfb9ede391da36e2af7e3739ba34c77b7d25a37806b4b930affd846c6ca7b981b0a4c20c6e516e2c85ceb27e040a285bec3026bdcb8c68f18907be1bf23f30c00';
module.exports = {
    async up (db) {
        await db.collection('users').insertMany([{
            employeeId: 5,
            firstName: 'Rahul',
            lastName: 'Ranva',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '8140958759',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'rahul.ranva',
            role: 1
        },
        {
            employeeId: 6,
            firstName: 'Harshad',
            lastName: 'Parmar',
            level: 'L3',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '7600331723',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'harshad.parmar',
            role: 1
        },
        {
            employeeId: 8,
            firstName: '<PERSON><PERSON><PERSON>',
            lastName: 'Babaria',
            level: 'L2',
            department: 'ANGULAR',
            countryCode: '+91',
            phoneNumber: '9586187555',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'krunal.babaria',
            role: 1
        },
        {
            employeeId: 9,
            firstName: 'Milan',
            lastName: 'Patel',
            level: 'L3',
            department: 'ANGULAR',
            countryCode: '+91',
            phoneNumber: '9998241243',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'milan.patel',
            role: 1
        },
        {
            employeeId: 10,
            firstName: 'Kapil',
            lastName: 'Jain',
            level: 'L2',
            department: 'QA-Automation',
            countryCode: '+91',
            phoneNumber: '8511636167',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'kapil.jain',
            role: 1
        },
        {
            employeeId: 11,
            firstName: 'Rahul',
            lastName: 'Solanki',
            level: 'L2',
            department: 'ANGULAR',
            countryCode: '+91',
            phoneNumber: '9714448925',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'rahul.solanki',
            role: 1
        },
        {
            employeeId: 14,
            firstName: 'Shivram',
            lastName: 'Bagesariya',
            level: 'L2',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '7383380281',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'shivram.bagesariya',
            role: 1
        },
        {
            employeeId: 15,
            firstName: 'Parth',
            lastName: 'Vora',
            level: 'L3',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '9974048966',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'parth.vora',
            role: 1
        },
        {
            employeeId: 16,
            firstName: 'Milan',
            lastName: 'Manvar',
            level: 'L2',
            department: 'ANDROID',
            countryCode: '+91',
            phoneNumber: '9427722192',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'milan.manvar',
            role: 1
        },
        {
            employeeId: 17,
            firstName: 'Nisha',
            lastName: 'Rani',
            level: 'L2',
            department: 'IOS',
            countryCode: '+91',
            phoneNumber: '7014920071',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'nisha.rani',
            role: 1
        },
        {
            employeeId: 18,
            firstName: 'Krunal',
            lastName: 'Palaniya',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '8401737093',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'krunal.palaniya',
            role: 1
        },
        {
            employeeId: 19,
            firstName: 'Anshul',
            lastName: 'Kumar',
            level: 'L3',
            department: 'UI/UX',
            countryCode: '+91',
            phoneNumber: '8980750415',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'anshul.kumar',
            role: 1
        },
        {
            employeeId: 20,
            firstName: 'Hitesh',
            lastName: 'Parikh',
            level: 'L4',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '9925461330',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'hitesh.parikh',
            role: 1
        },
        {
            employeeId: 21,
            firstName: 'Sadique',
            lastName: 'Waris',
            level: 'L2',
            department: 'DEVOPS',
            countryCode: '+91',
            phoneNumber: '7507842874',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'sadique.waris',
            role: 1
        },
        {
            employeeId: 22,
            firstName: 'Maulik',
            lastName: 'Patel',
            level: 'L2',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '9537527578',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'maulik.patel',
            role: 1
        },
        {
            employeeId: 23,
            firstName: 'Juhi',
            lastName: 'Soni',
            level: 'L1',
            department: 'ANGULAR',
            countryCode: '+91',
            phoneNumber: '7874308494',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'juhi.soni',
            role: 1
        },
        {
            employeeId: 24,
            firstName: 'Mohit',
            lastName: 'Niwadunge',
            level: 'L2',
            department: 'ANGULAR',
            countryCode: '+91',
            phoneNumber: '7000526216',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'mohit.niwadunge',
            role: 1
        },
        {
            employeeId: 25,
            firstName: 'Jaydip',
            lastName: 'Maniyar',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '9974110472',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'jaydip.maniyar',
            role: 1
        },
        {
            employeeId: 26,
            firstName: 'Janakirao',
            lastName: 'Varanasi',
            level: 'L4',
            department: 'ADM',
            countryCode: '+91',
            phoneNumber: '9974384897',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'janaki@growexx',
            role: 1
        },
        {
            employeeId: 27,
            firstName: 'Neel',
            lastName: 'Khambhayata',
            level: 'L1',
            department: 'UI/UX',
            countryCode: '+91',
            phoneNumber: '8141639191',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'neel.khambhayata',
            role: 1
        },
        {
            employeeId: 28,
            firstName: 'Jaimin',
            lastName: 'Patel',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '9033522946',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'jaimin.patel',
            role: 1
        },
        {
            employeeId: 29,
            firstName: 'Aman',
            lastName: 'Mishra',
            level: 'L1',
            department: 'DEVOPS',
            countryCode: '+91',
            phoneNumber: '8140310899',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'aman.mishra',
            role: 1
        },
        {
            employeeId: 30,
            firstName: 'Manali',
            lastName: 'Sharma',
            level: 'L1',
            department: 'QA-Automation',
            countryCode: '+91',
            phoneNumber: '8469867568',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'manali.sharma',
            role: 1
        },
        {
            employeeId: 31,
            firstName: 'Riya',
            lastName: 'Mansuri',
            level: 'L1',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '9512144663',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'riya.mansuri',
            role: 1
        },
        {
            employeeId: 32,
            firstName: 'Payal',
            lastName: 'Chauhan',
            level: 'L1',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '8460205853',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'payal.chauhan',
            role: 1
        },
        {
            employeeId: 33,
            firstName: 'Akash',
            lastName: 'Rathod',
            level: 'L1',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '9714876196',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'akash.rathod',
            role: 1
        },
        {
            employeeId: 34,
            firstName: 'Puneet',
            lastName: 'Pandey',
            level: 'L2',
            department: 'PO',
            countryCode: '+91',
            phoneNumber: '7046336162',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'puneet.pandey',
            role: 1
        },
        {
            employeeId: 35,
            firstName: 'Siddharajsinh',
            lastName: 'Zala',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '8347676729',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'siddharajsinh.zala',
            role: 1
        },
        {
            employeeId: 36,
            firstName: 'Arima',
            lastName: 'Sharma',
            level: 'L2',
            department: 'QA-Automation',
            countryCode: '+91',
            phoneNumber: '84603 41335',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'arima.sharma',
            role: 1
        },
        {
            employeeId: 37,
            firstName: 'Kirankumar',
            lastName: 'Patel',
            level: 'L2',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '7874444601',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'kirankumar.patel',
            role: 1
        },
        {
            employeeId: 39,
            firstName: 'Prince',
            lastName: 'Dholiya',
            level: 'L0',
            department: 'REACTNATIVE',
            countryCode: '+91',
            phoneNumber: '7433074500',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'prince.dholiya',
            role: 1
        },
        {
            employeeId: 40,
            firstName: 'Mahima',
            lastName: 'Gajiwala',
            level: 'L0',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '9913705414',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'mahima.gajiwala',
            role: 1
        },
        {
            employeeId: 41,
            firstName: 'Parita',
            lastName: 'Dudhani',
            level: 'L0',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '9601074626',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'parita.dudhani',
            role: 1
        },
        {
            employeeId: 42,
            firstName: 'Avkash',
            lastName: 'Chevli',
            level: 'L0',
            department: 'REACT',
            countryCode: '+91',
            phoneNumber: '9376798914',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'avkash.chevli',
            role: 1
        },
        {
            employeeId: 43,
            firstName: 'Pratibha',
            lastName: 'Hotwani',
            level: 'L0',
            department: 'REACT',
            countryCode: '+91',
            phoneNumber: '8758508742',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'pratibha.hotwani',
            role: 1
        },
        {
            employeeId: 45,
            firstName: 'Shuchi',
            lastName: 'Sheth',
            level: 'L1',
            department: 'ANDROID',
            countryCode: '+91',
            phoneNumber: '8511819200',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'shuchi.sheth',
            role: 1
        },
        {
            employeeId: 46,
            firstName: 'Jaydeep',
            lastName: 'Dosi',
            level: 'L5',
            department: 'ADM',
            countryCode: '+91',
            phoneNumber: '98876 55777',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'jaydeep@growexx',
            role: 1
        },
        {
            employeeId: 48,
            firstName: 'Shraddha',
            lastName: 'Sojitra',
            level: 'L1',
            department: 'IOS',
            countryCode: '+91',
            phoneNumber: '9998071502',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'shraddha.sojitra',
            role: 1
        },
        {
            employeeId: 49,
            firstName: 'Charandas',
            lastName: 'Chatla',
            level: 'L2',
            department: 'PO',
            countryCode: '+91',
            phoneNumber: '9167282820',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'charandas.chatla',
            role: 1
        },
        {
            employeeId: 50,
            firstName: 'Parth',
            lastName: 'Palkhiwala',
            level: 'L3',
            department: 'MAGENTO',
            countryCode: '+91',
            phoneNumber: '9510022896',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'parth.palkhiwala',
            role: 1
        },
        {
            employeeId: 53,
            firstName: 'Ravi',
            lastName: 'Shrimali',
            level: 'L1',
            department: 'LAMP',
            countryCode: '+91',
            phoneNumber: '8460000689',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'ravi.shrimali',
            role: 1
        },
        {
            employeeId: 86,
            firstName: 'Ajay',
            lastName: 'Dharsandia',
            level: 'L1',
            department: 'NODE',
            countryCode: '+91',
            phoneNumber: '9574297224',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'ajay.dharsandia',
            role: 1
        },
        {
            employeeId: 90,
            firstName: 'Shivani',
            lastName: 'Mistry',
            level: 'L1',
            department: 'REACT',
            countryCode: '+91',
            phoneNumber: '978465231',
            email: '<EMAIL>',
            password: pass,
            isActive: 1,
            label: 'shivani.mistry',
            role: 1
        }
        ]);
    }
};
