/**
 * @name projectTrackerMetrics model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const metricsSchema = new appMongoose.Schema({
    salesEstimate: {
        type: String,
        default: '0.00',
        trim: true
    },
    spent: {
        type: String,
        default: '0.00',
        trim: true
    },
    toBeSpent: {
        type: String,
        default: '0.00',
        trim: true
    },
    difference: {
        type: String,
        default: '0.00',
        trim: true
    }
});

const deviationSchema = new appMongoose.Schema({
    totalHours: {
        planned: {
            value: {
                type: String,
                default: '0.00',
                trim: true
            },
            percentage: {
                type: String,
                default: '0.00',
                trim: true
            }
        },
        actual: {
            value: {
                type: String,
                default: '0.00',
                trim: true
            },
            percentage: {
                type: String,
                default: '0.00',
                trim: true
            }
        }
    },
    metrics: {
        done: {
            salesEstimate: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            spent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            toBeSpent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            difference: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            }
        },
        inProgress: {
            salesEstimate: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            spent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            toBeSpent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            difference: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            }
        },
        toDo: {
            salesEstimate: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            spent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            toBeSpent: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            },
            difference: {
                value: { type: String, default: '0.00', trim: true },
                percentage: { type: String, default: '0.00', trim: true }
            }
        }
    },
    epicCount: {
        value: { type: String, default: '0', trim: true },
        percentage: { type: String, default: '0', trim: true }
    },
    storyCount: {
        value: { type: String, default: '0', trim: true },
        percentage: { type: String, default: '0', trim: true }
    },
    storiesWithoutEpic: {
        value: { type: String, default: '0', trim: true },
        percentage: { type: String, default: '0', trim: true }
    },
    epicsWithoutSalesEstimate: {
        value: { type: String, default: '0', trim: true },
        percentage: { type: String, default: '0', trim: true }
    },
    bugsWithoutLinkedStories: {
        value: { type: String, default: '0', trim: true },
        percentage: { type: String, default: '0', trim: true }
    }
});

const schema = new appMongoose.Schema({
    projectId: {
        type: appMongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'Project'
    },
    projectName: {
        type: String,
        required: true,
        trim: true
    },
    boardId: {
        type: appMongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'ProjectBoard'
    },
    totalHours: {
        planned: {
            type: String,
            default: '0.00',
            trim: true
        },
        actual: {
            type: String,
            default: '0.00',
            trim: true
        },
        difference: {
            type: String,
            default: '0.00',
            trim: true
        }
    },
    metrics: {
        done: metricsSchema,
        inProgress: metricsSchema,
        toDo: metricsSchema
    },
    epicCount: {
        type: String,
        default: '0',
        trim: true
    },
    storyCount: {
        type: String,
        default: '0',
        trim: true
    },
    storiesWithoutEpic: {
        type: String,
        default: '0',
        trim: true
    },
    epicsWithoutSalesEstimate: {
        type: String,
        default: '0',
        trim: true
    },
    bugsWithoutLinkedStories: {
        type: String,
        default: '0',
        trim: true
    },
    deviations: {
        type: deviationSchema,
        default: () => ({})
    }
}, {
    timestamps: true
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('ProjectTrackerMetrics', schema);
