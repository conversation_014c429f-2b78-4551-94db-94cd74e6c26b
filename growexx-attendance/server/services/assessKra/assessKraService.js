const UserKRA = require('../../models/userKra.model');
const User = require('../../models/user.model');
const AssessKraValidator = require('./assessKraValidator');
const GeneralError = require('../../util/GeneralError');
const Email = require('../../util/sendEmail');
const mongoose = require('mongoose');
const Notification = require('../../models/notification.model');
/**
 * Class represents services for assess a kra.
 */
class AssessKraService {

    /**
     * @desc This function is being used to assess a kra
     * <AUTHOR>
     * @since 12/07/2022
     * @param {Object} req Request
     */
    static async startKRAAssessment (req, locale) {
        const Validator = new AssessKraValidator(req.body, locale);
        Validator.validationAssessKra();
        const { userKraId, selfAssessmentEndDate,
            reviewManagerAssessmentEndDate, reportingManagerAssessmentEndDate } = req.body;
        const userKraData = await UserKRA.find( {
            '_id': { $in: userKraId }
        });
        if (!userKraData.length) {
            throw new GeneralError(locale('KRA_NOT_FOUND_FOR_QUARTER'), 400);
        }
        const subject = `Growexx: Self-Assessment Form for ${userKraData[0].quarter} ${userKraData[0].year}-${userKraData[0].year - 1999}`;
        const template = 'emailTemplates/assessmentStarted.html';
        const appUrl = process.env.FRONTEND_URL;

        const templateVariables = {
            dueDate: MOMENT(selfAssessmentEndDate).format('ll'),
            appUrl
        };

        for (let i = 0; i < userKraData.length; i++) {
            templateVariables.actionURL = `${appUrl}/self-kra/rate/user-kra?userKRAId=${userKraData[i]._id}`;
            const userData = await User.findOne({ _id: mongoose.Types.ObjectId(userKraData[i].userId) }).lean();
            const userName = `${userData.firstName} ${userData.lastName}`;
            await Notification.updateOne({ userId: userData._id }, { message: locale('ASSESSMENT_STARTED') }, { upsert: true });
            Email.prepareAndSendEmail([userData.email], subject, template, { ...templateVariables, userName }).then();
        }

        return await UserKRA.updateMany({
            '_id': { $in: userKraId }
        },
        {
            $set: {
                selfAssessmentEndDate: MOMENT(selfAssessmentEndDate).format('YYYY-MM-DD'),
                reviewManagerAssessmentEndDate: MOMENT(reviewManagerAssessmentEndDate).format('YYYY-MM-DD'),
                reportingManagerAssessmentEndDate: MOMENT(reportingManagerAssessmentEndDate).format('YYYY-MM-DD'),
                status: CONSTANTS.KRA_STATUS.ASSESSMENT_STARTED
            }
        });
    }
}

module.exports = AssessKraService;
