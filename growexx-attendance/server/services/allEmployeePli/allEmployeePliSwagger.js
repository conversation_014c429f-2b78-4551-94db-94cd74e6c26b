const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/pli/all-employee-profile'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: ['PLI'],
            description: 'Get all employees PLI data for admin view',
            summary: 'Get all employees PLI data',
            responses: {
                200: {
                    description: 'PLI data for all employees retrieved successfully.',
                    schema: {
                        $ref: '#/definitions/successAllEmployeePliResponse'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                }
            }
        }
    };

    // Define the response schema
    swaggerJson.definitions.successAllEmployeePliResponse = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'array',
                items: {
                    $ref: '#/definitions/employeePliData'
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.employeePliData = {
        type: 'object',
        properties: {
            menteeId: {
                type: 'string',
                example: '507f1f77bcf86cd799439011'
            },
            name: {
                type: 'string',
                example: 'John Doe'
            },
            employeeId: {
                type: 'string',
                example: 'EMP001'
            },
            department: {
                type: 'string',
                example: 'Engineering'
            },
            reporteeId: {
                type: 'string',
                example: ''
            },
            status: {
                type: 'string',
                example: 'In Progress',
                description: 'PLI status (Not Started, In Progress, Completed, etc.)'
            },
            finalScore: {
                type: 'number',
                example: 4.2,
                description: 'Final PLI score for the employee'
            },
            pliCycle: {
                type: 'string',
                example: 'January',
                description: 'Month of PLI cycle'
            }
        }
    };

    return swaggerJson;
};
