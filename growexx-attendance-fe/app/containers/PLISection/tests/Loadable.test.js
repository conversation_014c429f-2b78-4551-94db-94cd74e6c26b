/**
 * Test for PLISection Loadable component
 */

import React from 'react';
import { render } from 'react-testing-library';

// Mock the loadable utility to return a component that can be rendered
jest.mock('utils/loadable', () => {
  const mockComponent = () => (
    <div data-testid="loadable-component">Loadable Component</div>
  );
  return () => mockComponent;
});

// Mock the PLIRedirect component
jest.mock('../PLIRedirect', () => () => (
  <div data-testid="pli-redirect">PLI Redirect Component</div>
));

describe('PLISection Loadable', () => {
  // Import after the mocks are set up
  let PLIRedirect;
  let DefaultExport;

  beforeEach(async () => {
    // Clear module cache to ensure fresh imports with mocks applied
    jest.resetModules();
    // Import the module after mocks are set up using dynamic import
    const loadableModule = await import('../Loadable');
    ({ PLIRedirect, default: DefaultExport } = loadableModule);
  });

  it('should render without crashing', () => {
    const { container } = render(<PLIRedirect />);
    expect(container).toBeDefined();
  });

  it('should render a loadable component', () => {
    const { getByTestId } = render(<PLIRedirect />);
    expect(getByTestId('loadable-component')).toBeDefined();
    expect(getByTestId('loadable-component').textContent).toBe(
      'Loadable Component',
    );
  });

  it('should export PLIRedirect as default', () => {
    // Verify that default export is the same as PLIRedirect
    expect(DefaultExport).toBe(PLIRedirect);

    // Render the default export
    const { getByTestId } = render(<DefaultExport />);
    expect(getByTestId('loadable-component')).toBeDefined();
  });
});
