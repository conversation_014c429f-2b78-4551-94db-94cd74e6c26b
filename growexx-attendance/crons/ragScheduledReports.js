const { transports } = require('winston');
const dotenv = require('dotenv');
// models
const RagService = require('../server/services/getRag/ragService');
const RunningSprint = require('../server/models/runningSprint.model');
// utils
const { DAYS_MAPPING } = require('../server/util/constants');
// -------------------

const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [new transports.Console({})];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME }));
}

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(
        process.env.DB_PASSWORD
    )}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};

const isWeekend = (momentDate) =>
    momentDate.day() === DAYS_MAPPING.Saturday ||
  momentDate.day() === DAYS_MAPPING.Sunday;

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        console.info('MongoDB connection error.', err);
        return false;
    }
};

const generateScheduledRagReports = async () => {
    console.info(`==== CRON STARTED AT: ${MOMENT()} ==== `);
    /* Restricting this function to run on Weekends */
    if (isWeekend(MOMENT())) {
        return;
    }


    const twoDaysBeforeMoment = MOMENT().subtract(2, 'day');
    console.info(
        'generateScheduledRagReports ~ twoDaysBeforeMoment:',
        twoDaysBeforeMoment.format('DD-MM-YYYY')
    );

    if (isWeekend(twoDaysBeforeMoment)) {
        while (twoDaysBeforeMoment.day() !== DAYS_MAPPING.Friday) {
            twoDaysBeforeMoment.subtract(1, 'days');
        }
    }
    console.info(
        'generateScheduledRagReports ~ twoDaysBeforeMoment:After Subtractions',
        twoDaysBeforeMoment.format('DD-MM-YYYY')
    );

    const records = await RunningSprint.find({
        endDate: {
            $gte: twoDaysBeforeMoment.startOf('day').toDate(),
            $lte: twoDaysBeforeMoment.endOf('day').toDate()
        }
    });

    if (!records.length) return;

    /* storing running-sprints mongoIds to delete after successful rag report */
    const runningSprintsMongoIds = [];
    await Promise.allSettled(
        records.map(async (item) => {
            const payload = {
                query: {
                    project: item.projectName,
                    start: twoDaysBeforeMoment.startOf('day').toDate(),
                    end: twoDaysBeforeMoment.endOf('day').toDate()
                }
            };
            const response = await RagService.getRagReport(payload);
            if (response.length) {
                runningSprintsMongoIds.push(item._id);
            }
        })
    );

    if (runningSprintsMongoIds.length) {
        CONSOLE_LOGGER.info(
            'generateScheduledRagReports ~ runningSprintsMongoIds:',
            runningSprintsMongoIds
        );
        await RunningSprint.deleteMany({ _id: { $in: runningSprintsMongoIds } });
    }
    console.info(`==== CRON ENDED at: ${MOMENT()} ====`);
};
const main = async () => {
    await connectDB();
    console.info('==== GENERATING REPORTS FOR RECENT SPRINTS ====');
    await generateScheduledRagReports();
    process.exit();
};

main();

module.exports = generateScheduledRagReports;
