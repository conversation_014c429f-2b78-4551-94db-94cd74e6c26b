const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.paths['/project-tracker/download-report'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Download Report'
            ],
            description: 'Get project tracker and Sprint report data.',
            summary: 'Get project tracker and sprint report data.',
            parameters: [{
                in: 'query',
                name: 'boardId',
                description: 'Search by board id'
            }],
            responses: {
                200: {
                    description: 'Sucess',
                    schema: {
                        $ref: '#/definitions/successGetProjectTrackerSheet'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.successGetProjectTrackerSheet = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    'status': 1,
                    'data': {
                        'sprintReport': [
                            {
                                'deviation': 106.75,
                                'sprintStartDate': '2023-04-10T05:30:23.109Z',
                                'sprintEndDate': '2023-04-21T17:30:00.000Z',
                                'totalOriginalEstimate': 0,
                                'totalLoggedEfforts': 106.75,
                                'sprintName': 'Sprint 15',
                                'salesDeviation': -66.75,
                                'developerOriginalEstimate': 0,
                                'developerLoggedEfforts': 91.25,
                                'scrumRitualsOriginalEstimate': 0,
                                'scrumRitualsLoggedEfforts': 15.5,
                                'poOriginalEstimate': 0,
                                'poLoggedEfforts': 0,
                                'tpmOriginalEstimate': 0,
                                'tpmLoggedEfforts': 0,
                                'devopsOriginalEstimate': 0,
                                'devopsLoggedEfforts': 0
                            }
                        ],
                        'projectTracker': [
                            {
                                '_id': '656060f36373241a63a8d766',
                                'createdAt': '2023-11-24T08:38:11.664Z',
                                'epicDeviation': -55,
                                'extractedStoryNumber': 52,
                                'epicName': 'Test Epic 1',
                                'userStory': 'Story 3',
                                'functionalFlow': '',
                                'storyNumber': 'WA-52',
                                'storyLink': 'https://growexx.atlassian.net/browse/WA-52',
                                'salesEstimation': 55,
                                'orginalEstimation': 8,
                                'startDate': '2022-06-16T18:30:00.000Z',
                                'endDate': '2022-06-13T18:30:00.000Z',
                                'sprintName': 'Test Sprint 2',
                                'currentStatus': 'Done',
                                'spent': 0,
                                'deviation': -8
                            }
                        ]
                    },
                    'message': 'Success'
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    return swaggerJson;
};
