const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');

const User = require('../server/models/user.model');
const CronStatus = require('../server/models/cronStatus.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10

};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchDailyMissedLog = async () => {
    try {
        CONSOLE_LOGGER.info(`FetchDailyMissedLog Cron Started at ${new Date}.`);
        await connectDB();
        const cronStatus = await CronStatus.findOne({ createdAt: { $gte: MOMENT().startOf('day') } });
        CONSOLE_LOGGER.info(`GetDailyAttendanceCron status: ${cronStatus.isGetDailyAttendanceCronRun} and run at ${cronStatus.updatedAt}.`);
        if (cronStatus.isGetDailyAttendanceCronRun) {
            const missedLoggedData = await User.aggregate(getAggregateParams());
            await sendEmail(missedLoggedData);
        }
        CONSOLE_LOGGER.info(`FetchDailyMissedLog Cron Started at ${new Date}.`);
        process.exit();
        return true;
    } catch (err) {
        CONSOLE_LOGGER.info('FetchDailyMissedLog error', err);
        process.exit();
        return false;
    }
};

const getAggregateParams = () => {
    const aggregateParams = [];
    aggregateParams.push(
        {
            '$match': {
                'label': {
                    '$exists': true
                },
                'isActive': 1
            }
        },
        {
            '$project': {
                '_id': 1,
                'label': 1
            }
        },
        {
            '$unwind': {
                'path': '$label'
            }
        },
        {
            '$lookup': {
                'from': 'logs',
                'localField': 'label',
                'foreignField': 'label',
                'as': 'workedLogs',
                'pipeline': [
                    {
                        '$match': {
                            'logDate': {
                                '$gte': new Date(MOMENT().startOf('day'))
                            }
                        }
                    },
                    {
                        '$project': {
                            'timeSpentHours': 1,
                            '_id': 0,
                            'logDate': 1
                        }
                    }
                ]
            }
        },
        {
            '$unwind': {
                'path': '$workedLogs',
                'preserveNullAndEmptyArrays': true
            }
        },
        {
            '$project': {
                'timeSpentHours': '$workedLogs.timeSpentHours',
                'label': 1,
                'logDate': '$workedLogs.logDate'
            }
        },
        {
            '$group': {
                '_id': '$label',
                'totalTimeSpentHours': {
                    '$sum': '$timeSpentHours'
                }
            }
        },
        {
            '$match': {
                'totalTimeSpentHours': {
                    '$lt': 7.75
                }
            }
        },
        {
            '$sort': {
                'totalTimeSpentHours': 1
            }
        }
    );
    return aggregateParams;
};

const sendEmail = async (data) => {
    try {
        const template = 'emailTemplates/missedWorkLogsMail.html';
        const templateVariables = {
            years: MOMENT().format('YYYY'),
            username: 'Team'
        };
        const subject = 'Worklogs missed employees list';
        let tableData = '';
        for (const e of data) {
            tableData += `<tr>
                    <td
                        style="font-size: 14px;
                        padding-bottom: 40px;
                        color: #000000;
                        border:1px solid #eeeeee;
                        padding:10px;
                        width: 20%;
                        margin:0;
                        text-align: left;"
                    >
                        ${e._id}
                    </td>
                    <td 
                        style="font-size: 14px;
                        padding-bottom: 40px;
                        color: #000000;
                        border:1px solid #eeeeee;
                        padding:10px;
                        margin:0;
                        text-align: left;"
                    >
                        ${e.totalTimeSpentHours}
                    </td>
                </tr>`;
        }
        CONSOLE_LOGGER.info(`Sending email to : ${CONSTANTS.MISSED_LOGS_EMAIL[process.env.NODE_ENV]}`);
        await Email.prepareAndSendEmail(
            CONSTANTS.MISSED_LOGS_EMAIL[process.env.NODE_ENV],
            subject,
            template,
            { ...templateVariables, tableData }
        ).then();
        CONSOLE_LOGGER.info('DailyMissedLog email sent successfully.');
    } catch (err) {
        CONSOLE_LOGGER.info('Error sending in email for missedLogs', err);
        process.exit();
    }
};

fetchDailyMissedLog();
