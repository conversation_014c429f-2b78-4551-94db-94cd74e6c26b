/**
 * PLIRating actions
 */

import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATINGS_SUCCESS,
  FETCH_PLI_RATINGS_ERROR,
  FETCH_PLI_RATING_DETAIL,
  FETCH_PLI_RATING_DETAIL_SUCCESS,
  FETCH_PLI_RATING_DETAIL_ERROR,
  OVERRIDE_PLI_RATING,
  OVERRIDE_PLI_RATING_SUCCESS,
  OVERRIDE_PLI_RATING_ERROR,
  FINALIZE_PLI_RATING,
  FINALIZE_PLI_RATING_SUCCESS,
  FINALIZE_PLI_RATING_ERROR,
  SHOW_MODAL,
  HIDE_MODAL,
  DEFAULT_ACTION,
  PROCESS_BULK_ASSIGNMENTS,
  PROCESS_BULK_ASSIGNMENTS_SUCCESS,
  PROCESS_BULK_ASSIGNMENTS_ERROR,
  SHOW_RESULTS_MODAL,
  HIDE_RESULTS_MODAL,
} from './constants';

/**
 * Fetch PLI ratings
 * @param {Object} params - Query parameters for filtering
 * @returns {Object} An action object with type and params
 */
export function fetchPLIRatings(params = {}) {
  return {
    type: FETCH_PLI_RATINGS,
    params,
  };
}

/**
 * PLI ratings fetched successfully
 * @param {Array} ratings - List of PLI ratings
 * @returns {Object} An action object with type and ratings
 */
export function fetchPLIRatingsSuccess(ratings) {
  return {
    type: FETCH_PLI_RATINGS_SUCCESS,
    ratings,
  };
}

/**
 * PLI ratings fetch error
 * @param {Object} error - Error object
 * @returns {Object} An action object with type and error
 */
export function fetchPLIRatingsError(error) {
  return {
    type: FETCH_PLI_RATINGS_ERROR,
    error,
  };
}

/**
 * Fetch PLI rating detail
 * @param {string} id - Rating ID
 * @returns {Object} An action object with type and id
 */
export function fetchPLIRatingDetail(id) {
  return {
    type: FETCH_PLI_RATING_DETAIL,
    id,
  };
}

/**
 * PLI rating detail fetched successfully
 * @param {Object} ratingDetail - Rating detail object
 * @returns {Object} An action object with type and ratingDetail
 */
export function fetchPLIRatingDetailSuccess(ratingDetail) {
  return {
    type: FETCH_PLI_RATING_DETAIL_SUCCESS,
    ratingDetail,
  };
}

/**
 * PLI rating detail fetch error
 * @param {Object} error - Error object
 * @returns {Object} An action object with type and error
 */
export function fetchPLIRatingDetailError(error) {
  return {
    type: FETCH_PLI_RATING_DETAIL_ERROR,
    error,
  };
}

/**
 * Override PLI rating
 * @param {string} id - Rating ID
 * @param {Object} data - Override data
 * @returns {Object} An action object with type, id and data
 */
export function overridePLIRating(id, data) {
  return {
    type: OVERRIDE_PLI_RATING,
    id,
    data,
  };
}

/**
 * PLI rating override successful
 * @param {Object} result - Result object
 * @returns {Object} An action object with type and result
 */
export function overridePLIRatingSuccess(result) {
  return {
    type: OVERRIDE_PLI_RATING_SUCCESS,
    result,
  };
}

/**
 * PLI rating override error
 * @param {Object} error - Error object
 * @returns {Object} An action object with type and error
 */
export function overridePLIRatingError(error) {
  return {
    type: OVERRIDE_PLI_RATING_ERROR,
    error,
  };
}

/**
 * Finalize PLI rating
 * @param {string} id - Rating ID
 * @returns {Object} An action object with type and id
 */
export function finalizePLIRating(id) {
  return {
    type: FINALIZE_PLI_RATING,
    id,
  };
}

/**
 * PLI rating finalize successful
 * @param {Object} result - Result object
 * @returns {Object} An action object with type and result
 */
export function finalizePLIRatingSuccess(result) {
  return {
    type: FINALIZE_PLI_RATING_SUCCESS,
    result,
  };
}

/**
 * PLI rating finalize error
 * @param {Object} error - Error object
 * @returns {Object} An action object with type and error
 */
export function finalizePLIRatingError(error) {
  return {
    type: FINALIZE_PLI_RATING_ERROR,
    error,
  };
}

/**
 * Show modal with selected rating
 * @param {Object} rating - Rating object
 * @returns {Object} An action object with type and rating
 */
export function showModal(rating) {
  return {
    type: SHOW_MODAL,
    rating,
  };
}

/**
 * Hide modal
 * @returns {Object} An action object with type
 */
export function hideModal() {
  return {
    type: HIDE_MODAL,
  };
}

/**
 * Default action
 * @returns {Object} An action object with type
 */
export function defaultAction() {
  return {
    type: DEFAULT_ACTION,
  };
}

/**
 * Process bulk assignments action creator
 * @param {Array} data - Array of assignment data to process
 * @returns {Object} Process bulk assignments action
 */
export function processBulkAssignments(data) {
  return {
    type: PROCESS_BULK_ASSIGNMENTS,
    data,
  };
}

/**
 * Process bulk assignments success action creator
 * @param {Object} results - Results from backend processing
 * @returns {Object} Process bulk assignments success action
 */
export function processBulkAssignmentsSuccess(results) {
  return {
    type: PROCESS_BULK_ASSIGNMENTS_SUCCESS,
    results,
  };
}

/**
 * Process bulk assignments error action creator
 * @param {Object} error - Error from backend processing
 * @returns {Object} Process bulk assignments error action
 */
export function processBulkAssignmentsError(error) {
  return {
    type: PROCESS_BULK_ASSIGNMENTS_ERROR,
    error,
  };
}

/**
 * Show results modal action creator
 * @returns {Object} Show results modal action
 */
export function showResultsModal() {
  return {
    type: SHOW_RESULTS_MODAL,
  };
}

/**
 * Hide results modal action creator
 * @returns {Object} Hide results modal action
 */
export function hideResultsModal() {
  return {
    type: HIDE_RESULTS_MODAL,
  };
}

// Action Types
export const FETCH_MENTOR_MENTEE_DATA =
  'app/PLIRatingOverview/FETCH_MENTOR_MENTEE_DATA';
export const FETCH_MENTOR_MENTEE_DATA_SUCCESS =
  'app/PLIRatingOverview/FETCH_MENTOR_MENTEE_DATA_SUCCESS';
export const FETCH_MENTOR_MENTEE_DATA_ERROR =
  'app/PLIRatingOverview/FETCH_MENTOR_MENTEE_DATA_ERROR';

// Action Creators
export const fetchMentorMenteeData = () => ({
  type: FETCH_MENTOR_MENTEE_DATA,
});

export const fetchMentorMenteeDataSuccess = data => ({
  type: FETCH_MENTOR_MENTEE_DATA_SUCCESS,
  data,
});

export const fetchMentorMenteeDataError = error => ({
  type: FETCH_MENTOR_MENTEE_DATA_ERROR,
  error,
});
