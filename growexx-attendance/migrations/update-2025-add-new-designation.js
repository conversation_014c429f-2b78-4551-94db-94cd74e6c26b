module.exports = {
    // Apply the migration
    async up(db) {
        const designationsCollection = db.collection("designations");

        const DESIGNATION = [
            'Engineering Head',
            'Sr. Project Manager',
            'Product Designer',
            'Data Science - Senior MLOps Engineer'
        ];

        console.log("Adding new designations to designations collection...");

        const designationDocs = DESIGNATION.map((name) => ({ name }));
        await designationsCollection.insertMany(designationDocs);

        console.log("Designations added to designations collection.");
    },

    // Rollback the migration
    async down(db) {
        const designationsCollection = db.collection("designations");

        console.log("Removing all designations from designations collection...");

        await designationsCollection.deleteMany({});

        console.log("All designations removed from designations collection.");
    },
};
