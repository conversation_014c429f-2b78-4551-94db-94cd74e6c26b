const message = require('../../locales/en.json');

module.exports = swaggerJson => {
    swaggerJson.paths['/kra/category-weightage'] = {
        post: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Kra'
            ],
            description: 'Add category weightage',
            summary: 'Add category weightage',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Add Add category weightage',
                required: true,
                schema: {
                    $ref: '#/definitions/addCategoryWeightage'
                }
            }],
            responses: {
                200: {
                    description: 'Add category weightage',
                    schema: {
                        $ref: '#/definitions/successAddCategoryWeightage'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        },
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Kra'
            ],
            description: 'Get category weightage',
            summary: 'Get category weightage',
            parameters: [{
                in: 'query',
                name: 'designation',
                description: 'Fetch by designation'
            }],
            responses: {
                200: {
                    description: 'Sucess',
                    schema: {
                        $ref: '#/definitions/successGetCategoryWeightage'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };
    swaggerJson.definitions.addCategoryWeightage = {
        type: 'object',
        properties: {
            categoryWeightages: {
                type: 'array',
                example: [
                    {
                        'designation': 'Data Engineer',
                        'weightage': 20,
                        'category': 'Business Result',
                        'quarter': 'Q3',
                        'year': 2022
                    },
                    {
                        'designation': 'Data Engineer',
                        'weightage': 10,
                        'category': 'Value Add',
                        'quarter': 'Q3',
                        'year': 2022
                    },
                    {
                        'designation': 'Data Engineer',
                        'weightage': 40,
                        'category': 'Impact to customer',
                        'quarter': 'Q3',
                        'year': 2022
                    },
                    {
                        'designation': 'Data Engineer',
                        'weightage': 20,
                        'category': 'Process Adherence',
                        'quarter': 'Q3',
                        'year': 2022
                    },
                    {
                        'designation': 'Data Engineer',
                        'weightage': 10,
                        'category': 'Development Goals',
                        'quarter': 'Q3',
                        'year': 2022
                    }
                ]
            }
        }
    };


    swaggerJson.definitions.successAddCategoryWeightage = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    'categoryWeightages': [
                        {
                            'designation': 'Data Engineer',
                            'weightage': 20,
                            'category': 'Business Result',
                            'quarter': 'Q3',
                            'year': 2022
                        },
                        {
                            'designation': 'Data Engineer',
                            'weightage': 10,
                            'category': 'Value Add',
                            'quarter': 'Q3',
                            'year': 2022
                        },
                        {
                            'designation': 'Data Engineer',
                            'weightage': 40,
                            'category': 'Impact to customer',
                            'quarter': 'Q3',
                            'year': 2022
                        },
                        {
                            'designation': 'Data Engineer',
                            'weightage': 20,
                            'category': 'Process Adherence',
                            'quarter': 'Q3',
                            'year': 2022
                        },
                        {
                            'designation': 'Data Engineer',
                            'weightage': 10,
                            'category': 'Development Goals',
                            'quarter': 'Q3',
                            'year': 2022
                        }
                    ]
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successGetCategoryWeightage = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    'status': 1,
                    'data': {
                        'categories': [
                            {
                                'category': 'Business Result',
                                'weightage': 20
                            },
                            {
                                'category': 'Value Add',
                                'weightage': 10
                            },
                            {
                                'category': 'Impact to customer',
                                'weightage': 40
                            },
                            {
                                'category': 'Process Adherence',
                                'weightage': 20
                            },
                            {
                                'category': 'Development Goals',
                                'weightage': 10
                            }
                        ],
                        'designation': 'Data Engineer',
                        'isWeightageAssignedToKra': 1
                    },
                    'message': 'Success'
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    return swaggerJson;

};
