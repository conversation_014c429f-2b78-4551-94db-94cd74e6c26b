const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');
const mongoose = require('mongoose');
mongoose.set('strictQuery', false);

const Logs = require('../server/models/logs.model');
const User = require('../server/models/user.model');
const Project = require('../server/models/project.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: true,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchLogs = async () => {
    try {
        await connectDB();
        const currentDate = new Date();

        // Check if today is the last day of the month
        const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();
        const isLastDayOfMonth = currentDate.getDate() === lastDayOfMonth;

        let startDate, endDate;

        if (isLastDayOfMonth) {
            // Fetch data from the first day of the current month to today
            startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            endDate = currentDate;
        } else {
            // Get the previous week's Monday–Sunday range
            const todayDay = currentDate.getDay();

            // Find last Monday (exact start of last full week)
            const lastMonday = new Date(currentDate);
            lastMonday.setDate(currentDate.getDate() - todayDay - 6);
            lastMonday.setHours(0, 0, 0, 0);

            // Find last Sunday (exact end of last full week)
            const lastSunday = new Date(lastMonday);
            lastSunday.setDate(lastMonday.getDate() + 6);
            lastSunday.setHours(23, 59, 59, 999);

            startDate = lastMonday;
            endDate = lastSunday;
        }

        console.log(`Fetching logs from: ${startDate.toISOString()} to ${endDate.toISOString()}`);

        const pipeline = [
            {
                $match: {
                    logDate: { $gte: startDate, $lte: endDate }
                }
            },
            {
                $addFields: {
                    lowercaseJiraProjectName: { $toLower: "$jiraProjectName" }
                }
            },
            {
                $lookup: {
                    from: "projects",
                    let: { lowercaseJiraProjectName: "$lowercaseJiraProjectName" },
                    pipeline: [
                        {
                            $addFields: {
                                lowercaseProjectName: { $toLower: "$projectName" }
                            }
                        },
                        {
                            $match: {
                                $expr: { $eq: ["$lowercaseProjectName", "$$lowercaseJiraProjectName"] }
                            }
                        }
                    ],
                    as: "project"
                }
            },
            { $unwind: "$project" },
            {
                $lookup: {
                    from: "users",
                    localField: "project.reviewManager",
                    foreignField: "_id",
                    as: "reviewManagerDetails"
                }
            },
            { $unwind: "$reviewManagerDetails" },
            {
                $group: {
                    _id: "$reviewManagerDetails._id",
                    reviewManager: {
                        $first: {
                            name: { $concat: ["$reviewManagerDetails.firstName", " ", "$reviewManagerDetails.lastName"] },
                            email: "$reviewManagerDetails.email"
                        }
                    },
                    projects: {
                        $addToSet: {
                            projectId: "$project._id",
                            projectName: "$project.projectName",
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    reviewManager: 1,
                    projects: 1
                }
            }
        ];

        const result = await Logs.aggregate(pipeline);
        return result;
    } catch (error) {
        console.error("Error fetching logs grouped by Review Manager:", error);
        throw error;
    }
};

const notifyPendingLogs = async () => {
    try {
        await connectDB();
        const projectsWithLogs = await fetchLogs();

        // Group projects by Review Manager
        const reviewManagers = projectsWithLogs.map(project => ({
            reviewManager: project.reviewManager,
            projects: project.projects.map(p => p.projectName),
        }));

        // Prepare and send emails
        const appUrl = process.env.FRONTEND_URL;
        await Promise.all(
            reviewManagers.map(async (reviewManagerData) => {
                const { reviewManager, projects } = reviewManagerData;

                if (projects.length > 0) {
                    const tableData = bindProjectData(projects);
                    const templateVariables = {
                        userName: reviewManager.name,
                        actionURL: `${appUrl}/review-logs`,
                        tableData: tableData,
                        content: 'Timesheet logs for your team working on the following project have been submitted for review – approved or rejected:',
                    };
                    const subject = 'Project Logs Awaiting Approval/Rejection on Timesheet';
                    const template = 'emailTemplates/pendingLogsProjects.html';
                    try {
                        await Email.prepareAndSendEmail(
                            //uncomment the below line for production and remove hardcoded values
                            //[reviewManager.email],
                            ['<EMAIL>','<EMAIL>'],
                            subject,
                            template,
                            templateVariables
                        );
                    } catch (error) {
                        console.error(`Failed to send email to ${reviewManager.email}:`, error);
                    }
                }
            })
        );

        console.log('Notification emails for pending projects/rejected/approved sent successfully.');
        process.exit(0);
    } catch (error) {
        console.error('Error sending email notifications for pending projects:', error);
        process.exit(1);
    }
};

function bindProjectData(projects) {
    let tableData = '';
    projects.forEach(project => {
        tableData += `<tr>
      <td
          style="font-size: 14px;
          padding: 10px;
          color: #000000;
          border: 1px solid #eeeeee;
          text-align: left;"
      >
          ${project}
      </td>
    </tr>`;
    });
    return tableData;
}

notifyPendingLogs();