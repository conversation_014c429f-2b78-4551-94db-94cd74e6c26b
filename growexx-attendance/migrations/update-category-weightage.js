module.exports = {
    async up (db) {
        let designations = await db
            .collection('projects')
            .aggregate([
                {
                    $unwind: {
                        path: '$users',
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $lookup: {
                        from: 'userkras',
                        let: { userId: '$users.empId', projectId: '$_id' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$projectId', '$$projectId'] },
                                            { $eq: ['$userId', '$$userId'] }
                                        ]
                                    }
                                }
                            }
                        ],
                        as: 'result'
                    }
                },
                {
                    $match: {
                        'result.0': {
                            $exists: true
                        }
                    }
                },
                {
                    $group: {
                        _id: '$users.empRole'
                    }
                }
            ])
            .toArray();
        designations = designations.map((designation) => designation._id);
        const categoryWeightage = await db
            .collection('categoryweightages')
            .find()
            .toArray();
        for (const d of categoryWeightage) {
            await db.collection('categoryweightages').updateOne(
                { _id: d._id },
                {
                    $set: {
                        quarter: 'Q2',
                        year: 2022,
                        isWeightageAssignedToKra: designations.includes(d.designation)
                            ? 1
                            : 0
                    }
                }
            );
        }
    }
};
