
/*
* CRON JOB for Generating Rag Report for storing active sprints of all projects.
*/

const projectBoardModel = require('../server/models/projectBoard.model');

const Portal = require('../server/models/portal.model');
const RunningSprint = require('../server/models/runningSprint.model');
const axios = require('axios');
const dotenv = require('dotenv');
const { transports } = require('winston');


const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [new transports.Console({})];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME }));
}

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(
        process.env.DB_PASSWORD
    )}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        console.info('MongoDB connection error.', err);
        return false;
    }
};

const getAllBoardProjects = async () => {
    const aggregateParams = [
        {
            $lookup: {
                from: 'projects',
                localField: 'projectId',
                foreignField: '_id',
                as: 'project'
            }
        },
        {
            $unwind: {
                path: '$project'
            }
        },
        {
            $group: {
                _id: '$jiraBoardName',
                projectBoard: { $first: '$$ROOT' }
            }
        },
        {
            $replaceRoot: {
                newRoot: {
                    $mergeObjects: [
                        '$projectBoard',
                        { latestSprintEndDate: '$latestSprintEndDate' }
                    ]
                }
            }
        }
    ];
    return await projectBoardModel.aggregate(aggregateParams);
};
const fetchJiraData = async (jiraUrl, portal, isPaginate = true) => {
    const responseData = [];
    let isLast = false;
    let startAt = 0;
    try {
        do {
            const url = isPaginate ? `${jiraUrl}?startAt=${startAt}` : jiraUrl;
            const response = await axios.get(url, {
                headers: {
                    Authorization: `Basic ${Buffer.from(
                        `${portal.email}:${portal.token}`
                    ).toString('base64')}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            if (!isPaginate) {
                return response.data;
            }
            responseData.push(...response.data.values);
            isLast = response.data.isLast;
            startAt += response.data.maxResults;
        } while (!isLast);
        return responseData;
    } catch (error) {
        return null;
    }
};

const ragScheduler = async () => {
    console.info(`==== CRON STARTED AT: ${MOMENT()} ==== `);
    const activeProjectSprints = [];
    const boardData = await getAllBoardProjects();
    for (let i = 0; i < boardData.length; i++) {
        const projectId = boardData[i].projectId;
        const projectName = boardData[i].project.projectName;
        const boardId = boardData[i].jiraBoardId;
        const portal = await Portal.findById(boardData[i].portalId);
        const jiraSprintURL = `https://${portal.url}/rest/agile/1.0/board/${boardId}/sprint`;
        const SprintData = await fetchJiraData(jiraSprintURL, portal);
        if (SprintData) {
            const activeSprints = SprintData.filter((sprint) => sprint.state === 'active');
            if (activeSprints.length !== 0) {
                const activeSprint = {
                    projectId, projectName,
                    sprintId: activeSprints[0].id,
                    startDate: activeSprints[0].startDate,
                    endDate: activeSprints[0].endDate,
                    jiraBoardId: activeSprints[0].originBoardId,
                    state: activeSprints[0].state,
                    sprintUrl: activeSprints[0].self,
                    sprintGoal: activeSprints[0].goal
                };
                const isExists = await RunningSprint.findOne({
                    sprintId: activeSprint.sprintId,
                    projectName: activeSprint.projectName,
                    boardId: activeSprint.boardId
                });
                if (!isExists) {
                    console.log(`
                        Storing sprint active details id: ${activeSprint.sprintId}, 
                        boardId: ${activeSprint.jiraBoardId}
                    `);
                    activeProjectSprints.push(activeSprint);
                }
            }
        }
    }
    await RunningSprint.insertMany(activeProjectSprints);
    console.info(`==== CRON ENDED at: ${MOMENT()} ====`);
};

const main = async () => {
    await connectDB();
    console.info('------ CRON FOR STORING RUNNING SPRINTS -------');
    await ragScheduler();
    process.exit();
};

main();

module.exports = ragScheduler;
