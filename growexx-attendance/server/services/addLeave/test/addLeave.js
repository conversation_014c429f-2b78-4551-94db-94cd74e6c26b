module.exports = {
    addLeave: [{
        it: 'As a admin I should validate if leaveType is invalid pass',
        options: {
            leaveType: 'Growexx'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if leaveType is pass as blank',
        options: {
            leaveType: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if employeeId is pass as blank',
        options: {
            leaveType: 'Paid Leave',
            employeeId: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if startDate is not pass',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if startDate is pass but empty',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if startDate is pass as invalid',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: 'test'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if end date is not pass',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: '2021-02-01'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if end date is pass as empty',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: '2021-02-01',
            endDate: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if  end date is invalid pass',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: '2021-02-01',
            endDate: 'Test'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if employee exists',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1001,
            startDate: '2021-02-01',
            endDate: '2021-02-01'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate I should not add leave for weekend',
        options: {
            leaveType: 'Paid Leave',
            employeeId: 1,
            startDate: '2021-11-06',
            endDate: '2021-11-06'
        },
        status: 1
    }]
};
