const TestCase = require('./addKra');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Add kra', () => {

    TestCase.addKra.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .post('/kra')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should able to add kra', async () => {
        const data = {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software Engineer',
            quarter: 'Q3',
            year: 2022
        };
        const res = await request(process.env.BASE_URL)
            .post('/kra')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });
});
