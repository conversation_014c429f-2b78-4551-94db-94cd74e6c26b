module.exports = {
    // Apply the migration
    async up(db) {
        const logsCollection = db.collection("logs");

        console.log("Updating logStatus to 0 for logs created on or after 2024-12-01...");

        try {
            const result = await logsCollection.updateMany(
                { logDate: { $gt: new Date("2024-12-01T00:00:00.000Z") } },
                {
                    $set: {
                        logStatus: 0,
                    },
                }
            );

            console.log(`Migration completed: ${result.modifiedCount} documents updated.`);
        } catch (error) {
            console.error("Error during migration:", error);
        }
    },

    // Rollback the migration
    async down(db) {
        const logsCollection = db.collection("logs");

        console.log("Reverting logStatus changes for logs created on or after 2024-12-01...");

        try {
            const result = await logsCollection.updateMany(
                { logDate: { $gt: new Date("2024-12-01T00:00:00.000Z") } },
                {
                    $unset: {
                        logStatus: null,
                    },
                }
            );

            console.log(`Rollback completed: ${result.modifiedCount} documents reverted.`);
        } catch (error) {
            console.error("Error during rollback:", error);
        }
    },
};
