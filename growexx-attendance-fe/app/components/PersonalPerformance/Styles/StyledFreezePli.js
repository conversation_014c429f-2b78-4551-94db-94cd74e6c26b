import styled from 'styled-components';
import { Button } from 'antd';

export const Container = styled.div`
  margin-top: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

export const Label = styled.div`
  font-weight: 500;
  color: #666;
  padding: 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
  word-break: break-word;
`;

export const Value = styled.div`
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  min-height: 40px;
  display: flex;
  align-items: center;
  word-break: break-word;
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
`;

export const StyledButton = styled(Button)`
  min-width: 120px;
  margin-bottom: 8px;
`;
