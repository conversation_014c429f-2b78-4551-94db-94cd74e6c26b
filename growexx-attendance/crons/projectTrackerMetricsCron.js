const dotenv = require('dotenv');
const { createLogger, format, transports } = require('winston');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

const projectTrackerHelper = require('./monthlydeviationHelper');
const Project = require('../server/models/project.model');
const { combine, timestamp, printf } = format;
// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');
const loggerFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} => ${level}: ${message}`;
});

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [
    new transports.Console({})
];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME }));
}
const logger = createLogger({
    format: combine(
        timestamp(),
        loggerFormat
    ),
    transports: appTransports
});

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return process.env.NODE_ENV !== 'testing' && await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        logger.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchDailyProjectTracker = async () => {
    try {
        logger.info(`Fetch Daily Project Tracker Cron Started at ${new Date}`);
        await connectDB();

        // Fetch all active projects
        const projects = await Project.find({ 
            isActive: 1,
            projectState: 'Active',
            portalId: { $exists: true, $ne: null },
            jiraProjectId: { $exists: true, $ne: null }
        }).select('_id');

        // Extract project IDs into an array
        const projectIds = projects.map(project => project._id.toString());

        try {
            await projectTrackerHelper.monthlyDeviationData(projectIds);
            logger.info(`Successfully processed ${projectIds.length} projects`);
        } catch (error) {
            logger.error(`Error processing projects: ${error.message}`);
        }

        logger.info(`Fetch Daily Project Tracker Cron Completed at ${new Date}`);
        process.env.NODE_ENV !== 'testing' && process.exit();
    } catch (err) {
        logger.error('Fetch Daily Project Tracker error', err.message);
        process.env.NODE_ENV !== 'testing' && process.exit();
    }
};

fetchDailyProjectTracker();
module.exports = fetchDailyProjectTracker;
