import React from 'react';
import { render, fireEvent, act } from 'react-testing-library';
import { notification } from 'antd';
import Submit from '../Submit';
import { EmployeeProfileContext } from '../../../containers/EmployeeProfile/context';
import request from '../../../utils/request';

// Mock dependencies
jest.mock('../../../utils/request', () => jest.fn());

describe('Submit Component', () => {
  const mockIds = {
    menteeId: 'mentee123',
    mentorId: 'mentor456',
    parameterIds: {
      project1_Rated: 'param1',
      project2_Additional: 'param2',
    },
  };

  const mockPliDataMap = {
    project1_Rated: {
      sprint1: {
        parameter1: { value: 4 },
        parameter2: { value: 3 },
      },
      sprint2: {
        parameter1: { value: 5 },
        parameter2: { value: 4 },
      },
      comments: 'Good performance on project 1',
      projectWeightage: '60',
    },
    project2_Additional: {
      sprint1: {
        parameter1: { value: 3 },
        parameter2: { value: 3 },
      },
      comments: 'Average performance on project 2',
      projectWeightage: '40',
    },
  };

  const mockProfile = {
    name: '<PERSON>e',
    employeeId: 'emp123',
    menteeId: 'mentee123',
    mentorId: 'mentor456',
  };

  const mockContextValue = {
    pliDataMap: mockPliDataMap,
    profile: mockProfile,
    selectedMonth: 'January',
    commentsMap: {
      project1_Rated: 'Good performance on project 1',
      project2_Additional: 'Average performance on project 2',
    },
  };

  const calculatedPLIScore = 3.8;

  // Setup and teardown for each test
  beforeEach(() => {
    // Spy on notification methods before each test
    jest.spyOn(notification, 'success').mockImplementation(() => {});
    jest.spyOn(notification, 'error').mockImplementation(() => {});
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original implementation after each test
    jest.restoreAllMocks();
  });

  it('renders without crashing', () => {
    const { container } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );
    expect(container).toBeTruthy();
  });

  it('displays the calculated PLI score', () => {
    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );
    expect(getByText('Calculated PLI Score')).toBeTruthy();
    expect(getByText('3.8')).toBeTruthy();
  });

  it('displays N/A when no PLI score is provided', () => {
    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} />
      </EmployeeProfileContext.Provider>,
    );
    expect(getByText('N/A')).toBeTruthy();
  });

  it('renders a submit button', () => {
    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );
    expect(getByText('Submit')).toBeTruthy();
  });

  it('formats data correctly for submission', async () => {
    // Mock successful API response
    request.mockResolvedValueOnce({ status: 1, message: 'Success' });

    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );

    // Click the submit button
    await act(async () => {
      fireEvent.click(getByText('Submit'));
    });

    // Check that request was called with correctly formatted data
    expect(request).toHaveBeenCalledTimes(1);
    const [endpoint, options] = request.mock.calls[0];

    // Verify endpoint
    expect(endpoint).toContain('sprint-scores');

    // Parse the request body
    const requestBody = JSON.parse(options.body);

    // Verify structure of request body
    expect(requestBody).toHaveProperty('menteeId', 'mentee123');
    expect(requestBody).toHaveProperty('mentorId', 'mentor456');
    expect(requestBody).toHaveProperty('month', 1); // January
    expect(requestBody).toHaveProperty('projectRatings');
    expect(requestBody.projectRatings.length).toBe(2);

    // Verify project ratings structure
    const project1 = requestBody.projectRatings.find(
      p => p.projectId === 'project1',
    );
    expect(project1).toBeTruthy();
    expect(project1.projectWeightage).toBe(60);

    // Verify notification was called
    expect(notification.success).toHaveBeenCalledTimes(1);
    expect(notification.success).toHaveBeenCalledWith({
      message: 'Success',
      description: 'Sprint scores submitted successfully',
    });
  });

  it('handles API error gracefully', async () => {
    // Mock API error
    request.mockRejectedValueOnce(new Error('API Error'));

    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );

    // Click the submit button
    await act(async () => {
      fireEvent.click(getByText('Submit'));
    });

    // Error notification should be shown
    expect(notification.error).toHaveBeenCalledTimes(1);
    expect(notification.error).toHaveBeenCalledWith({
      message: 'Error',
      description: 'API Error',
    });
  });

  it('handles missing context data gracefully', async () => {
    // Render with empty context
    const { getByText } = render(
      <EmployeeProfileContext.Provider value={{}}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );

    // Click the submit button
    await act(async () => {
      fireEvent.click(getByText('Submit'));
    });

    // Error notification should be shown
    expect(notification.error).toHaveBeenCalledTimes(1);
    expect(notification.error).toHaveBeenCalledWith({
      message: 'Error',
      description: 'No data available to submit',
    });
  });

  it('sets loading state while submitting', async () => {
    // For this test, we'll focus on verifying the request is called
    // and not worry about the button loading state which is hard to test
    let resolveRequest;
    const requestPromise = new Promise(resolve => {
      resolveRequest = () => resolve({ status: 1 });
    });

    request.mockImplementationOnce(() => requestPromise);

    const { getByText } = render(
      <EmployeeProfileContext.Provider value={mockContextValue}>
        <Submit ids={mockIds} calculatedPLIScore={calculatedPLIScore} />
      </EmployeeProfileContext.Provider>,
    );

    // Click the submit button
    const submitButton = getByText('Submit');
    await act(async () => {
      fireEvent.click(submitButton);
    });

    // Verify request was called
    expect(request).toHaveBeenCalledTimes(1);

    // Resolve the API request
    await act(async () => {
      resolveRequest();
    });

    // Verify success notification was shown after resolution
    expect(notification.success).toHaveBeenCalledTimes(1);
  });
});
