const chai = require('chai');
const expect = chai.expect;
const assert = chai.assert;
const MOMENT = require('moment');

// Load environment variables for testing
const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: __dirname + '/../../../../' + env + '.env' });

describe('Download user attendance missing logs report', () => {
    describe('Service Unit Tests', () => {
        it('Should format hours correctly - whole numbers', () => {
            const formatHours = (hours) => {
                return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`;
            };
            
            assert.equal(formatHours(0), '0h');
            assert.equal(formatHours(1), '1h');
            assert.equal(formatHours(8), '8h');
            assert.equal(formatHours(16), '16h');
        });

        it('Should format hours correctly - decimal numbers', () => {
            const formatHours = (hours) => {
                return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`;
            };
            
            assert.equal(formatHours(8.5), '8.50h');
            assert.equal(formatHours(16.25), '16.25h');
            assert.equal(formatHours(7.75), '7.75h');
            assert.equal(formatHours(8.1), '8.10h');
        });

        it('Should format hours correctly - handle precision', () => {
            const formatHours = (hours) => {
                return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`;
            };
            
            assert.equal(formatHours(8.0), '8h');
            assert.equal(formatHours(16.00), '16h');
            assert.equal(formatHours(7.333), '7.33h');
            assert.equal(formatHours(12.999), '13.00h');
        });

        it('Should handle edge cases in hour formatting', () => {
            const formatHours = (hours) => {
                return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`;
            };
            
            assert.equal(formatHours(0.1), '0.10h');
            assert.equal(formatHours(0.01), '0.01h');
            assert.equal(formatHours(23.99), '23.99h');
            assert.equal(formatHours(0.001), '0.00h');
        });

        it('Should generate proper CSV headers', () => {
            const headers = [
                { key: 'Content-Type', value: 'text/csv' },
                { key: 'Content-Disposition', value: 'attachment; filename=attendance-missing-logs-report.csv' }
            ];

            const contentTypeHeader = headers.find(h => h.key === 'Content-Type');
            const dispositionHeader = headers.find(h => h.key === 'Content-Disposition');
            
            assert.equal(contentTypeHeader.value, 'text/csv');
            assert.include(dispositionHeader.value, 'attendance-missing-logs-report');
            assert.include(dispositionHeader.value, '.csv');
        });

        it('Should exclude weekends from date range', () => {
            const getWeekdaysInRange = (startDate, endDate) => {
                const dateRange = [];
                let currentDate = MOMENT(startDate);
                const endMoment = MOMENT(endDate);
                
                while (currentDate.isSameOrBefore(endMoment, 'day')) {
                    // Skip weekends (Saturday = 6, Sunday = 0)
                    if (currentDate.day() !== 0 && currentDate.day() !== 6) {
                        dateRange.push(currentDate.format('YYYY-MM-DD'));
                    }
                    currentDate.add(1, 'day');
                }
                return dateRange;
            };

            const weekdays = getWeekdaysInRange('2021-03-06', '2021-03-08'); // Saturday to Monday
            
            assert.include(weekdays, '2021-03-08'); // Monday
            assert.notInclude(weekdays, '2021-03-06'); // Saturday
            assert.notInclude(weekdays, '2021-03-07'); // Sunday
        });

        it('Should format date for CSV headers correctly', () => {
            const formatDateForHeader = (date) => {
                return MOMENT(date).format('DD-MMM-YYYY');
            };

            assert.equal(formatDateForHeader('2021-03-01'), '01-Mar-2021');
            assert.equal(formatDateForHeader('2021-12-25'), '25-Dec-2021');
            assert.equal(formatDateForHeader('2021-01-01'), '01-Jan-2021');
        });

        it('Should properly format employee names for CSV', () => {
            const formatEmployeeName = (firstName, lastName) => {
                return `${firstName || ''} ${lastName || ''}`.trim() || 'N/A';
            };

            assert.equal(formatEmployeeName('John', 'Doe'), 'John Doe');
            assert.equal(formatEmployeeName('John', ''), 'John');
            assert.equal(formatEmployeeName('', 'Doe'), 'Doe');
            assert.equal(formatEmployeeName('', ''), 'N/A');
            assert.equal(formatEmployeeName(null, null), 'N/A');
        });

        it('Should calculate missing hours correctly', () => {
            const calculateMissingHours = (dailyHours, leaveHours, hasLeave) => {
                let missingHours = 0;
                
                if (hasLeave) {
                    // If there's a leave, no missing hours
                    missingHours = 0;
                } else if (dailyHours === 0) {
                    // No leave and no hours logged - full day missing
                    missingHours = 8;
                } else if (dailyHours < 8) {
                    // Partial hours logged, missing the remainder
                    missingHours = 8 - dailyHours;
                }
                
                return missingHours;
            };

            assert.equal(calculateMissingHours(0, 0, false), 8); // Full day missing
            assert.equal(calculateMissingHours(8, 0, false), 0); // Full day logged
            assert.equal(calculateMissingHours(4, 0, false), 4); // Half day missing
            assert.equal(calculateMissingHours(0, 8, true), 0); // On leave
            assert.equal(calculateMissingHours(4, 4, true), 0); // Partial leave
        });

        it('Should determine display value correctly', () => {
            const getDisplayValue = (dailyHours, leaveType) => {
                if (leaveType) {
                    return leaveType;
                } else if (dailyHours === 0) {
                    return 'Not Added';
                } else {
                    return `${dailyHours}h`;
                }
            };

            assert.equal(getDisplayValue(0, null), 'Not Added');
            assert.equal(getDisplayValue(8, null), '8h');
            assert.equal(getDisplayValue(4.5, null), '4.5h');
            assert.equal(getDisplayValue(0, 'Sick Leave'), 'Sick Leave');
            assert.equal(getDisplayValue(4, 'Casual Leave'), 'Casual Leave');
        });

        it('Should create proper CSV field structure', () => {
            const dateRange = ['2021-03-01', '2021-03-02', '2021-03-03'];
            const formattedDateRange = dateRange.map(date => MOMENT(date).format('DD-MMM-YYYY'));
            const fields = ['Employee Name', ...formattedDateRange, 'Total Hours', 'Total Missing Hours'];

            assert.include(fields, 'Employee Name');
            assert.include(fields, 'Total Hours');
            assert.include(fields, 'Total Missing Hours');
            assert.include(fields, '01-Mar-2021');
            assert.include(fields, '02-Mar-2021');
            assert.include(fields, '03-Mar-2021');
            assert.equal(fields[0], 'Employee Name');
            assert.equal(fields[fields.length - 2], 'Total Hours');
            assert.equal(fields[fields.length - 1], 'Total Missing Hours');
        });

        it('Should create proper employee row data', () => {
            const mockEmployee = {
                firstName: 'John',
                lastName: 'Doe'
            };
            
            const dateRange = ['2021-03-01', '2021-03-02'];
            const logsMap = {
                'employeeId_2021-03-01': 8,
                'employeeId_2021-03-02': 4
            };
            const leavesMap = {
                'employeeId_2021-03-01': null,
                'employeeId_2021-03-02': null
            };

            // Simulate row creation
            const employeeRow = {
                'Employee Name': `${mockEmployee.firstName} ${mockEmployee.lastName}`
            };

            let totalHours = 0;
            let totalMissingHours = 0;

            dateRange.forEach(date => {
                const key = `employeeId_${date}`;
                const dailyHours = logsMap[key] || 0;
                const leaveData = leavesMap[key];
                
                let displayValue = '';
                if (leaveData) {
                    displayValue = leaveData;
                } else if (dailyHours === 0) {
                    displayValue = 'Not Added';
                    totalMissingHours += 8;
                } else {
                    displayValue = `${dailyHours}h`;
                    totalHours += dailyHours;
                    if (dailyHours < 8) {
                        totalMissingHours += (8 - dailyHours);
                    }
                }
                
                const formattedDate = MOMENT(date).format('DD-MMM-YYYY');
                employeeRow[formattedDate] = displayValue;
            });

            const formattedTotalHours = totalHours % 1 === 0 ? `${totalHours}h` : `${totalHours.toFixed(2)}h`;
            const formattedMissingHours = totalMissingHours % 1 === 0 ? `${totalMissingHours}h` : `${totalMissingHours.toFixed(2)}h`;
            
            employeeRow['Total Hours'] = formattedTotalHours;
            employeeRow['Total Missing Hours'] = formattedMissingHours;

            assert.equal(employeeRow['Employee Name'], 'John Doe');
            assert.equal(employeeRow['01-Mar-2021'], '8h');
            assert.equal(employeeRow['02-Mar-2021'], '4h');
            assert.equal(employeeRow['Total Hours'], '12h');
            assert.equal(employeeRow['Total Missing Hours'], '4h');
        });

        it('Should handle CSV options correctly', () => {
            const csvOptions = { 
                fields: ['Employee Name', 'Total Hours'], 
                quote: '"',
                delimiter: ',',
                eol: '\n'
            };

            assert.equal(csvOptions.quote, '"');
            assert.equal(csvOptions.delimiter, ',');
            assert.equal(csvOptions.eol, '\n');
            assert.include(csvOptions.fields, 'Employee Name');
            assert.include(csvOptions.fields, 'Total Hours');
        });

        it('Should sort employee data correctly', () => {
            const mockData = [
                { 'Employee Name': 'John Doe' },
                { 'Employee Name': 'Alice Smith' },
                { 'Employee Name': 'Bob Johnson' }
            ];

            const sortedData = mockData.sort((a, b) => {
                return (a['Employee Name'] || '').localeCompare(b['Employee Name'] || '');
            });

            assert.equal(sortedData[0]['Employee Name'], 'Alice Smith');
            assert.equal(sortedData[1]['Employee Name'], 'Bob Johnson');
            assert.equal(sortedData[2]['Employee Name'], 'John Doe');
        });

        it('Should handle empty data gracefully', () => {
            const reportData = [];
            const fields = ['Employee Name', 'Total Hours', 'Total Missing Hours'];
            
            // Even with empty data, structure should be valid
            assert.isArray(reportData);
            assert.include(fields, 'Employee Name');
            assert.include(fields, 'Total Hours');
            assert.include(fields, 'Total Missing Hours');
        });

        it('Should validate date range logic', () => {
            const validateDateRange = (startDate, endDate) => {
                const start = MOMENT(startDate);
                const end = MOMENT(endDate);
                
                return {
                    isValid: start.isValid() && end.isValid(),
                    isStartBeforeEnd: start.isSameOrBefore(end),
                    daysDifference: end.diff(start, 'days')
                };
            };

            const result1 = validateDateRange('2021-03-01', '2021-03-31');
            assert.isTrue(result1.isValid);
            assert.isTrue(result1.isStartBeforeEnd);
            assert.equal(result1.daysDifference, 30);

            const result2 = validateDateRange('2021-03-31', '2021-03-01');
            assert.isTrue(result2.isValid);
            assert.isFalse(result2.isStartBeforeEnd);

            const result3 = validateDateRange('invalid-date', '2021-03-31');
            assert.isFalse(result3.isValid);
        });

        it('Should generate error response when exception occurs', () => {
            const createErrorResponse = (errorMessage) => {
                return {
                    headers: [
                        { key: 'Content-Type', value: 'text/csv' },
                        { key: 'Content-Disposition', value: 'attachment; filename=attendance-missing-logs-error.csv' }
                    ],
                    data: `Error,Message\n"Error occurred while generating report","${errorMessage}"`
                };
            };

            const errorResponse = createErrorResponse('Test database error');
            
            assert.include(errorResponse.data, 'Error,Message');
            assert.include(errorResponse.data, 'Error occurred while generating report');
            assert.include(errorResponse.data, 'Test database error');
            assert.equal(errorResponse.headers[0].value, 'text/csv');
        });

        it('Should handle leave data integration correctly', () => {
            const integrateLeaveData = (dailyHours, leaveData) => {
                const result = {
                    displayValue: '',
                    totalHours: 0,
                    missingHours: 0
                };

                if (leaveData && leaveData.type) {
                    // Employee is on leave
                    result.displayValue = leaveData.type;
                    result.totalHours = 0;
                    result.missingHours = 0;
                } else if (dailyHours === 0) {
                    // No leave, no hours logged
                    result.displayValue = 'Not Added';
                    result.totalHours = 0;
                    result.missingHours = 8;
                } else {
                    // Hours are logged
                    result.displayValue = `${dailyHours}h`;
                    result.totalHours = dailyHours;
                    result.missingHours = dailyHours < 8 ? (8 - dailyHours) : 0;
                }

                return result;
            };

            // Test full day logged
            const fullDay = integrateLeaveData(8, null);
            assert.equal(fullDay.displayValue, '8h');
            assert.equal(fullDay.totalHours, 8);
            assert.equal(fullDay.missingHours, 0);

            // Test partial day logged
            const partialDay = integrateLeaveData(4, null);
            assert.equal(partialDay.displayValue, '4h');
            assert.equal(partialDay.totalHours, 4);
            assert.equal(partialDay.missingHours, 4);

            // Test no hours logged
            const noHours = integrateLeaveData(0, null);
            assert.equal(noHours.displayValue, 'Not Added');
            assert.equal(noHours.totalHours, 0);
            assert.equal(noHours.missingHours, 8);

            // Test on leave
            const onLeave = integrateLeaveData(0, { type: 'Sick Leave', hours: 8 });
            assert.equal(onLeave.displayValue, 'Sick Leave');
            assert.equal(onLeave.totalHours, 0);
            assert.equal(onLeave.missingHours, 0);
        });
    });
}); 