const chai = require("chai");
const sinon = require("sinon");
const expect = chai.expect;
const mongoose = require("mongoose");
const FreezePliRatingService = require("../freezePliRatingService");
const FreezePliRatingController = require("../freezePliRatingController");
const Utils = require("../../../util/utilFunctions");

describe("FreezePliRating Controller", () => {
  let sandbox;
  let req;
  let res;
  const mockPliRatingId = new mongoose.Types.ObjectId();
  const mockMentorId = new mongoose.Types.ObjectId();
  const mockMenteeId = new mongoose.Types.ObjectId();

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    req = {
      body: {
        pliRatingId: mockPliRatingId.toString(),
        mentorId: mockMentorId.toString(),
        menteeId: mockMenteeId.toString(),
        month: 5,
        year: 2025,
      },
    };
    res = {
      __: sinon.stub().returns("Localized string"),
      status: sinon.stub().returns({ json: sinon.stub() }),
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe("freezePliRating", () => {
    it("should successfully freeze PLI rating", async () => {
      // Mock service response
      const mockResponse = {
        status: 1,
        data: {
          _id: mockPliRatingId,
          mentorId: mockMentorId,
          menteeId: mockMenteeId,
          isFrozen: true,
          status: "Submitted",
          month: 5,
          year: 2025,
        },
      };

      // Setup stubs
      const freezePliRatingStub = sandbox
        .stub(FreezePliRatingService, "freezePliRating")
        .resolves(mockResponse);
      const sendResponseStub = sandbox.stub(Utils, "sendResponse");

      // Act
      await FreezePliRatingController.freezePliRating(req, res);

      // Assert
      expect(freezePliRatingStub.calledOnce).to.be.true;
      expect(freezePliRatingStub.calledWith(req, res.__)).to.be.true;
      expect(sendResponseStub.calledOnce).to.be.true;
      expect(
        sendResponseStub.calledWith(null, mockResponse, res, "Localized string")
      ).to.be.true;
    });

    it("should handle errors when freezing PLI rating", async () => {
      // Mock error
      const mockError = new Error("Test error");

      // Setup stubs
      const freezePliRatingStub = sandbox
        .stub(FreezePliRatingService, "freezePliRating")
        .rejects(mockError);
      const sendResponseStub = sandbox.stub(Utils, "sendResponse");

      // Act
      await FreezePliRatingController.freezePliRating(req, res);

      // Assert
      expect(freezePliRatingStub.calledOnce).to.be.true;
      expect(sendResponseStub.calledOnce).to.be.true;
      expect(sendResponseStub.calledWith(mockError, null, res, "Test error")).to
        .be.true;
    });
  });

  describe("acceptPliRating", () => {
    beforeEach(() => {
      req = {
        body: {
          pliRatingId: mockPliRatingId.toString(),
          menteeId: mockMenteeId.toString(),
        },
      };
    });

    it("should successfully accept PLI rating", async () => {
      // Mock service response
      const mockResponse = {
        status: 1,
        data: {
          _id: mockPliRatingId,
          mentorId: mockMentorId,
          menteeId: mockMenteeId,
          isFrozen: true,
          status: "Accepted",
        },
      };

      // Setup stubs
      const acceptPliRatingStub = sandbox
        .stub(FreezePliRatingService, "acceptPliRating")
        .resolves(mockResponse);
      const sendResponseStub = sandbox.stub(Utils, "sendResponse");

      // Act
      await FreezePliRatingController.acceptPliRating(req, res);

      // Assert
      expect(acceptPliRatingStub.calledOnce).to.be.true;
      expect(acceptPliRatingStub.calledWith(req, res.__)).to.be.true;
      expect(sendResponseStub.calledOnce).to.be.true;
      expect(
        sendResponseStub.calledWith(null, mockResponse, res, "Localized string")
      ).to.be.true;
    });

    it("should handle errors when accepting PLI rating", async () => {
      // Mock error
      const mockError = new Error("Test error");

      // Setup stubs
      const acceptPliRatingStub = sandbox
        .stub(FreezePliRatingService, "acceptPliRating")
        .rejects(mockError);
      const sendResponseStub = sandbox.stub(Utils, "sendResponse");

      // Act
      await FreezePliRatingController.acceptPliRating(req, res);

      // Assert
      expect(acceptPliRatingStub.calledOnce).to.be.true;
      expect(sendResponseStub.calledOnce).to.be.true;
      expect(sendResponseStub.calledWith(mockError, null, res, "Test error")).to
        .be.true;
    });
  });
});
