/**
 * @name leave model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const { LEAVE_TYPE } = require('../util/constants');

const schema = new appMongoose.Schema({
    startDate: {
        type: Date,
        required: true
    },
    leaveDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    duration: {
        type: String,
        enum: ['full', 'first', 'second'],
        required: true
    },
    leaveType: {
        type: String,
        trim: true,
        enum: LEAVE_TYPE,
        required: true
    },
    timeSpentHours: {
        type: Number
    },
    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user'
    },
    isDelete: {
        type: Number,
        default: 0,
        enum: [0, 1]
    }
}, {
    timestamps: true
});
schema.path('leaveType').required(true, 'Leave type cannot be blank');
schema.path('leaveDate').validate(async function (value) {
    const count = await this.model('leave').countDocuments({
        leaveDate: value,
        userId: this.userId
    });
    return !count;
}, 'Leave already exists');
schema.path('startDate').required(true, 'Leave start date cannot be blank');
schema.path('endDate').required(true, 'Leave end date cannot be blank');
schema.path('duration').required(true, 'Leave duration cannot be blank');
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
schema.index({ userId: 1, leaveDate: 1 }, { unique: true });

module.exports = appMongoose.model('leave', schema);
