const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');
const mongoose = require('mongoose');
mongoose.set('strictQuery', false);

const Logs = require('../server/models/logs.model');
const User = require('../server/models/user.model');
const Project = require('../server/models/project.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: true,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchLogsForLastWeek = async () => {
    try {
        await connectDB();
        const currentDate = new Date();
        const oneWeekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);

        const pipeline = [
            {
                $match: {
                    logStatus: 0,
                    logDate: { $gte: oneWeekAgo, $lte: currentDate }
                }
            },
            {
                $addFields: {
                    lowercaseJiraProjectName: { $toLower: "$jiraProjectName" }
                }
            },
            {
                $lookup: {
                    from: "projects",
                    let: { lowercaseJiraProjectName: "$lowercaseJiraProjectName" },
                    pipeline: [
                        {
                            $addFields: {
                                lowercaseProjectName: { $toLower: "$projectName" }
                            }
                        },
                        {
                            $match: {
                                $expr: { $eq: ["$lowercaseProjectName", "$$lowercaseJiraProjectName"] }
                            }
                        }
                    ],
                    as: "project"
                }
            },
            { $unwind: "$project" },
            {
                $lookup: {
                    from: "users",
                    localField: "project.pmUser",
                    foreignField: "_id",
                    as: "pmUserDetails"
                }
            },
            { $unwind: "$pmUserDetails" },
            {
                $group: {
                    _id: "$pmUserDetails._id",
                    pmUser: {
                        $first: {
                            name: { $concat: ["$pmUserDetails.firstName", " ", "$pmUserDetails.lastName"] },
                            email: "$pmUserDetails.email"
                        }
                    },
                    projects: {
                        $addToSet: {
                            projectId: "$project._id",
                            projectName: "$project.projectName",
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    pmUser: 1,
                    projects: 1
                }
            }
        ];

        const result = await Logs.aggregate(pipeline);
        return result;
    } catch (error) {
        console.error("Error fetching logs grouped by PM user:", error);
        throw error;
    }
};

const notifyPendingLogs = async () => {
    try {
        await connectDB();
        const projectsWithLogs = await fetchLogsForLastWeek();

        // Group projects by PM user
        const pmUsers = projectsWithLogs.map(project => ({
            pmUser: project.pmUser,
            projects: project.projects.map(p => p.projectName),
        }));

        // Prepare and send emails
        const appUrl = process.env.FRONTEND_URL;
        await Promise.all(
            pmUsers.map(async (pmUserData) => {
                const { pmUser, projects } = pmUserData;

                if (projects.length > 0) {
                    const tableData = bindProjectData(projects);
                    const templateVariables = {
                        userName: pmUser.name,
                        actionURL: `${appUrl}/review-logs`,
                        tableData: tableData,
                        content:'You have <strong>pending</strong> timesheet requests that require your immediate attention for the following projects:',
                    };
                    const subject = 'Action Required: Pending Timesheet Requests';
                    const template = 'emailTemplates/pendingLogsProjects.html';
                    try {
                        await Email.prepareAndSendEmail(
                            //[pmUser.email],
                            ['<EMAIL>','<EMAIL>'],
                            subject,
                            template,
                            templateVariables
                        );
                    } catch (error) {
                        console.error(`Failed to send email to ${pmUser.email}:`, error);
                    }
                }
            })
        );

        console.log('Notification emails for pending projects sent successfully.');
        process.exit(0);
    } catch (error) {
        console.error('Error sending email notifications for pending projects:', error);
        process.exit(1);
    }
};

function bindProjectData(projects) {
    let tableData = '';
    projects.forEach(project => {
        tableData += `<tr>
      <td
          style="font-size: 14px;
          padding: 10px;
          color: #000000;
          border: 1px solid #eeeeee;
          text-align: center;"
      >
          ${project}
      </td>
    </tr>`;
    });
    return tableData;
}

notifyPendingLogs();