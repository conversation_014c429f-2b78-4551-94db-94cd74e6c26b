module.exports = {
    async up (db) {
        await db.collection('constants').insert({
            projectSetForPersonHoursReport: [
                // DS TNM
                'Vonage',
                'Syntasa',
                'RPM-One',
                'Parkstreet Customer Rating',
                'Mistras',
                'Hytrol',
                // WM PS
                'Parkstreet',
                'PSI II',
                'PSI IV',
                'PSI V',
                'PSI VI',
                'PSI VII',
                'PSI IX',
                'PSI WW',
                'PSI Automation',
                'PSI Mobile',
                'Product Enhancements Queue',
                'PSI Enhancement',
                'IT Service Desk',
                'IT Department',
                // WM TNM
                'beatBread External Product',
                'sliceNote',
                'Technology',
                'beatBread Curve',
                'ERP',
                'Pendo',
                'Tribetech Admin',
                'CharteredBikes',
                'TesserRx Application',
                '[IOS] Roya-TV-2022',
                '[iOS] Roya News 2023',
                'Resolv Migration',
                'Omnidian',
                'PureBrand',
                'Cequence AI',
                'Max',
                'Mens Suits',
                // FPP
                'Simply Music',
                'SecretImage',
                'DressyClub',
                'TellFriends',
                'VaaLee',
                'iMadrassa Mobile Application',
                'Karma Volunteering',
                'Karma Volunteering Upgrade',
                'Hooili',
                // WM Internal
                'Readerr.io',
                'Scrumit',
                'Influentex',
                'Matangi Web Scraper',
                'Auction-Mobile App',
                'Growexx Recruitment System',
                'Growexx',
                'Matangi Industries',
                'Dr.Priyanka Chiripal',
                'WiserAI Systems',
                'RoyaKids',
                'POC',
                'COE',
                'Estimation',
                'PO Group',
                'DevOps',
                'Induction',
                'Discovery',
                'COE-Web and Mobile',
                'COE-LAMP and Angular',
                'Other Task-Web and Mobile',
                'Induction-Web and Mobile',
                'Estimation-Web and Mobile',
                'Interview-Web and Mobile',
                'Other Task',
                'Aemal Sayer',
                'Semy',
                'Interns Training Program 2024'
            ]
        });
    }
};
