module.exports = {
  async up(db, client) {
    const result = await db.collection("projects").updateMany(
      { isActive: { $exists: false } }, // Only documents where isActive is missing
      {
        $set: {
          isActive: 1,
          updatedAt: new Date(),
        },
      }
    );

    console.log(
      `✅ Added isActive: 1 to ${result.modifiedCount} project documents`
    );
  },

  async down(db, client) {
    const result = await db.collection("projects").updateMany(
      { isActive: 1 }, // Only those we added with isActive: 1
      {
        $unset: { isActive: "" },
        $set: { updatedAt: new Date() },
      }
    );

    console.log(
      `⏪ Rollback: Removed isActive from ${result.modifiedCount} project documents`
    );
  },
};
