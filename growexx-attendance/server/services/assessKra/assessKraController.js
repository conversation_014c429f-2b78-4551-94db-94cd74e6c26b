const AssessKraService = require('./assessKraService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for assess kra
 */
class AssessKraController {

    /**
     * @desc This function is being used to assess a kra
     * <AUTHOR>
     * @since 12/07/2022
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async startKRAAssessment (req, res) {
        try {
            const data = await AssessKraService.startKRAAssessment(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AssessKraController;
