/**
 * @name Logs model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    name: {
        type: String,
        unique: true,
        required: true,
        validate: {
            validator: function (v) {
                const regex = /^[a-zA-Z\s-.]+$/;
                return regex.test(v);
            },
            message: 'Name cannot contain numbers or special characters, only letters, spaces, and hyphens are allowed.',
        },
    },
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('Designation', schema);
