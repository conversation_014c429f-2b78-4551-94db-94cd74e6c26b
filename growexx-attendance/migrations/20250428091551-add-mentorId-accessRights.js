module.exports = {
  async up(db) {
    try {
      await db.collection('users').updateMany(
        {}, 
        {
          $set: {
            mentorId: null,  
            accessRights: {
              pli: false,  
              pliAdmin: false,  
              pliSuperAdmin: false  
            }
          }
        }
      );
      console.log('Migration up successful');
    } catch (error) {
      console.error('Error in migration up:', error);
      throw error;
    }
  },

  async down(db) {
    try {
      await db.collection('users').updateMany(
        {},
        {
          $unset: {
            mentorId: '',  
            accessRights: ''
          }
        }
      );
      console.log('Migration down successful');
    } catch (error) {
      console.error('Error in migration down:', error);
      throw error;
    }
  }
};
