const CONSTANTS = require("../util/constants");

/**
 * @desc Middleware to check user roles
 * @param {Array} roles Array of allowed roles
 * @returns {Function} Middleware function
 */
const checkRole = (roles) => {
  return (req, res, next) => {
    const user = res.locals.user;

    if (!user) {
      return res.status(401).json({
        status: 0,
        message: "Unauthorized - User not found",
      });
    }

    // Check if user role matches any of the allowed roles
    // Allow both role numbers and role names
    const hasAccess = roles.some((role) => {
      if (typeof role === "number") {
        return user.role === role;
      }
      // Map role names to numbers based on CONSTANTS
      if (role === "Admin") return user.role === CONSTANTS.ROLE.ADMIN;
      if (role === "BU") return user.role === CONSTANTS.ROLE.BU;
      return false;
    });

    if (!hasAccess) {
      return res.status(403).json({
        status: 0,
        message: "Forbidden - Insufficient permissions",
      });
    }

    next();
  };
};

module.exports = {
  checkRole,
};
