const chai = require('chai');
const chaiHttp = require('chai-http');
const sinon = require('sinon');
const request = require('supertest');
const TestCase = require('./addJiraPortal');
const JiraClient = require('jira.js');
const jwt = require('jsonwebtoken');

let jira;
const expect = chai.expect;
const assert = chai.assert;
chai.use(chaiHttp);

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Add jira portal to DB', () => {
    before(() => {
        const jiraObj = new JiraClient.Version2Client({
            host: 'https://new.atlassian.net',
            authentication: {
                basic: {
                    email: '<EMAIL>',
                    apiToken: 'CfPncKurIRvT0hscipX7E23B'
                }
            }
        });

        jira = sinon.mock(jiraObj.issueSearch);
        jira.expects('searchForIssuesUsingJql').once().withArgs({}).yields(null, null, JSON.stringify({}));
    });

    TestCase.addjiraPortal.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .post('/jira/portal')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should able to add new jira portal', async () => {
        const data = {
            name: 'BB',
            url: 'beatbread.atlassian.net',
            email: '<EMAIL>',
            token: '2fOH3yrFUoTx3Ld1swPD9EA7'
        };
        const res = await request(process.env.BASE_URL)
            .post('/jira/portal')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    after(() => {
        jira.restore();
    });
});
