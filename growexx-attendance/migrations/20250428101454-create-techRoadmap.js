/**
 * Migration for techRoadmap collection
 * Up: Create collection if it doesn't exist
 * Down: Drop collection
 */
module.exports = {
    async up(db, client) {
        const collections = await db.listCollections({ name: 'techRoadmap' }).toArray();
        if (collections.length === 0) {
            await db.createCollection('techRoadmap');
        }
  },

  async down(db, client) {
    const collections = await db.listCollections({ name: 'techRoadmap' }).toArray();
    if (collections.length > 0) {
      await db.collection('techRoadmap').drop();
    }
  }
};