const mongoose = require("mongoose");
const Logs = require("../../models/logs.model");
const User = require("../../models/user.model");
const Project = require("../../models/project.model");
const BusinessUnit = require("../../models/bu.model");
const { parse } = require("json2csv");
const CONSTANTS = require("../../util/constants");
const MOMENT = require("moment");

/**
 * Class represents services for download manager pending logs report.
 */
class DownloadManagerPendingLogsService {
  /**
   * @desc This function is being used to download manager pending logs report
   * <AUTHOR>
   * @since 25/12/2024
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @return {Object} response Success response with CSV data and headers
   */
  static async downloadManagerPendingLogs(req, user) {
    try {
      // Set date range
      const startDate = req.query.startDate
        ? MOMENT(req.query.startDate).startOf("day")._d
        : MOMENT().startOf("month")._d;
      const endDate = req.query.endDate
        ? MOMENT(req.query.endDate).endOf("day")._d
        : MOMENT().endOf("month")._d;

      console.log('Date range:', startDate, 'to', endDate);

      // Get manager project-wise data with logs using aggregation pipeline
      const managerProjectData = await this.getManagerProjectDataWithAggregation(startDate, endDate);

      console.log('Found manager project data count:', managerProjectData.length);

      // Generate date range array (excluding weekends)
      const dateRange = [];
      let currentDate = MOMENT(startDate);
      const endMoment = MOMENT(endDate);
      
      while (currentDate.isSameOrBefore(endMoment, 'day')) {
        // Skip weekends (Saturday = 6, Sunday = 0)
        if (currentDate.day() !== 0 && currentDate.day() !== 6) {
          dateRange.push(currentDate.format('YYYY-MM-DD'));
        }
        currentDate.add(1, 'day');
      }

      console.log('Date range columns:', dateRange);

      // Build the report data
      const reportData = [];
      
      if (!managerProjectData || managerProjectData.length === 0) {
        // If no data found, return a message row
        const messageRow = {
          ManagerName: 'No managers with log data found for the selected date range',
          ProjectName: '',
          TotalApprovedHours: '',
          TotalNotApprovedHours: ''
        };
        
        // Add empty columns for each date
        dateRange.forEach(date => {
          messageRow[date.replace(/-/g, '_')] = '';
        });
        
        reportData.push(messageRow);
      } else {
        managerProjectData.forEach(item => {
          const row = {
            ManagerName: item.managerName || 'N/A',
            ProjectName: item.projectName || 'N/A'
          };
          
          let totalApprovedHours = 0;
          let totalNotApprovedHours = 0;
          
          // Add status for each date
          dateRange.forEach(date => {
            const dateData = item.dateWiseData && item.dateWiseData[date];
            
            let displayValue = '';
            
            if (!dateData || (dateData.approvedHours === 0 && dateData.notApprovedHours === 0)) {
              displayValue = 'No Logs';
            } else {
              const parts = [];
              if (dateData.approvedHours > 0) {
                parts.push(`${dateData.approvedHours.toFixed(1)}h - Approved`);
                totalApprovedHours += dateData.approvedHours;
              }
              if (dateData.notApprovedHours > 0) {
                parts.push(`${dateData.notApprovedHours.toFixed(1)}h - Not Approved`);
                totalNotApprovedHours += dateData.notApprovedHours;
              }
              displayValue = parts.join(', ');
            }
            
            // Use underscore instead of hyphens for field names to avoid CSV issues
            row[date.replace(/-/g, '_')] = displayValue;
          });
          
          row.TotalApprovedHours = this.formatHours(totalApprovedHours);
          row.TotalNotApprovedHours = this.formatHours(totalNotApprovedHours);
          
          reportData.push(row);
        });

        // Sort by manager name, then by project name
        reportData.sort((a, b) => {
          const managerDiff = (a.ManagerName || '').localeCompare(b.ManagerName || '');
          if (managerDiff !== 0) return managerDiff;
          return (a.ProjectName || '').localeCompare(b.ProjectName || '');
        });
      }

      console.log('Generated report data rows:', reportData.length);
      console.log('Sample row structure:', reportData[0] ? Object.keys(reportData[0]) : 'No data');

      // Prepare CSV fields - Manager info first, then all dates, then totals
      // Using simple field names without spaces to avoid CSV parsing issues
      const dateFields = dateRange.map(date => date.replace(/-/g, '_'));
      const fields = [
        { label: 'Manager Name', value: 'ManagerName' },
        { label: 'Project Name', value: 'ProjectName' },
        ...dateFields.map(dateField => ({ 
          label: dateField.replace(/_/g, '-'), 
          value: dateField 
        })),
        { label: 'Total Approved Hours', value: 'TotalApprovedHours' },
        { label: 'Total Not Approved Hours', value: 'TotalNotApprovedHours' }
      ];
      
      console.log('CSV fields:', fields.map(f => f.label));
      
      const opts = { fields, quote: '"' };
      const csvData = parse(reportData, opts);

      console.log('CSV data preview (first 500 chars):', csvData.substring(0, 500));

      const startDateStr = MOMENT(startDate).format('YYYY-MM-DD');
      const endDateStr = MOMENT(endDate).format('YYYY-MM-DD');
      const filename = `managers-project-wise-logs-report-${startDateStr}-to-${endDateStr}.csv`;

      return {
        headers: [
          {
            key: "Content-Type",
            value: "text/csv",
          },
          {
            key: "Content-Disposition",
            value: `attachment; filename=${filename}`,
          },
        ],
        data: csvData,
      };
    } catch (error) {
      console.error('Error in downloadManagerPendingLogs:', error);
      // Return a proper CSV error response instead of HTML
      const errorData = [{
        ManagerName: 'Error occurred while generating report',
        ProjectName: '',
        TotalApprovedHours: '',
        TotalNotApprovedHours: '',
        Error: error.message || 'Unknown error'
      }];
      
      const csvData = parse(errorData, { quote: '"' });
      
      return {
        headers: [
          {
            key: "Content-Type",
            value: "text/csv",
          },
          {
            key: "Content-Disposition",
            value: "attachment; filename=managers-project-wise-logs-error.csv",
          },
        ],
        data: csvData,
      };
    }
  }

  /**
   * @desc Helper function to get manager project-wise data using aggregation pipeline
   * @param {Date} startDate Start date
   * @param {Date} endDate End date
   * @return {Array} Array of manager-project combinations with logs
   */
  static async getManagerProjectDataWithAggregation(startDate, endDate) {
    try {
      console.log('Starting aggregation pipeline...');
      
      const pipeline = [
        // Match logs in date range
        {
          $match: {
            logDate: { $gte: startDate, $lte: endDate },
            isActive: { $ne: 0 }, // Include active logs (1) and any other non-zero values
            jiraProjectName: { $exists: true, $ne: null, $ne: "" } // Ensure project name exists
          }
        },
        // Add lowercase project name for matching
        {
          $addFields: {
            lowercaseJiraProjectName: { $toLower: "$jiraProjectName" }
          }
        },
        // Lookup projects with case-insensitive matching
        {
          $lookup: {
            from: "projects",
            let: { lowercaseJiraProjectName: "$lowercaseJiraProjectName" },
            pipeline: [
              {
                $addFields: {
                  lowercaseProjectName: { $toLower: "$projectName" }
                }
              },
              {
                $match: {
                  $expr: { $eq: ["$lowercaseProjectName", "$$lowercaseJiraProjectName"] }
                }
              }
            ],
            as: "project"
          }
        },
        { $unwind: "$project" },
        // Lookup PM user details
        {
          $lookup: {
            from: "users",
            localField: "project.pmUser",
            foreignField: "_id",
            as: "pmUserDetails"
          }
        },
        { $unwind: "$pmUserDetails" },
        // Format date for grouping
        {
          $addFields: {
            formattedDate: {
              $dateToString: { format: "%Y-%m-%d", date: "$logDate" }
            }
          }
        },
        // Group by PM user, project, and date
        {
          $group: {
            _id: {
              pmUserId: "$pmUserDetails._id",
              projectId: "$project._id",
              date: "$formattedDate"
            },
            managerFirstName: { $first: "$pmUserDetails.firstName" },
            managerLastName: { $first: "$pmUserDetails.lastName" },
            managerEmail: { $first: "$pmUserDetails.email" },
            employeeId: { $first: "$pmUserDetails.employeeId" },
            projectName: { $first: "$project.projectName" },
            approvedHours: {
              $sum: {
                $cond: [{ $eq: ["$logStatus", 1] }, "$timeSpentHours", 0]
              }
            },
            notApprovedHours: {
              $sum: {
                $cond: [{ $ne: ["$logStatus", 1] }, "$timeSpentHours", 0]
              }
            }
          }
        },
        // Group by PM user and project to collect all dates
        {
          $group: {
            _id: {
              pmUserId: "$_id.pmUserId",
              projectId: "$_id.projectId"
            },
            managerFirstName: { $first: "$managerFirstName" },
            managerLastName: { $first: "$managerLastName" },
            managerEmail: { $first: "$managerEmail" },
            employeeId: { $first: "$employeeId" },
            projectName: { $first: "$projectName" },
            dateWiseData: {
              $push: {
                date: "$_id.date",
                approvedHours: "$approvedHours",
                notApprovedHours: "$notApprovedHours"
              }
            }
          }
        },
        // Sort by manager name, then project name
        {
          $sort: {
            managerFirstName: 1,
            managerLastName: 1,
            projectName: 1
          }
        }
      ];

      console.log('Executing aggregation pipeline...');
      const result = await Logs.aggregate(pipeline);
      console.log('Aggregation result count:', result.length);

      if (result.length > 0) {
        console.log('Sample aggregation result:', JSON.stringify(result[0], null, 2));
      }

      // Transform the result to match our expected format
      const transformedResult = result.map(item => {
        const dateWiseDataMap = {};
        
        // Convert array to map for easier access
        item.dateWiseData.forEach(dateData => {
          dateWiseDataMap[dateData.date] = {
            approvedHours: dateData.approvedHours || 0,
            notApprovedHours: dateData.notApprovedHours || 0
          };
        });

        // Combine first and last name properly
        const fullName = `${item.managerFirstName || ''} ${item.managerLastName || ''}`.trim();

        return {
          managerName: fullName || 'N/A',
          managerEmail: item.managerEmail || 'N/A',
          employeeId: item.employeeId || 'N/A',
          projectName: item.projectName || 'N/A',
          dateWiseData: dateWiseDataMap
        };
      });

      console.log('Transformed result count:', transformedResult.length);
      if (transformedResult.length > 0) {
        console.log('Sample transformed result:', JSON.stringify(transformedResult[0], null, 2));
      }
      
      return transformedResult;
    } catch (error) {
      console.error('Error in getManagerProjectDataWithAggregation:', error);
      return [];
    }
  }

  /**
   * @desc Helper function to format hours with proper decimal places and "h" suffix
   * @param {number} hours Hours to format
   * @return {string} Formatted hours string
   */
  static formatHours(hours) {
    if (hours === 0) {
      return '0h';
    } else if (hours % 1 === 0) {
      // Whole number - no decimal places
      return `${hours.toFixed(0)}h`;
    } else {
      // Decimal number - show up to 2 decimal places, remove trailing zeros
      return `${parseFloat(hours.toFixed(2))}h`;
    }
  }
}

module.exports = DownloadManagerPendingLogsService; 