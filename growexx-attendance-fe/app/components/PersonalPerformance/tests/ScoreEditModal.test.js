import '@testing-library/jest-dom';
import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react';
import ScoreEditModal from '../ScoreEditModal';

// Helper to open modal with props
const setup = (props = {}) => {
  const defaultProps = {
    visible: true,
    onCancel: jest.fn(),
    onConfirm: jest.fn(),
    sprintName: 'Sprint 1',
    parameterName: 'Quality',
    originalValue: 5,
    currentValue: 5,
    isReadOnly: false,
    comments: '',
    isSuperAdmin: false,
  };
  return render(<ScoreEditModal {...defaultProps} {...props} />);
};

describe('<ScoreEditModal />', () => {
  it('renders modal with correct title and values', () => {
    const { getByText, getByLabelText } = setup();
    expect(getByText('Sprint 1 - Quality')).toBeTruthy();
    expect(getByText('Original Value')).toBeTruthy();
    expect(getByText('5')).toBeTruthy();
    expect(getByLabelText('New Value')).toBeTruthy();
  });

  it('shows original value as 0 if originalValue is 0', () => {
    const { getByText } = setup({ originalValue: 0 });
    expect(getByText('0')).toBeTruthy();
  });

  it('calls onCancel when Cancel/Close is clicked', () => {
    const onCancel = jest.fn();
    const { getByText } = setup({ onCancel });
    fireEvent.click(getByText('Cancel'));
    expect(onCancel).toHaveBeenCalled();
  });

  it('calls onConfirm with new value and comment', async () => {
    const onConfirm = jest.fn();
    const { getByLabelText, getByText } = setup({ onConfirm, currentValue: 5 });
    fireEvent.change(getByLabelText('New Value'), { target: { value: 6 } });
    fireEvent.change(getByLabelText('Comment (Reason for change)'), {
      target: { value: 'Test comment' },
    });
    fireEvent.blur(getByLabelText('Comment (Reason for change)'));
    await act(async () => {
      jest.runAllTimers();
      await Promise.resolve();
    });
    await waitFor(() =>
      expect(getByLabelText('Comment (Reason for change)')).toHaveClass(
        'ant-input-status-success',
      ),
    );
    const confirmButton = getByText('Confirm').closest('button');
    await waitFor(() => expect(confirmButton).not.toBeDisabled());
    await act(async () => {
      fireEvent.click(confirmButton);
    });
    await waitFor(() =>
      expect(onConfirm).toHaveBeenCalledWith(
        '6',
        expect.stringContaining('Test comment'),
      ),
    );
  });

  it('requires comment when value changes', async () => {
    const onConfirm = jest.fn();
    const { getByLabelText, getByText, findByText } = setup({
      onConfirm,
      currentValue: 5,
    });
    fireEvent.change(getByLabelText('New Value'), { target: { value: 6 } });
    await act(async () => {
      fireEvent.click(getByText('Confirm'));
    });
    expect(
      await findByText('Comment is required when changing the score.'),
    ).toBeTruthy();
    expect(onConfirm).not.toHaveBeenCalled();
  });

  it('shows error if new value is same as original', async () => {
    const onConfirm = jest.fn();
    const { getByText } = setup({ onConfirm, currentValue: 5 });
    await act(async () => {
      fireEvent.click(getByText('Confirm'));
    });
    await waitFor(() =>
      expect(
        getByText('New value must be different from the original value.'),
      ).toBeTruthy(),
    );
    expect(onConfirm).not.toHaveBeenCalled();
  });

  it('renders in read-only mode with comment and user role', () => {
    const comment = 'Reviewed[OG:5][BY:SA]';
    const { getByText } = setup({ isReadOnly: true, comments: comment });
    expect(getByText('Reviewed')).toBeTruthy();
    expect(getByText('Updated By:')).toBeTruthy();
    expect(getByText('Super Admin')).toBeTruthy();
  });

  it('renders in read-only mode with mentor role', () => {
    const comment = 'Checked[OG:5][BY:M]';
    const { getByText } = setup({ isReadOnly: true, comments: comment });
    expect(getByText('Mentor')).toBeTruthy();
  });

  it('handles empty comment and displays N/A for original value', () => {
    const { getByText } = setup({
      isReadOnly: true,
      comments: '',
      originalValue: undefined,
    });
    expect(getByText('N/A')).toBeTruthy();
  });

  it('disables input fields in read-only mode', () => {
    const { getByLabelText } = setup({ isReadOnly: true });
    expect(getByLabelText('New Value')).toBeDisabled();
  });

  it('shows Close button in read-only mode', () => {
    const { getByText } = setup({ isReadOnly: true });
    expect(getByText('Close')).toBeTruthy();
  });

  it('calls onCancel when Close is clicked in read-only mode', () => {
    const onCancel = jest.fn();
    const { getByText } = setup({ isReadOnly: true, onCancel });
    fireEvent.click(getByText('Close'));
    expect(onCancel).toHaveBeenCalled();
  });

  it('trims comment and stores metadata on confirm', async () => {
    jest.useFakeTimers();
    const onConfirm = jest.fn();
    const { getByLabelText, getByText, debug } = setup({
      onConfirm,
      currentValue: 5,
      isSuperAdmin: true,
    });
    await act(async () => {
      fireEvent.change(getByLabelText('New Value'), { target: { value: 6 } });
      fireEvent.change(getByLabelText('Comment (Reason for change)'), {
        target: { value: '  Test meta  ' },
      });
      fireEvent.blur(getByLabelText('Comment (Reason for change)'));
      await act(async () => {
        jest.runAllTimers();
        await Promise.resolve();
      });
      // Wait for validation to finish
      await waitFor(() =>
        expect(getByLabelText('Comment (Reason for change)')).toHaveClass(
          'ant-input-status-success',
        ),
      );
    });
    debug(); // Log the DOM before clicking confirm
    const confirmButton = getByText('Confirm').closest('button');
    await waitFor(() => expect(confirmButton).not.toBeDisabled());
    await act(async () => {
      fireEvent.click(confirmButton);
    });
    debug(); // Log the DOM after clicking confirm
    await waitFor(() =>
      expect(onConfirm).toHaveBeenCalledWith(
        '6',
        expect.stringContaining('[OG:5][BY:SA]'),
      ),
    );
    jest.useRealTimers();
  });

  it('shows correct comment and role when comment string is complex', () => {
    const comment = 'Some feedback [OG:7][BY:M] extra';
    const { getByText } = setup({ isReadOnly: true, comments: comment });
    expect(getByText('Some feedback extra')).toBeTruthy();
    expect(getByText('Mentor')).toBeTruthy();
  });
});
