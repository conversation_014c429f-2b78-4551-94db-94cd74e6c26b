/**
 * PLIRating selectors
 */

import { createSelector } from 'reselect';
import { initialState } from './reducer';

/**
 * Direct selector to the pliRating state domain
 */
const selectPLIRatingDomain = state => state.pliRating || initialState;

/**
 * Select the PLI ratings
 */
const makeSelectPLIRatings = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.ratings,
  );

/**
 * Select loading state
 */
const makeSelectLoading = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.loading,
  );

/**
 * Select error state
 */
const makeSelectError = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.error,
  );

/**
 * Select rating detail
 */
const makeSelectRatingDetail = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.ratingDetail,
  );

/**
 * Select detail loading state
 */
const makeSelectDetailLoading = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.detailLoading,
  );

/**
 * Select detail error state
 */
const makeSelectDetailError = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.detailError,
  );

/**
 * Select modal visibility
 */
const makeSelectModalVisible = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.modalVisible,
  );

/**
 * Select current rating (for modal)
 */
const makeSelectCurrentRating = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.currentRating,
  );

/**
 * Select override loading state
 */
const makeSelectOverrideLoading = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.overrideLoading,
  );

/**
 * Select override error state
 */
const makeSelectOverrideError = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.overrideError,
  );

/**
 * Select finalize loading state
 */
const makeSelectFinalizeLoading = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.finalizeLoading,
  );

/**
 * Select finalize error state
 */
const makeSelectFinalizeError = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.finalizeError,
  );

/**
 * Select processing loading state
 */
const makeSelectProcessingLoading = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.processingLoading,
  );

/**
 * Select processing results
 */
const makeSelectProcessingResults = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.processingResults,
  );

/**
 * Select processing error state
 */
const makeSelectProcessingError = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.processingError,
  );

/**
 * Select results modal visibility
 */
const makeSelectResultsModalVisible = () =>
  createSelector(
    selectPLIRatingDomain,
    substate => substate.resultsModalVisible,
  );

const selectMentorMenteeData = () => state => state.pliRating.mentorMenteeData;
const selectMentorMenteeLoading = () => state =>
  state.pliRating.mentorMenteeLoading;
const selectMentorMenteeError = () => state =>
  state.pliRating.mentorMenteeError;

const makeSelectMentorMenteeData = () =>
  createSelector(
    selectMentorMenteeData(),
    mentorMenteeData => mentorMenteeData,
  );

const makeSelectMentorMenteeLoading = () =>
  createSelector(
    selectMentorMenteeLoading(),
    mentorMenteeLoading => mentorMenteeLoading,
  );

const makeSelectMentorMenteeError = () =>
  createSelector(
    selectMentorMenteeError(),
    mentorMenteeError => mentorMenteeError,
  );

export {
  selectPLIRatingDomain,
  makeSelectPLIRatings,
  makeSelectLoading,
  makeSelectError,
  makeSelectRatingDetail,
  makeSelectDetailLoading,
  makeSelectDetailError,
  makeSelectModalVisible,
  makeSelectCurrentRating,
  makeSelectOverrideLoading,
  makeSelectOverrideError,
  makeSelectFinalizeLoading,
  makeSelectFinalizeError,
  makeSelectProcessingLoading,
  makeSelectProcessingResults,
  makeSelectProcessingError,
  makeSelectResultsModalVisible,
  makeSelectMentorMenteeData,
  makeSelectMentorMenteeLoading,
  makeSelectMentorMenteeError,
};
