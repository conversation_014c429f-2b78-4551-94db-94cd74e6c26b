import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Modal, Input, Form, Typography, Button } from 'antd';

const { Text } = Typography;

const ScoreEditModal = ({
  visible,
  onCancel,
  onConfirm,
  sprintName,
  parameterName,
  originalValue,
  currentValue,
  isReadOnly = false,
  comments = [],
  isSuperAdmin = false,
}) => {
  const [form] = Form.useForm();
  const [displayOriginalValue, setDisplayOriginalValue] = React.useState(
    originalValue,
  );

  // Parse original value from comment string
  const parseOriginalValueFromComment = commentStr => {
    if (typeof commentStr !== 'string') return null;
    const match = commentStr.match(/\[OG:([^\]]+)\]/);
    return match ? match[1] : null;
  };

  // Parse user role from comment string
  const parseUserRoleFromComment = commentStr => {
    if (typeof commentStr !== 'string') return null;
    const match = commentStr.match(/\[BY:([^\]]+)\]/);
    return match ? match[1] : null;
  };

  // Get display comment without the markers
  const getDisplayComment = commentStr => {
    if (typeof commentStr !== 'string') return '';
    return commentStr
      .replace(/\[OG:[^\]]+\]/, '')
      .replace(/\[BY:[^\]]+\]/, '')
      .trim();
  };

  useEffect(() => {
    if (visible) {
      const commentStr = typeof comments === 'string' ? comments : '';
      const displayComment = getDisplayComment(commentStr);
      const storedOriginalValue = parseOriginalValueFromComment(commentStr);

      form.setFieldsValue({
        newValue: currentValue,
        comment: isReadOnly ? displayComment : '',
      });

      // If in read-only mode and we have a stored original value, use it
      if (isReadOnly && storedOriginalValue !== null) {
        setDisplayOriginalValue(storedOriginalValue);
      } else {
        setDisplayOriginalValue(originalValue);
      }
    }
  }, [visible, currentValue, form, comments, isReadOnly, originalValue]);

  const handleOk = () => {
    if (isReadOnly) {
      onCancel();
      return;
    }

    form
      .validateFields()
      .then(values => {
        const newRawValue = values.newValue;
        const originalRawValue = displayOriginalValue;

        // Determine if the new value is effectively the same as the original value.
        // Compare numerically if possible, otherwise as strings.
        let isSameValue = false;

        const numOriginal = Number(originalRawValue);
        const numNew = Number(newRawValue);

        if (!Number.isNaN(numOriginal) && !Number.isNaN(numNew)) {
          // Both are numbers, check if numerically same
          isSameValue = numOriginal === numNew;
        } else {
          // At least one is not a number or both are non-numeric strings, compare as strings
          isSameValue = String(originalRawValue) === String(newRawValue);
        }

        // --- Validation 1: Prevent submitting the exact same value ---
        const triggerSameValueError = isSameValue;

        if (triggerSameValueError) {
          form.setFields([
            {
              name: 'newValue',
              errors: ['New value must be different from the original value.'],
            },
          ]);
          return;
        }

        // --- Validation 2: Require comment if value has changed ---
        // Value is considered changed if they are NOT the same value based on the logic above.
        const valueChanged = !isSameValue;

        if (valueChanged && !values.comment?.trim()) {
          form.setFields([
            {
              name: 'comment',
              errors: ['Comment is required when changing the score.'],
            },
          ]);
          return;
        }

        // If validations pass, proceed with confirmation
        const userRole = isSuperAdmin ? 'SA' : 'M';
        const commentWithMetadata = values.comment?.trim()
          ? `${values.comment.trim()}[OG:${displayOriginalValue}][BY:${userRole}]`
          : '';
        onConfirm(values.newValue, commentWithMetadata);
        form.resetFields();
      })
      .catch(() => {});
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // Get user role display text
  const getUserRoleDisplay = commentStr => {
    const role = parseUserRoleFromComment(commentStr);
    if (!role) return '';
    return role === 'SA' ? 'Super Admin' : 'Mentor';
  };

  return (
    <Modal
      visible={visible}
      title={`${sprintName} - ${parameterName}`}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose
      closable={false}
      footer={[
        <Button key="back" onClick={handleCancel}>
          {isReadOnly ? 'Close' : 'Cancel'}
        </Button>,
        !isReadOnly && (
          <Button key="submit" type="primary" onClick={handleOk}>
            Confirm
          </Button>
        ),
      ].filter(Boolean)}
    >
      <Form form={form} layout="vertical">
        <Form.Item label="Original Value">
          <Text strong>
            {displayOriginalValue === 0 ? 0 : displayOriginalValue || 'N/A'}
          </Text>
        </Form.Item>
        <Form.Item
          name="newValue"
          label="New Value"
          rules={
            [
              // { // Keep other potential rules if any, but remove the 'required: true' one
              //   required: true,
              //   message: 'Please input the new value!',
              // },
            ]
          }
        >
          <Input type="number" step="0.1" disabled={isReadOnly} />
        </Form.Item>
        {isReadOnly ? (
          <>
            <Form.Item label="Comment">
              <Input.TextArea
                rows={3}
                value={
                  typeof comments === 'string'
                    ? getDisplayComment(comments)
                    : ''
                }
                readOnly
                style={{
                  backgroundColor: '#f5f5f5',
                  cursor: 'default',
                  userSelect: 'text',
                  WebkitUserSelect: 'text',
                  MozUserSelect: 'text',
                  msUserSelect: 'text',
                  borderColor: 'transparent',
                  boxShadow: 'none',
                  outline: 'none',
                  caretColor: 'transparent',
                  color: '#8c8c8c', // Gray out the comment text
                }}
              />
            </Form.Item>
            {/* eslint-disable */}
            {/* prettier-ignore */}
            {typeof comments === 'string' &&
              parseUserRoleFromComment(comments) && (
                <Form.Item>
                  <Text>
                    Updated By:{' '}
                    <Text strong style={{ color: '#1890ff' }}>
                      {getUserRoleDisplay(comments)}
                    </Text>
                  </Text>
                </Form.Item>
              )}
              {/* eslint-enable */}
          </>
        ) : (
          <Form.Item
            name="comment"
            label="Comment (Reason for change)"
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const formValue = getFieldValue('newValue');
                  if (
                    String(formValue) !== String(displayOriginalValue) &&
                    !value?.trim()
                  ) {
                    return Promise.reject(
                      new Error('Comment is required when changing the score.'),
                    );
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Input.TextArea
              rows={3}
              placeholder="Please provide a reason for the change"
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

ScoreEditModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  sprintName: PropTypes.string.isRequired,
  parameterName: PropTypes.string.isRequired,
  originalValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  currentValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  isReadOnly: PropTypes.bool,
  isSuperAdmin: PropTypes.bool,
  comments: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        timestamp: PropTypes.oneOfType([
          PropTypes.string,
          PropTypes.instanceOf(Date),
        ]).isRequired,
      }),
    ),
  ]),
};

ScoreEditModal.defaultProps = {
  originalValue: 'N/A',
  currentValue: '',
  isReadOnly: false,
  isSuperAdmin: false,
  comments: [],
};

export default ScoreEditModal;
