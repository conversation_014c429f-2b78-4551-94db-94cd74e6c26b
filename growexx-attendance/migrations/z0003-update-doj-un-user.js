
module.exports = {
    async up (db) {
        const dojList = [
            { employeeId: 5, doj: '2021-02-15' },
            { employeeId: 8, doj: '2021-02-15' },
            { employeeId: 9, doj: '2021-02-15' },
            { employeeId: 10, doj: '2021-02-15' },
            { employeeId: 11, doj: '2021-02-15' },
            { employeeId: 14, doj: '2021-02-15' },
            { employeeId: 15, doj: '2021-02-15' },
            { employeeId: 16, doj: '2021-02-15' },
            { employeeId: 17, doj: '2021-02-15' },
            { employeeId: 18, doj: '2021-02-15' },
            { employeeId: 19, doj: '2021-02-15' },
            { employeeId: 20, doj: '2021-02-15' },
            { employeeId: 21, doj: '2021-02-15' },
            { employeeId: 22, doj: '2021-02-15' },
            { employeeId: 23, doj: '2021-02-15' },
            { employeeId: 24, doj: '2021-02-15' },
            { employeeId: 25, doj: '2021-02-15' },
            { employeeId: 27, doj: '2021-02-15' },
            { employeeId: 28, doj: '2021-02-15' },
            { employeeId: 30, doj: '2021-02-15' },
            { employeeId: 31, doj: '2021-02-15' },
            { employeeId: 32, doj: '2021-02-15' },
            { employeeId: 33, doj: '2021-02-15' },
            { employeeId: 34, doj: '2021-02-15' },
            { employeeId: 35, doj: '2021-02-15' },
            { employeeId: 36, doj: '2021-02-15' },
            { employeeId: 37, doj: '2021-02-15' },
            { employeeId: 39, doj: '2021-02-15' },
            { employeeId: 40, doj: '2021-02-15' },
            { employeeId: 42, doj: '2021-02-15' },
            { employeeId: 43, doj: '2021-02-15' },
            { employeeId: 45, doj: '2021-02-15' },
            { employeeId: 48, doj: '2021-02-15' },
            { employeeId: 49, doj: '2021-02-15' },
            { employeeId: 50, doj: '2021-03-01' },
            { employeeId: 53, doj: '2021-03-02' },
            { employeeId: 55, doj: '2021-03-15' },
            { employeeId: 56, doj: '2021-04-06' },
            { employeeId: 57, doj: '2021-04-12' },
            { employeeId: 62, doj: '2021-05-24' },
            { employeeId: 64, doj: '2021-06-01' },
            { employeeId: 65, doj: '2021-06-07' },
            { employeeId: 66, doj: '2021-06-07' },
            { employeeId: 67, doj: '2021-06-14' },
            { employeeId: 69, doj: '2021-06-21' },
            { employeeId: 71, doj: '2021-06-21' },
            { employeeId: 73, doj: '2021-07-01' },
            { employeeId: 74, doj: '2021-07-20' },
            { employeeId: 75, doj: '2021-07-26' },
            { employeeId: 76, doj: '2021-07-27' },
            { employeeId: 77, doj: '2021-08-02' },
            { employeeId: 78, doj: '2021-08-02' },
            { employeeId: 79, doj: '2021-08-16' },
            { employeeId: 80, doj: '2021-08-18' },
            { employeeId: 81, doj: '2021-08-23' },
            { employeeId: 82, doj: '2021-09-02' },
            { employeeId: 83, doj: '2021-09-09' },
            { employeeId: 84, doj: '2021-09-14' },
            { employeeId: 86, doj: '2021-09-20' },
            { employeeId: 87, doj: '2021-09-20' },
            { employeeId: 88, doj: '2021-09-21' },
            { employeeId: 89, doj: '2021-09-27' },
            { employeeId: 90, doj: '2021-09-27' },
            { employeeId: 91, doj: '2021-09-27' },
            { employeeId: 92, doj: '2021-10-04' },
            { employeeId: 93, doj: '2021-10-08' },
            { employeeId: 996, doj: '2021-09-27' },
            { employeeId: 997, doj: '2021-09-27' },
            { employeeId: 998, doj: '2021-10-11' },
            { employeeId: 999, doj: '2021-10-11' }
        ];

        dojList.map(async obj => {
            return await db.collection('users').updateOne({ employeeId: obj.employeeId }, {
                $set: { doj: new Date(obj.doj) }
            });
        });
    }

};
