import {
  makeSelectPLIRatings,
  makeSelectLoading,
  makeSelectError,
  makeSelectRatingDetail,
  makeSelectDetailLoading,
  makeSelectDetailError,
  makeSelectModalVisible,
  makeSelectCurrentRating,
  makeSelectOverrideLoading,
  makeSelectOverrideError,
  makeSelectFinalizeLoading,
  makeSelectFinalizeError,
  makeSelectProcessingLoading,
  makeSelectProcessingResults,
  makeSelectProcessingError,
  makeSelectResultsModalVisible,
  makeSelectMentorMenteeData,
  makeSelectMentorMenteeLoading,
  makeSelectMentorMenteeError,
} from '../selectors';

describe('PLI Rating Selectors', () => {
  const mockState = {
    pliRating: {
      ratings: [{ id: 1, name: 'Test Rating' }],
      loading: true,
      error: 'Test Error',
      ratingDetail: { id: 1, name: 'Test Detail' },
      detailLoading: true,
      detailError: 'Detail Error',
      modalVisible: true,
      currentRating: { id: 1, name: 'Current Rating' },
      overrideLoading: true,
      overrideError: 'Override Error',
      finalizeLoading: true,
      finalizeError: 'Finalize Error',
      processingLoading: true,
      processingResults: { success: true },
      processingError: 'Processing Error',
      resultsModalVisible: true,
      mentorMenteeData: [{ id: 1, name: 'Mentor 1' }],
      mentorMenteeLoading: true,
      mentorMenteeError: 'Mentor Mentee Error',
    },
  };

  describe('makeSelectPLIRatings', () => {
    it('should select the ratings', () => {
      const selector = makeSelectPLIRatings();
      expect(selector(mockState)).toEqual(mockState.pliRating.ratings);
    });
  });

  describe('makeSelectLoading', () => {
    it('should select the loading state', () => {
      const selector = makeSelectLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.loading);
    });
  });

  describe('makeSelectError', () => {
    it('should select the error', () => {
      const selector = makeSelectError();
      expect(selector(mockState)).toBe(mockState.pliRating.error);
    });
  });

  describe('makeSelectRatingDetail', () => {
    it('should select the rating detail', () => {
      const selector = makeSelectRatingDetail();
      expect(selector(mockState)).toBe(mockState.pliRating.ratingDetail);
    });
  });

  describe('makeSelectDetailLoading', () => {
    it('should select the detail loading state', () => {
      const selector = makeSelectDetailLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.detailLoading);
    });
  });

  describe('makeSelectDetailError', () => {
    it('should select the detail error', () => {
      const selector = makeSelectDetailError();
      expect(selector(mockState)).toBe(mockState.pliRating.detailError);
    });
  });

  describe('makeSelectModalVisible', () => {
    it('should select the modal visibility', () => {
      const selector = makeSelectModalVisible();
      expect(selector(mockState)).toBe(mockState.pliRating.modalVisible);
    });
  });

  describe('makeSelectCurrentRating', () => {
    it('should select the current rating', () => {
      const selector = makeSelectCurrentRating();
      expect(selector(mockState)).toBe(mockState.pliRating.currentRating);
    });
  });

  describe('makeSelectOverrideLoading', () => {
    it('should select the override loading state', () => {
      const selector = makeSelectOverrideLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.overrideLoading);
    });
  });

  describe('makeSelectOverrideError', () => {
    it('should select the override error', () => {
      const selector = makeSelectOverrideError();
      expect(selector(mockState)).toBe(mockState.pliRating.overrideError);
    });
  });

  describe('makeSelectFinalizeLoading', () => {
    it('should select the finalize loading state', () => {
      const selector = makeSelectFinalizeLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.finalizeLoading);
    });
  });

  describe('makeSelectFinalizeError', () => {
    it('should select the finalize error', () => {
      const selector = makeSelectFinalizeError();
      expect(selector(mockState)).toBe(mockState.pliRating.finalizeError);
    });
  });

  describe('makeSelectProcessingLoading', () => {
    it('should select the processing loading state', () => {
      const selector = makeSelectProcessingLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.processingLoading);
    });
  });

  describe('makeSelectProcessingResults', () => {
    it('should select the processing results', () => {
      const selector = makeSelectProcessingResults();
      expect(selector(mockState)).toBe(mockState.pliRating.processingResults);
    });
  });

  describe('makeSelectProcessingError', () => {
    it('should select the processing error', () => {
      const selector = makeSelectProcessingError();
      expect(selector(mockState)).toBe(mockState.pliRating.processingError);
    });
  });

  describe('makeSelectResultsModalVisible', () => {
    it('should select the results modal visibility', () => {
      const selector = makeSelectResultsModalVisible();
      expect(selector(mockState)).toBe(mockState.pliRating.resultsModalVisible);
    });
  });

  describe('makeSelectMentorMenteeData', () => {
    it('should select the mentor mentee data', () => {
      const selector = makeSelectMentorMenteeData();
      expect(selector(mockState)).toBe(mockState.pliRating.mentorMenteeData);
    });
  });

  describe('makeSelectMentorMenteeLoading', () => {
    it('should select the mentor mentee loading state', () => {
      const selector = makeSelectMentorMenteeLoading();
      expect(selector(mockState)).toBe(mockState.pliRating.mentorMenteeLoading);
    });
  });

  describe('makeSelectMentorMenteeError', () => {
    it('should select the mentor mentee error', () => {
      const selector = makeSelectMentorMenteeError();
      expect(selector(mockState)).toBe(mockState.pliRating.mentorMenteeError);
    });
  });
});
