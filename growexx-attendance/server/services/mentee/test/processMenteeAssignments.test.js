const chai = require("chai");
const chaiHttp = require("chai-http");
const request = require("supertest");
const app = require("../../../server");
const User = require("../../../models/user.model");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");
const { expect } = chai;

chai.use(chaiHttp);

const tokenOptionalInfo = {
  algorithm: "HS256",
  expiresIn: 86400,
};

// Test helper constants
const TEST_ADMIN = {
  email: "<EMAIL>",
  password: "Test@123",
  firstName: "Test",
  lastName: "Admin",
  role: 4,
  employeeId: 9001,
  label: ["admin"],
  designation: "Director",
  level: "L1",
  businessUnit: "Management",
  dateOfJoining: "2024-01-01",
  isActive: 1,
};

const TEST_MENTOR = {
  email: "<EMAIL>",
  password: "Test@123",
  firstName: "Test",
  lastName: "Mentor",
  role: 2,
  employeeId: 9002,
  label: ["mentor"],
  designation: "Senior Developer",
  level: "L2",
  businessUnit: "Engineering",
  dateOfJoining: "2024-01-01",
  isActive: 1,
};

const TEST_MENTEE1 = {
  email: "<EMAIL>",
  password: "Test@123",
  firstName: "Test",
  lastName: "Mentee1",
  role: 1,
  employeeId: 9003,
  label: ["mentee"],
  designation: "Junior Developer",
  level: "L3",
  businessUnit: "Engineering",
  dateOfJoining: "2024-01-01",
  isActive: 1,
};

const TEST_MENTEE2 = {
  email: "<EMAIL>",
  password: "Test@123",
  firstName: "Test",
  lastName: "Mentee2",
  role: 1,
  employeeId: 9004,
  label: ["mentee"],
  designation: "Junior Developer",
  level: "L3",
  businessUnit: "Engineering",
  dateOfJoining: "2024-01-01",
  isActive: 1,
};

describe("Process Mentee Assignments", () => {
  let adminToken;
  let mentorId;
  let mentee1Id;
  let mentee2Id;

  before(async () => {
    // Clean up any existing test users first
    await User.deleteMany({
      email: {
        $in: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
    });

    // Create test users
    const admin = await User.create(TEST_ADMIN);
    const mentor = await User.create(TEST_MENTOR);
    const mentee1 = await User.create(TEST_MENTEE1);
    const mentee2 = await User.create(TEST_MENTEE2);

    // Store IDs for later use
    mentorId = mentor._id.toString();
    mentee1Id = mentee1._id.toString();
    mentee2Id = mentee2._id.toString();

    // Generate admin token
    adminToken = jwt.sign(
      {
        id: admin._id,
        email: admin.email,
        role: admin.role,
      },
      process.env.JWT_SECRET,
      tokenOptionalInfo
    );
  });

  after(async () => {
    // Clean up test users
    await User.deleteMany({
      email: {
        $in: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
    });
  });

  describe("POST /user/mentees/process-assignments", () => {
    it("should process valid mentees and skip invalid ones", async () => {
      const assignmentData = [
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>", // This email doesn't exist in the organization
          ],
        },
      ];

      const res = await request(app)
        .post("/user/mentees/process-assignments")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(assignmentData);

      expect(res.status).to.equal(200);
      expect(res.body.status).to.equal(1);
      expect(res.body.data).to.have.property("totalRows").to.equal(1);
      expect(res.body.data)
        .to.have.property("successfulAssignments")
        .to.be.at.least(1);
      expect(res.body.data)
        .to.have.property("partiallyProcessedRows")
        .to.equal(1);
      expect(res.body.data)
        .to.have.property("skippedEmails")
        .to.include("<EMAIL>");

      // Verify mentees were assigned to mentor
      const mentee1 = await User.findById(mentee1Id);
      const mentee2 = await User.findById(mentee2Id);

      expect(mentee1.mentorId.toString()).to.equal(mentorId);
      expect(mentee2.mentorId.toString()).to.equal(mentorId);
    });

    it("should skip rows where all mentees are invalid", async () => {
      const assignmentData = [
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": ["<EMAIL>", "<EMAIL>"],
        },
      ];

      const res = await request(app)
        .post("/user/mentees/process-assignments")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(assignmentData);

      expect(res.status).to.equal(200);
      expect(res.body.status).to.equal(1);
      expect(res.body.data).to.have.property("totalRows").to.equal(1);
      expect(res.body.data).to.have.property("skippedRows").to.equal(1);
      expect(res.body.data)
        .to.have.property("skippedEmails")
        .to.include.members(["<EMAIL>", "<EMAIL>"]);
    });

    it("should skip rows with invalid mentor emails", async () => {
      const assignmentData = [
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": ["<EMAIL>", "<EMAIL>"],
        },
      ];

      const res = await request(app)
        .post("/user/mentees/process-assignments")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(assignmentData);

      expect(res.status).to.equal(200);
      expect(res.body.status).to.equal(1);
      expect(res.body.data).to.have.property("totalRows").to.equal(1);
      expect(res.body.data).to.have.property("skippedRows").to.equal(1);
      expect(res.body.data)
        .to.have.property("skippedEmails")
        .to.include("<EMAIL>");
    });

    it("should handle multiple rows with mixed valid and invalid mentees", async () => {
      // First reset mentee assignments
      await User.updateMany(
        { _id: { $in: [mentee1Id, mentee2Id] } },
        { $unset: { mentorId: 1 } }
      );

      const assignmentData = [
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": ["<EMAIL>", "<EMAIL>"],
        },
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": ["<EMAIL>"],
        },
        {
          "Mentor email": "<EMAIL>",
          "Mentee email": ["<EMAIL>", "<EMAIL>"],
        },
      ];

      const res = await request(app)
        .post("/user/mentees/process-assignments")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(assignmentData);

      expect(res.status).to.equal(200);
      expect(res.body.status).to.equal(1);
      expect(res.body.data).to.have.property("totalRows").to.equal(3);
      expect(res.body.data)
        .to.have.property("partiallyProcessedRows")
        .to.be.at.least(1);
      expect(res.body.data).to.have.property("skippedRows").to.be.at.least(1);

      // Verify mentee1 was assigned to mentor
      const mentee1 = await User.findById(mentee1Id);
      expect(mentee1.mentorId.toString()).to.equal(mentorId);
    });
  });
});
