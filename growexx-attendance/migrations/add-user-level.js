module.exports = {
    async up (db) {
        const levelList =
            [
                { employeeId: 5, level: 'L3' },
                { employeeId: 8, level: 'L2' },
                { employeeId: 9, level: 'L3' },
                { employeeId: 11, level: 'L3' },
                { employeeId: 15, level: 'L3' },
                { employeeId: 19, level: 'L3' },
                { employeeId: 20, level: 'L4' },
                { employeeId: 21, level: 'L3' },
                { employeeId: 22, level: 'L3' },
                { employeeId: 25, level: 'L2' },
                { employeeId: 26, level: 'L5' },
                { employeeId: 28, level: 'L2' },
                { employeeId: 30, level: 'L2' },
                { employeeId: 31, level: 'L3' },
                { employeeId: 32, level: 'L2' },
                { employeeId: 33, level: 'L2' },
                { employeeId: 34, level: 'L3' },
                { employeeId: 35, level: 'L2' },
                { employeeId: 37, level: 'L2' },
                { employeeId: 40, level: 'L1' },
                { employeeId: 42, level: 'L1' },
                { employeeId: 46, level: 'L5' },
                { employeeId: 49, level: 'L3' },
                { employeeId: 50, level: 'L4' },
                { employeeId: 53, level: 'L2' },
                { employeeId: 55, level: 'L3' },
                { employeeId: 56, level: 'L2' },
                { employeeId: 57, level: 'L4' },
                { employeeId: 62, level: 'L2' },
                { employeeId: 64, level: 'L1' },
                { employeeId: 67, level: 'L2' },
                { employeeId: 71, level: 'L1' },
                { employeeId: 72, level: 'L1' },
                { employeeId: 73, level: 'L2' },
                { employeeId: 75, level: 'L2' },
                { employeeId: 76, level: 'L1' },
                { employeeId: 80, level: 'L1' },
                { employeeId: 83, level: 'L2' },
                { employeeId: 84, level: 'L2' },
                { employeeId: 86, level: 'L1' },
                { employeeId: 87, level: 'L1' },
                { employeeId: 88, level: 'L1' },
                { employeeId: 89, level: 'L1' },
                { employeeId: 90, level: 'L1' },
                { employeeId: 96, level: 'L1' },
                { employeeId: 100, level: 'L2' },
                { employeeId: 101, level: 'L2' },
                { employeeId: 102, level: 'L1' },
                { employeeId: 104, level: 'L4' },
                { employeeId: 108, level: 'L2' },
                { employeeId: 113, level: 'L1' },
                { employeeId: 118, level: 'L1' },
                { employeeId: 119, level: 'L1' },
                { employeeId: 121, level: 'L0' },
                { employeeId: 124, level: 'L1' },
                { employeeId: 125, level: 'L1' },
                { employeeId: 126, level: 'L2' },
                { employeeId: 128, level: 'L1' },
                { employeeId: 129, level: 'L0' },
                { employeeId: 130, level: 'L0' },
                { employeeId: 131, level: 'L0' },
                { employeeId: 132, level: 'L0' },
                { employeeId: 133, level: 'L0' },
                { employeeId: 134, level: 'L0' },
                { employeeId: 135, level: 'L0' },
                { employeeId: 136, level: 'L0' },
                { employeeId: 137, level: 'L0' },
                { employeeId: 138, level: 'L0' },
                { employeeId: 139, level: 'L0' },
                { employeeId: 140, level: 'L0' },
                { employeeId: 141, level: 'L0' },
                { employeeId: 142, level: 'L0' },
                { employeeId: 143, level: 'L0' },
                { employeeId: 144, level: 'L0' },
                { employeeId: 145, level: 'L1' },
                { employeeId: 146, level: 'L1' },
                { employeeId: 148, level: 'L3' },
                { employeeId: 150, level: 'L2' },
                { employeeId: 152, level: 'L1' },
                { employeeId: 153, level: 'L1' },
                { employeeId: 154, level: 'L1' },
                { employeeId: 155, level: 'L1' },
                { employeeId: 156, level: 'L3' },
                { employeeId: 159, level: 'L1' },
                { employeeId: 160, level: 'L1' },
                { employeeId: 161, level: 'L2' },
                { employeeId: 162, level: 'L1' },
                { employeeId: 164, level: 'L0' },
                { employeeId: 165, level: 'L2' },
                { employeeId: 166, level: 'L1' },
                { employeeId: 167, level: 'L2' },
                { employeeId: 168, level: 'L0' },
                { employeeId: 169, level: 'L2' },
                { employeeId: 170, level: 'L3' },
                { employeeId: 171, level: 'L1' },
                { employeeId: 172, level: 'L6' },
                { employeeId: 173, level: 'L1' },
                { employeeId: 174, level: 'L1' },
                { employeeId: 175, level: 'L2' },
                { employeeId: 176, level: 'L2' },
                { employeeId: 177, level: 'L1' },
                { employeeId: 178, level: 'L1' },
                { employeeId: 179, level: 'L1' },
                { employeeId: 180, level: 'L1' },
                { employeeId: 181, level: 'L3' },
                { employeeId: 182, level: 'L1' },
                { employeeId: 183, level: 'L1' },
                { employeeId: 185, level: 'L1' },
                { employeeId: 186, level: 'L2' },
                { employeeId: 187, level: 'L2' },
                { employeeId: 188, level: 'L2' },
                { employeeId: 189, level: 'L1' },
                { employeeId: 190, level: 'L2' },
                { employeeId: 191, level: 'L1' },
                { employeeId: 192, level: 'L1' },
                { employeeId: 194, level: 'L1' }
            ];

        levelList.map(async obj => {
            return await db.collection('users').updateOne({ employeeId: obj.employeeId }, {
                $set: { level: obj.level }
            });
        });

    }
};
