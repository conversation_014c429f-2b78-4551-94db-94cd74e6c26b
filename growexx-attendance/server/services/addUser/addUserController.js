
const AddUserService = require('./addUserService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add a new user
 */
class AddUserController {

    /**
     * @desc This function is being used to add a new user
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addUser (req, res) {
        try {
            const data = await AddUserService.addUser(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddUserController;
