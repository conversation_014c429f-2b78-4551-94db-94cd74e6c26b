/**
 * @name epic model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const constants = require('../util/constants');

const schema = new appMongoose.Schema({
    boardId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'projectBoard'
    },
    sprintId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'sprint'
    },
    jiraEpicId: {
        type: String
    },
    jiraEpicUrl: {
        type: String
    },
    name: {
        type: String
    },
    summary: {
        type: String
    },
    status: {
        type: String
    },
    key: {
        type: String
    },
    description: {
        type: String
    },
    salesEstimate: {
        type: Number
    },
    belongsTo: {
        type: String,
        enum: Object.values(constants.PROJECT_TRACKER.USER_TYPE)
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('epic', schema);
