const FreezePliRatingService = require('./freezePliRatingService');
const Utils = require('../../util/utilFunctions');
const logger = require('../../util/logger');

/**
 * Class represents controller for freezing PLI rating
 */
class FreezePliRatingController {
    /**
   * @desc This function is being used to freeze a PLI rating
   * <AUTHOR>
   * @since 27/05/2025
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
    static async freezePliRating (req, res) {
        try {
            const data = await FreezePliRatingService.freezePliRating(
                req,
                res.__
            );
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            logger.error(`Error in freezePliRating: ${error.message}`);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to accept a frozen PLI rating by mentee
   * <AUTHOR>
   * @since 27/05/2025
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
    static async acceptPliRating (req, res) {
        try {
            const data = await FreezePliRatingService.acceptPliRating(
                req,
                res.__
            );
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            logger.error(`Error in acceptPliRating: ${error.message}`);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = FreezePliRatingController;
