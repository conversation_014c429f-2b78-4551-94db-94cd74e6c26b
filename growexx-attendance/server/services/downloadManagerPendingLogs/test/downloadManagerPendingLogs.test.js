const chai = require('chai');
const expect = chai.expect;
const assert = chai.assert;

// Load environment variables for testing
const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: __dirname + '/../../../../' + env + '.env' });

describe('Download managers project-wise logs report', () => {
    describe('Service Unit Tests', () => {
        it('Should format hours correctly - whole numbers', () => {
            // Test helper function directly
            const formatHours = (hours) => {
                if (hours === 0) return '0h';
                const formatted = parseFloat(hours).toString();
                return formatted.replace(/\.?0+$/, '') + 'h';
            };
            
            assert.equal(formatHours(0), '0h');
            assert.equal(formatHours(1), '1h');
            assert.equal(formatHours(8), '8h');
            assert.equal(formatHours(16), '16h');
        });

        it('Should format hours correctly - decimal numbers', () => {
            const formatHours = (hours) => {
                if (hours === 0) return '0h';
                const formatted = parseFloat(hours).toString();
                return formatted.replace(/\.?0+$/, '') + 'h';
            };
            
            assert.equal(formatHours(8.5), '8.5h');
            assert.equal(formatHours(16.25), '16.25h');
            assert.equal(formatHours(7.75), '7.75h');
        });

        it('Should format hours correctly - remove trailing zeros', () => {
            const formatHours = (hours) => {
                if (hours === 0) return '0h';
                const formatted = parseFloat(hours).toString();
                return formatted.replace(/\.?0+$/, '') + 'h';
            };
            
            assert.equal(formatHours(8.0), '8h');
            assert.equal(formatHours(16.00), '16h');
            assert.equal(formatHours(7.50), '7.5h');
            assert.equal(formatHours(12.10), '12.1h');
        });

        it('Should handle edge cases in hour formatting', () => {
            const formatHours = (hours) => {
                if (hours === 0) return '0h';
                const formatted = parseFloat(hours).toString();
                return formatted.replace(/\.?0+$/, '') + 'h';
            };
            
            assert.equal(formatHours(0.1), '0.1h');
            assert.equal(formatHours(0.01), '0.01h');
            assert.equal(formatHours(23.99), '23.99h');
        });

        it('Should generate proper CSV headers', () => {
            const headers = [
                { key: 'Content-Type', value: 'text/csv' },
                { key: 'Content-Disposition', value: 'attachment; filename="managers-project-wise-logs-report-2021-03-01-to-2021-03-31.csv"' }
            ];

            const contentTypeHeader = headers.find(h => h.key === 'Content-Type');
            const dispositionHeader = headers.find(h => h.key === 'Content-Disposition');
            
            assert.equal(contentTypeHeader.value, 'text/csv');
            assert.include(dispositionHeader.value, 'managers-project-wise-logs-report');
            assert.include(dispositionHeader.value, '2021-03-01-to-2021-03-31.csv');
        });

        it('Should exclude weekends from date range', () => {
            const getWeekdaysInRange = (startDate, endDate) => {
                const dates = [];
                const currentDate = new Date(startDate);
                const end = new Date(endDate);
                
                while (currentDate <= end) {
                    const dayOfWeek = currentDate.getDay();
                    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
                        dates.push(currentDate.toISOString().split('T')[0]);
                    }
                    currentDate.setDate(currentDate.getDate() + 1);
                }
                return dates;
            };

            const weekdays = getWeekdaysInRange('2021-03-06', '2021-03-08'); // Saturday to Monday
            
            assert.include(weekdays, '2021-03-08'); // Monday
            assert.notInclude(weekdays, '2021-03-06'); // Saturday
            assert.notInclude(weekdays, '2021-03-07'); // Sunday
        });

        it('Should format hours display text correctly', () => {
            const formatHoursDisplay = (approvedHours, notApprovedHours) => {
                const formatHours = (hours) => {
                    if (hours === 0) return '0h';
                    const formatted = parseFloat(hours).toString();
                    return formatted.replace(/\.?0+$/, '') + 'h';
                };

                const parts = [];
                if (approvedHours > 0) {
                    parts.push(`${formatHours(approvedHours)} - Approved`);
                }
                if (notApprovedHours > 0) {
                    parts.push(`${formatHours(notApprovedHours)} - Not Approved`);
                }
                return parts.length > 0 ? parts.join(', ') : '-';
            };

            assert.equal(formatHoursDisplay(8, 0), '8h - Approved');
            assert.equal(formatHoursDisplay(0, 4), '4h - Not Approved');
            assert.equal(formatHoursDisplay(4, 4), '4h - Approved, 4h - Not Approved');
            assert.equal(formatHoursDisplay(0, 0), '-');
            assert.equal(formatHoursDisplay(8.5, 2.5), '8.5h - Approved, 2.5h - Not Approved');
        });

        it('Should create proper CSV row data', () => {
            const mockManagerData = {
                managerName: 'John Doe',
                projectName: 'Test Project',
                dateWiseData: {
                    '2021-03-01': { approvedHours: 8, notApprovedHours: 0 },
                    '2021-03-02': { approvedHours: 4, notApprovedHours: 4 }
                }
            };

            const weekdays = ['2021-03-01', '2021-03-02', '2021-03-03', '2021-03-04', '2021-03-05'];
            
            const formatHours = (hours) => {
                if (hours === 0) return '0h';
                const formatted = parseFloat(hours).toString();
                return formatted.replace(/\.?0+$/, '') + 'h';
            };

            const formatHoursDisplay = (approvedHours, notApprovedHours) => {
                const parts = [];
                if (approvedHours > 0) {
                    parts.push(`${formatHours(approvedHours)} - Approved`);
                }
                if (notApprovedHours > 0) {
                    parts.push(`${formatHours(notApprovedHours)} - Not Approved`);
                }
                return parts.length > 0 ? parts.join(', ') : '-';
            };

            const row = [
                mockManagerData.managerName,
                mockManagerData.projectName
            ];

            // Add date columns
            weekdays.forEach(date => {
                const dateData = mockManagerData.dateWiseData[date];
                if (dateData) {
                    row.push(formatHoursDisplay(dateData.approvedHours, dateData.notApprovedHours));
                } else {
                    row.push('-');
                }
            });

            // Calculate totals
            let totalApproved = 0;
            let totalNotApproved = 0;
            Object.values(mockManagerData.dateWiseData).forEach(data => {
                totalApproved += data.approvedHours;
                totalNotApproved += data.notApprovedHours;
            });

            row.push(formatHours(totalApproved));
            row.push(formatHours(totalNotApproved));

            assert.equal(row[0], 'John Doe');
            assert.equal(row[1], 'Test Project');
            assert.equal(row[2], '8h - Approved');
            assert.equal(row[3], '4h - Approved, 4h - Not Approved');
            assert.equal(row[4], '-'); // No data for 2021-03-03
            assert.equal(row[5], '-'); // No data for 2021-03-04
            assert.equal(row[6], '-'); // No data for 2021-03-05
            assert.equal(row[7], '12h'); // Total approved
            assert.equal(row[8], '4h');  // Total not approved
        });

        it('Should generate correct CSV structure for empty data', () => {
            const weekdays = ['2021-03-01', '2021-03-02', '2021-03-03', '2021-03-04', '2021-03-05'];
            
            const headers = ['Manager Name', 'Project Name'];
            weekdays.forEach(date => headers.push(date));
            headers.push('Total Approved Hours');
            headers.push('Total Not Approved Hours');

            const csvHeader = headers.join(',');
            const csvContent = 'No managers with log data found for the selected date range.';
            const fullCsv = csvHeader + '\n' + csvContent;

            assert.include(fullCsv, 'Manager Name,Project Name');
            assert.include(fullCsv, 'Total Approved Hours');
            assert.include(fullCsv, 'Total Not Approved Hours');
            assert.include(fullCsv, 'No managers with log data found');
        });

        it('Should generate error CSV when exception occurs', () => {
            const headers = ['Error', 'Message'];
            const errorMessage = 'Test database error';
            const csvContent = `Error occurred while generating report,"${errorMessage}"`;
            const fullCsv = headers.join(',') + '\n' + csvContent;

            assert.include(fullCsv, 'Error,Message');
            assert.include(fullCsv, 'Error occurred while generating report');
            assert.include(fullCsv, 'Test database error');
        });
    });
}); 