const express = require("express");
const router = express.Router();
const multer = require("multer");
const upload = multer({ storage: multer.memoryStorage() });
const AuthMiddleWare = require("../middleware/auth");
const ACLMiddleWare = require("../middleware/acl");
const EmployeeProfileController = require("../services/employeeProfile/employeeProfileController");
const AllEmployeePliController = require("../services/allEmployeePli/allEmployeePliController");
const { uploadPliFile, getPliFiles } = EmployeeProfileController;

// Route to get employee profile data for PLI
router.get(
  "/employee-profile/:employeeId",
  AuthMiddleWare,
  ACLMiddleWare,
  EmployeeProfileController.getEmployeeProfileForPli
);

// Route to get all employees PLI data
router.get(
  "/all-employee-profile",
  AuthMiddleWare,
  ACLMiddleWare,
  AllEmployeePliController.getAllEmployeeProfile
);

// File upload route
router.post("/upload/:employeeId", upload.single("file"), uploadPliFile);

// Get files route
router.get("/files/:employeeId", getPliFiles);

module.exports = router;
