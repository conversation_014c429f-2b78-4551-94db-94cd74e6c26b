module.exports = {
    assessKra: [
        {
            it: 'As a admin, I should validate if selfAssessmentEndDate is not present in request parameter then it should fail',
            options: {
                'selfAssessmentEndDate': ''
            },
            status: 400
        }, {
            it: 'As a admin, I should validate if selfAssessmentEndDate is less then current date then it should fail',
            options: {
                'selfAssessmentEndDate': '2022-06-02'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if userKraId is not passed',
            options: {

            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if userKraId is not empty',
            options: {
                'userKraId': []

            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if userKraId not contains valid ids',
            options: {
                'userKraId': ['62fc9b1fac61859575e', '62f3658fa47fe7aba7cec8c9']

            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if selfassessment end date is not pass ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9']

            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if selfassessment end date is not future date ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9'],
                'selfAssessmentEndDate': '2023-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if reportingManagerAssessmentEndDate end date is not pass ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9'],
                'selfAssessmentEndDate': '2023-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if reportingManagerAssessmentEndDate end date is not future date ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9'],
                'reportingManagerAssessmentEndDate': '2015-10-24',
                'selfAssessmentEndDate': '2023-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if reviewManagerAssessmentEndDate end date is not pass ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9'],
                'selfAssessmentEndDate': '2023-10-24',
                'reportingManagerAssessmentEndDate': '2025-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate if reviewManagerAssessmentEndDate end date is not future date ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710', '62f3658fa47fe7aba7cec8c9'],
                'reportingManagerAssessmentEndDate': '2025-10-24',
                'selfAssessmentEndDate': '2023-10-24',
                'reviewManagerAssessmentEndDate': '2015-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate that kras are assigned to user ',
            options: {
                'userKraId': ['62fc9b1fac61859575e7c710'],
                'reportingManagerAssessmentEndDate': '2025-10-24',
                'selfAssessmentEndDate': '2023-10-24',
                'reviewManagerAssessmentEndDate': '2025-10-24'
            },
            status: 400
        },
        {
            it: 'As a admin, I should start assessment for selected kras ',
            options: {
                'userKraId': ['62cd5fb5e123d78488b32a40'],
                'reportingManagerAssessmentEndDate': MOMENT().add(1, 'year').add(10, 'days').format('YYYY-MM-DD'),
                'selfAssessmentEndDate': MOMENT().add(1, 'year').format('YYYY-MM-DD'),
                'reviewManagerAssessmentEndDate': MOMENT().add(1, 'year').add(20, 'days').format('YYYY-MM-DD')
            },
            status: 200
        }
    ]
};
