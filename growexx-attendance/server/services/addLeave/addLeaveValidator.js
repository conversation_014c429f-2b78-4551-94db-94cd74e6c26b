const validation = require('../../util/validation');

/**
 * Class represents validations for Jira portal add.
 */
class AddUserLeaveValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate Jira portal add
     * <AUTHOR>
     * @since 18/03/2021
     */
    validationAddUserLeave () {
        super.leaveType(this.body.leaveType, 'Leave type' );
        super.userId(this.body.employeeId, 'Employee id');
        super.leaveDate(this.body.startDate, 'Start date');
        super.leaveDate(this.body.endDate, 'End date');
    }
}

module.exports = AddUserLeaveValidator;
