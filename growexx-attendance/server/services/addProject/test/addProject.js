module.exports = {
    addProject: [{
        it: 'As a admin, I should validate if project name is not pass',
        options: {
            projectName: '',
            startDate: '2022-07-06',
            endDate: '2022-08-06',
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'
        },
        status: 0
    }, {
        it: 'As a admin, I should validate if project start & end date is not passed',
        options: {
            projectName: 'appy',
            startDate: '',
            endDate: '',
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'
        },
        status: 0
    },
    {
        it: 'As a admin, I should validate if project startdate is not passed',
        options: {
            projectName: 'appy',
            startDate: '',
            endDate: null,
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'
        },
        status: 0
    },
    {
        it: 'As a admin, I should validate if project startdate is not valid',
        options: {
            projectName: 'appy',
            startDate: 'date',
            endDate: null,
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'
        },
        status: 0
    }, {
        it: 'As a admin, I should validate if project name is duplicate',
        options: {
            projectName: 'Project 1',
            startDate: '2022-07-06',
            endDate: '2022-08-06',
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'

        },
        status: 0
    }, {
        it: 'As a admin, I should validate if start date is greater than end date then it should throw error ',
        options: {
            projectName: 'Project start new',
            startDate: '2022-07-06',
            endDate: '2022-05-06',
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '5f083c352a7908662c335551'
        },
        status: 0
    }]
};
