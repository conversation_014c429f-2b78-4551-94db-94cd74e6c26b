module.exports = {
    // Apply the migration
    async up(db) {
      const logsCollection = db.collection("logs");
  
      console.log("Adding actionFlag, actionBy, and actionOn fields to logs...");
  
      await logsCollection.updateMany(
        {},
        {
          $set: {
            logStatus: null,
            lastActionPerformedBy: null,
            lastActionPerformedTimestamp: null 
          }
        }
      );
  
      console.log("Migration completed: New fields added to logs collection.");
    },
  
    // Rollback the migration
    async down(db) {
      const logsCollection = db.collection("logs");
  
      console.log("Removing actionFlag, actionBy, and actionOn fields from logs...");
  
      await logsCollection.updateMany(
        {},
        {
          $unset: {
            logStatus: "", // Removes the field
            lastActionPerformedBy: "",   // Removes the field
            lastActionPerformedTimestamp: ""    // Removes the field
          }
        }
      );
  
      console.log("Rollback completed: Fields removed from logs collection.");
    }
  };
  