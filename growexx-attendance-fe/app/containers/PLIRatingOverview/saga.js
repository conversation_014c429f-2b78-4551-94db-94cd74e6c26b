/**
 * PLIRating sagas
 */

import { call, put, takeLatest } from 'redux-saga/effects';
import { notification } from 'antd';
import request from 'utils/request';
import { API_ENDPOINTS } from '../constants';
import {
  fetchPLIRatingsSuccess,
  fetchPLIRatingsError,
  fetchPLIRatingDetailSuccess,
  fetchPLIRatingDetailError,
  overridePLIRatingSuccess,
  overridePLIRatingError,
  finalizePLIRatingSuccess,
  finalizePLIRatingError,
  processBulkAssignmentsSuccess,
  processBulkAssignmentsError,
  fetchMentorMenteeDataSuccess,
  fetchMentorMenteeDataError,
} from './actions';
import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATING_DETAIL,
  OVERRIDE_PLI_RATING,
  FINALIZE_PLI_RATING,
  PROCESS_BULK_ASSIGNMENTS,
  FETCH_MENTOR_MENTEE_DATA,
} from './constants';

/**
 * Fetch PLI ratings saga
 * @param {Object} action - Action object
 */
export function* fetchPLIRatingsSaga(action) {
  try {
    // Construct URL for the base request
    const url = API_ENDPOINTS.ALL_EMPLOYEE_PLI;

    // Make API call
    const response = yield call(request, url, {
      method: 'GET',
    });

    if (response.status === 1) {
      let filteredData = response.data || [];

      // Apply client-side filtering since the API doesn't support query parameters
      if (action.params) {
        const { department, status, cycleMonth, search } = action.params;

        // Filter by department
        if (department) {
          filteredData = filteredData.filter(
            item => item.department === department,
          );
        }

        // Filter by status
        if (status) {
          filteredData = filteredData.filter(item => item.status === status);
        }

        // Filter by pliCycle (month)
        if (cycleMonth && cycleMonth !== 'All') {
          filteredData = filteredData.filter(
            item => item.pliCycle === cycleMonth,
          );
        }

        // Filter by search term
        if (search) {
          const searchLower = search.toLowerCase().trim();
          const searchNum = Number(search);
          const isSearchNumber = !Number.isNaN(searchNum);

          filteredData = filteredData.filter(item => {
            // Name search (case insensitive contains)
            const nameMatch =
              item.name && item.name.toLowerCase().includes(searchLower);

            // Employee ID search (exact match if search is a number)
            let empIdMatch = false;
            if (isSearchNumber) {
              empIdMatch = item.employeeId === searchNum;
            } else if (item.employeeId) {
              empIdMatch = item.employeeId.toString().includes(search);
            }

            // Department search (case insensitive contains)
            const deptMatch =
              item.department &&
              item.department.toLowerCase().includes(searchLower);

            return nameMatch || empIdMatch || deptMatch;
          });
        }
      }

      yield put(fetchPLIRatingsSuccess(filteredData));
    } else {
      throw new Error(response.message || 'Failed to fetch PLI ratings');
    }
  } catch (err) {
    yield put(fetchPLIRatingsError(err));
    notification.error({
      message: 'Error',
      description: err.message || 'Failed to fetch PLI ratings',
    });
  }
}

/**
 * Fetch PLI rating detail saga
 * @param {Object} action - Action object with id
 */
export function* fetchPLIRatingDetailSaga(action) {
  try {
    const url = API_ENDPOINTS.PLI_RATING_DETAIL.replace(':id', action.id);

    // Make API call
    const response = yield call(request, url, {
      method: 'GET',
    });

    if (response.success) {
      yield put(fetchPLIRatingDetailSuccess(response.data || null));
    } else {
      throw new Error(response.message || 'Failed to fetch PLI rating detail');
    }
  } catch (err) {
    yield put(fetchPLIRatingDetailError(err));
    notification.error({
      message: 'Error',
      description: err.message || 'Failed to fetch PLI rating detail',
    });
  }
}

/**
 * Override PLI rating saga
 * @param {Object} action - Action object with id and data
 */
export function* overridePLIRatingSaga(action) {
  try {
    const url = API_ENDPOINTS.PLI_RATING_DETAIL.replace(':id', action.id);

    // Make API call
    const response = yield call(request, url, {
      method: 'PATCH',
      body: JSON.stringify({
        superAdminOverride: true,
        superAdminComments: action.data.comments,
        finalScore: action.data.score,
      }),
    });

    if (response.success) {
      yield put(overridePLIRatingSuccess(response.data || null));
      notification.success({
        message: 'Success',
        description: 'PLI rating overridden successfully',
      });
    } else {
      throw new Error(response.message || 'Failed to override PLI rating');
    }
  } catch (err) {
    yield put(overridePLIRatingError(err));
    notification.error({
      message: 'Error',
      description: err.message || 'Failed to override PLI rating',
    });
  }
}

/**
 * Finalize PLI rating saga
 * @param {Object} action - Action object with id
 */
export function* finalizePLIRatingSaga(action) {
  try {
    const url = API_ENDPOINTS.PLI_RATING_FINALIZE.replace(':id', action.id);

    // Make API call
    const response = yield call(request, url, {
      method: 'PATCH',
    });

    if (response.success) {
      yield put(finalizePLIRatingSuccess(response.data || null));
      notification.success({
        message: 'Success',
        description: 'PLI rating finalized successfully',
      });
    } else {
      throw new Error(response.message || 'Failed to finalize PLI rating');
    }
  } catch (err) {
    yield put(finalizePLIRatingError(err));
    notification.error({
      message: 'Error',
      description: err.message || 'Failed to finalize PLI rating',
    });
  }
}

/**
 * Process bulk assignments saga
 * @param {Object} action - Action object with data
 */
export function* processBulkAssignmentsSaga(action) {
  try {
    const response = yield call(
      request,
      API_ENDPOINTS.PROCESS_BULK_ASSIGNMENTS,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action.data),
      },
    );

    if (response.status === 1) {
      yield put(processBulkAssignmentsSuccess(response.data));
      notification.success({
        message: 'Success',
        description:
          response.message || 'Bulk assignments processed successfully',
      });

      // Trigger refetch of mentor-mentee data after successful bulk assignment
      yield put({ type: FETCH_MENTOR_MENTEE_DATA });
    } else {
      throw new Error(response.message || 'Failed to process bulk assignments');
    }
  } catch (err) {
    yield put(processBulkAssignmentsError(err));
    notification.error({
      message: 'Error',
      description: err.message || 'Failed to process bulk assignments',
    });
  }
}

/**
 * Fetch mentor-mentee data saga
 */
function* fetchMentorMenteeData() {
  try {
    const response = yield call(
      request,
      API_ENDPOINTS.FETCH_MENTOR_MENTEE_DATA,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (response.status === 1) {
      yield put(fetchMentorMenteeDataSuccess(response.data));
    } else {
      yield put(fetchMentorMenteeDataError(response.message));
    }
  } catch (error) {
    yield put(fetchMentorMenteeDataError(error.message));
  }
}

/**
 * Root saga for PLIRating
 */
export default function* pliRatingSaga() {
  yield takeLatest(FETCH_PLI_RATINGS, fetchPLIRatingsSaga);
  yield takeLatest(FETCH_PLI_RATING_DETAIL, fetchPLIRatingDetailSaga);
  yield takeLatest(OVERRIDE_PLI_RATING, overridePLIRatingSaga);
  yield takeLatest(FINALIZE_PLI_RATING, finalizePLIRatingSaga);
  yield takeLatest(PROCESS_BULK_ASSIGNMENTS, processBulkAssignmentsSaga);
  yield takeLatest(FETCH_MENTOR_MENTEE_DATA, fetchMentorMenteeData);
}
