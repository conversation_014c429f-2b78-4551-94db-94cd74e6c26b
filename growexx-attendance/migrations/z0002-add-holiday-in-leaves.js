module.exports = {
    async up (db) {
        const holidays = [
            new Date('2021-08-15'),
            new Date('2021-10-02'),
            new Date('2021-11-04'),
            new Date('2021-11-05'),
            new Date('2021-11-06'),
            new Date('2022-01-14'),
            new Date('2022-01-26')
        ];
        const users = await db.collection('users').find({ }).toArray();
        const leaves = [];
        for (const user of users) {
            for (const holiday of holidays) {
                leaves.push({
                    userId: user._id,
                    leaveDate: holiday,
                    startDate: holiday,
                    endDate: holiday,
                    leaveType: 'Holiday',
                    timeSpentHours: 8,
                    duration: 'full',
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        }
        await db.collection('leaves').insertMany(leaves);
    }
};
