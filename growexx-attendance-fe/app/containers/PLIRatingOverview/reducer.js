/**
 * PLIRating reducer
 */

import produce from 'immer';
import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATINGS_SUCCESS,
  <PERSON>ETCH_PLI_RATINGS_ERROR,
  <PERSON>ETCH_PLI_RATING_DETAIL,
  <PERSON><PERSON><PERSON>_PLI_RATING_DETAIL_SUCCESS,
  <PERSON>ETCH_PLI_RATING_DETAIL_ERROR,
  OVERRIDE_PLI_RATING,
  OVERRIDE_PLI_RATING_SUCCESS,
  OVERRIDE_PLI_RATING_ERROR,
  FINALIZE_PLI_RATING,
  FINALIZE_PLI_RATING_SUCCESS,
  FINALIZE_PLI_RATING_ERROR,
  SHOW_MODAL,
  HIDE_MODAL,
  PROCESS_BULK_ASSIGNMENTS,
  PROCESS_BULK_ASSIGNMENTS_SUCCESS,
  PROCESS_BULK_ASSIGNMENTS_ERROR,
  SHOW_RESULTS_MODAL,
  HIDE_RESULTS_MODAL,
  <PERSON>ETCH_MENTOR_MENTEE_DATA,
  <PERSON>ETCH_MENTOR_MENTEE_DATA_SUCCESS,
  <PERSON><PERSON>CH_MENTOR_MENTEE_DATA_ERROR,
} from './constants';

// The initial state of the PLIRating container
export const initialState = {
  ratings: [],
  ratingDetail: null,
  loading: false,
  error: null,
  modalVisible: false,
  currentRating: null,
  overrideLoading: false,
  overrideError: null,
  finalizeLoading: false,
  finalizeError: null,
  detailLoading: false,
  detailError: null,
  processingLoading: false,
  processingResults: null,
  processingError: null,
  resultsModalVisible: false,
  mentorMenteeData: [],
  mentorMenteeLoading: false,
  mentorMenteeError: null,
};

/* eslint-disable default-case, no-param-reassign, no-underscore-dangle */
const pliRatingReducer = (state = initialState, action) =>
  produce(state, draft => {
    switch (action.type) {
      case FETCH_PLI_RATINGS:
        draft.loading = true;
        draft.error = null;
        break;

      case FETCH_PLI_RATINGS_SUCCESS:
        draft.loading = false;
        draft.ratings = action.ratings;
        break;

      case FETCH_PLI_RATINGS_ERROR:
        draft.loading = false;
        draft.error = action.error;
        break;

      case FETCH_PLI_RATING_DETAIL:
        draft.detailLoading = true;
        draft.detailError = null;
        break;

      case FETCH_PLI_RATING_DETAIL_SUCCESS:
        draft.detailLoading = false;
        draft.ratingDetail = action.ratingDetail;
        break;

      case FETCH_PLI_RATING_DETAIL_ERROR:
        draft.detailLoading = false;
        draft.detailError = action.error;
        break;

      case OVERRIDE_PLI_RATING:
        draft.overrideLoading = true;
        draft.overrideError = null;
        break;

      case OVERRIDE_PLI_RATING_SUCCESS:
        draft.overrideLoading = false;

        // Update the rating in the list
        if (draft.ratings.length > 0) {
          const index = draft.ratings.findIndex(
            r => r._id === action.result._id,
          );
          if (index !== -1) {
            draft.ratings[index] = action.result;
          }
        }

        // Update rating detail if it's the same one
        if (
          draft.ratingDetail &&
          draft.ratingDetail._id === action.result._id
        ) {
          draft.ratingDetail = action.result;
        }

        break;

      case OVERRIDE_PLI_RATING_ERROR:
        draft.overrideLoading = false;
        draft.overrideError = action.error;
        break;

      case FINALIZE_PLI_RATING:
        draft.finalizeLoading = true;
        draft.finalizeError = null;
        break;

      case FINALIZE_PLI_RATING_SUCCESS:
        draft.finalizeLoading = false;

        // Update the rating in the list
        if (draft.ratings.length > 0) {
          const index = draft.ratings.findIndex(
            r => r._id === action.result._id,
          );
          if (index !== -1) {
            draft.ratings[index] = action.result;
          }
        }

        // Update rating detail if it's the same one
        if (
          draft.ratingDetail &&
          draft.ratingDetail._id === action.result._id
        ) {
          draft.ratingDetail = action.result;
        }

        break;

      case FINALIZE_PLI_RATING_ERROR:
        draft.finalizeLoading = false;
        draft.finalizeError = action.error;
        break;

      case SHOW_MODAL:
        draft.modalVisible = true;
        draft.currentRating = action.rating;
        break;

      case HIDE_MODAL:
        draft.modalVisible = false;
        break;

      case PROCESS_BULK_ASSIGNMENTS:
        draft.processingLoading = true;
        draft.processingError = null;
        draft.processingResults = null;
        break;

      case PROCESS_BULK_ASSIGNMENTS_SUCCESS:
        draft.processingLoading = false;
        draft.processingResults = action.results;
        draft.resultsModalVisible = true;
        break;

      case PROCESS_BULK_ASSIGNMENTS_ERROR:
        draft.processingLoading = false;
        draft.processingError = action.error;
        draft.resultsModalVisible = true;
        break;

      case SHOW_RESULTS_MODAL:
        draft.resultsModalVisible = true;
        break;

      case HIDE_RESULTS_MODAL:
        draft.resultsModalVisible = false;
        draft.processingResults = null;
        draft.processingError = null;
        break;

      case FETCH_MENTOR_MENTEE_DATA:
        draft.mentorMenteeLoading = true;
        draft.mentorMenteeError = null;
        break;

      case FETCH_MENTOR_MENTEE_DATA_SUCCESS:
        draft.mentorMenteeData = action.data;
        draft.mentorMenteeLoading = false;
        break;

      case FETCH_MENTOR_MENTEE_DATA_ERROR:
        draft.mentorMenteeError = action.error;
        draft.mentorMenteeLoading = false;
        break;
    }
  });

export default pliRatingReducer;
