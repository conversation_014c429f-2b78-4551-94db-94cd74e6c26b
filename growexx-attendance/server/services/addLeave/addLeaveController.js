
const AddUserLeaveService = require('./addLeaveService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add user leave
 */
class AddUserLeaveController {

    /**
     * @desc This function is being used to add user leave
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addUserLeave (req, res) {
        try {
            const data = await AddUserLeaveService.addUserLeave(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddUserLeaveController;
