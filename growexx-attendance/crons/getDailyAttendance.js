const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');

const FetchLogService = require('../server/services/fetchJiraLogs/fetchJiraLogsService');
const CronStatus = require('../server/models/cronStatus.model');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10

};

const req = {
    query: {
        startDate: MOMENT().subtract(1, 'days').startOf('day').format('YYYY-MM-DD'),
        endDate: MOMENT().endOf('day').format('YYYY-MM-DD')
    }
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchDailyLog = async () => {
    try {
        CONSOLE_LOGGER.info(`FetchDailyLog Cron Started at ${new Date}  with request ${req}`);
        await connectDB();
        await CronStatus.findOneAndUpdate(
            { createdAt: { $gte: MOMENT().startOf('day') } },
            { $set: { isGetDailyAttendanceCronRun: false } },
            {
                upsert: true,
                new: true
            }
        );
        const user = {
            role: CONSTANTS.ROLE.ADMIN
        };
        await FetchLogService.fetchJiraLogs(req, user);
        CONSOLE_LOGGER.info(`FetchDailyLog Cron Started at ${new Date}  with request ${req}`);
        await CronStatus.findOneAndUpdate(
            { createdAt: { $gte: MOMENT().startOf('day') } },
            { $set: { isGetDailyAttendanceCronRun: true } }
        );
        process.exit();
        return true;
    } catch (err) {
        await CronStatus.findOneAndUpdate(
            { createdAt: { $gte: MOMENT().startOf('day') } },
            { $set: { isGetDailyAttendanceCronRun: false } }
        );
        CONSOLE_LOGGER.info('fetchDailyLog error', err);
        process.exit();
        return false;
    }
};

fetchDailyLog();
