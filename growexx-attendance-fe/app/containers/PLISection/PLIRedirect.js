/**
 * PLI Redirect Handler
 * Handles redirection to PLI details after email link click
 */
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import queryString from 'query-string';
import { push } from 'connected-react-router';
import {
  makeSelectUser,
  makeSelectIsAuthenticated,
} from 'containers/App/selectors';
import { loginWithToken } from 'containers/Auth/Login/actions';
import StorageService from 'utils/StorageService';
import { TOKEN_KEY } from 'utils/constants';

export class PLIRedirect extends React.Component {
  componentDidMount() {
    this.processRedirect();
  }

  // Decode JWT token to extract payload
  decodeJwt = token => {
    try {
      const base64Url = token.split('.')[1];
      if (!base64Url) return null;

      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
          .join(''),
      );

      return JSON.parse(jsonPayload);
    } catch (error) {
      return null;
    }
  };

  processRedirect = () => {
    try {
      // Extract token from URL
      const parsedQuery = queryString.parse(this.props.location.search);
      const {
        token,
        redirectTo: explicitRedirect,
        pliRatingId,
        view,
        menteeId,
      } = parsedQuery;

      if (!token) {
        // Redirect to login with redirect parameter and appropriate query parameters
        const defaultRedirectPath = pliRatingId
          ? `/my-pli-rating?pliRatingId=${pliRatingId}&view=${view ||
              'mentee-view'}${menteeId ? `&menteeId=${menteeId}` : ''}`
          : '/pli?activeTab=pli';

        this.props.redirectToPath(
          `/login?redirect=${encodeURIComponent(defaultRedirectPath)}`,
        );
        return;
      }

      // Decode token to get redirect data
      const decodedToken = this.decodeJwt(token);

      // Determine the redirect path - either from token, explicit query param, or default
      let redirectPath;

      // If pliRatingId is provided in the URL, prioritize it
      if (pliRatingId) {
        redirectPath = `/my-pli-rating?pliRatingId=${pliRatingId}&view=${view ||
          'mentee-view'}${menteeId ? `&menteeId=${menteeId}` : ''}`;
      } else if (
        decodedToken &&
        decodedToken.redirectData &&
        decodedToken.redirectData.redirectTo
      ) {
        // Use path from token if available
        redirectPath = decodedToken.redirectData.redirectTo;
        // Ensure we're showing the PLI tab if it's the generic PLI page
        if (
          redirectPath.includes('/pli') &&
          !redirectPath.includes('activeTab=')
        ) {
          redirectPath += redirectPath.includes('?')
            ? '&activeTab=pli'
            : '?activeTab=pli';
        }
      } else if (explicitRedirect) {
        // Use explicit redirect parameter if provided
        redirectPath = explicitRedirect;
        if (
          redirectPath.includes('/pli') &&
          !redirectPath.includes('activeTab=')
        ) {
          redirectPath += redirectPath.includes('?')
            ? '&activeTab=pli'
            : '?activeTab=pli';
        }
      } else {
        // Default fallback - use the specific PLI rating URL
        redirectPath =
          '/my-pli-rating?pliRatingId=682dc898190bbe1c4d546591&view=mentee-view';
      }

      // Check if user is already authenticated by checking token in browser storage
      const browserToken = StorageService.get(TOKEN_KEY);

      if (browserToken) {
        // User has a token in browser storage, redirect directly to the target page

        this.props.redirectToPath(redirectPath);
      } else {
        // No token in browser storage, redirect to login with the redirect parameter

        this.props.redirectToPath(
          `/login?redirect=${encodeURIComponent(redirectPath)}`,
        );
      }
    } catch (error) {
      // Handle error gracefully without using console.error
      // Redirect to login with redirect parameter and tab query parameter
      this.props.redirectToPath('/login?redirect=%2Fpli%3FactiveTab%3Dpli');
    }
  };

  render() {
    // Don't render anything as we're redirecting immediately
    return null;
  }
}

PLIRedirect.propTypes = {
  location: PropTypes.object,
  redirectToPath: PropTypes.func,
};

const mapStateToProps = createStructuredSelector({
  user: makeSelectUser(),
  isAuthenticated: makeSelectIsAuthenticated(),
});

const mapDispatchToProps = dispatch => ({
  redirectToPath: path => dispatch(push(path)),
  loginWithToken: (token, redirectPath) =>
    dispatch(loginWithToken(token, redirectPath)),
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(withConnect)(PLIRedirect);
