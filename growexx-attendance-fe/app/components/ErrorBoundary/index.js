import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Result, <PERSON>ton, Card, Typography, Collapse, Alert } from 'antd';

// const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 20px;
  text-align: center;
  background-color: #f0f2f5;
`;

const ErrorDetails = styled(Card)`
  margin-top: 20px;
  max-width: 800px;
  width: 100%;
  text-align: left;
`;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <ErrorContainer>
          <Result
            status="error"
            title="Something went wrong"
            subTitle="The application encountered an unexpected error. You can try refreshing the page or contact support if the issue persists."
            extra={[
              <Button type="primary" key="retry" onClick={this.handleRetry}>
                Retry
              </Button>,
            ]}
          />
          {this.state.error && (
            <ErrorDetails>
              <Alert
                message="Error Details"
                description={this.state.error.toString()}
                type="error"
                showIcon
              />
              <Collapse ghost>
                <Panel header="Component Stack Trace" key="1">
                  <Typography>
                    <pre style={{ whiteSpace: 'pre-wrap' }}>
                      {this.state.errorInfo &&
                        this.state.errorInfo.componentStack}
                    </pre>
                  </Typography>
                </Panel>
              </Collapse>
            </ErrorDetails>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ErrorBoundary;
