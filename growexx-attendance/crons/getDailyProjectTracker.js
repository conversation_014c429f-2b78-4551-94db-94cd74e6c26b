const dotenv = require('dotenv');
// const axios = require('axios');
const { createLogger, format, transports } = require('winston');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

const fetchAndStoreProjectData = require('./projectTrackerHelper');
const { combine, timestamp, printf } = format;
// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');
const loggerFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} => ${level}: ${message}`;
});

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [
    new transports.Console({})
];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME })
    );
}
const logger = createLogger({
    format: combine(
        timestamp(),
        loggerFormat
    ),
    transports: appTransports
});
let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}
const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};
/**
* @desc This function is being used to connect to db server
* <AUTHOR>
* @since 27/11/2023
*/
const connectDB = async () => {
    try {
        return process.env.NODE_ENV !== 'testing' && await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        logger.info('MongoDB connection error.', err);
        return false;
    }
};

/**
* @desc This function is being used to fetch the project tracker data
* <AUTHOR>
* @since 27/11/2023
*/
const fetchDailyProjectTracker = async () => {
    try {
        logger.info(`Fetch Daily Project Tracker Cron Started at ${new Date}`);
        await connectDB();
        await fetchAndStoreProjectData();
        logger.info(`Fetch Daily Project Tracker Cron Started at ${new Date}`);
        process.env.NODE_ENV !== 'testing' && process.exit();
    } catch (err) {
        logger.error('Fetch Daily Project Tracker error', err.message);
        process.env.NODE_ENV !== 'testing' && process.exit();
    }
};

fetchDailyProjectTracker();
module.exports = fetchDailyProjectTracker;
