// original password Test@123
const pass = '1e879ae4d53496184ed3ce2ec1f1d0f4:8249253cdbbe7462afccd5a0cb3b42dcfb9ede391da36e2af7e3739ba34c77b7d25a37806b4b930affd846c6ca7b981b0a4c20c6e516e2c85ceb27e040a285bec3026bdcb8c68f18907be1bf23f30c00';

module.exports = {
    async up (db) {
        await db.collection('users').insert({
            email: '<EMAIL>',
            password: pass,
            // password: 'd1e7e544d77aeca847d93b701cfea44d:dace4ac5d07245eb29114a3f9f17e6efcd1ae0caeaf7fdf88bee36fa111d1fb78f0fdae1b802095719e6eef683926c47571a0c16389078692c67d393757fe178a2e98d0cd194e359e6766da5f452ff5b',
            role: 4,
            isActive: 1,
            firstName: 'Growexx',
            lastName: 'Admin'
        });
    }
};
