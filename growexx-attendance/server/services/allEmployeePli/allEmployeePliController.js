/**
 * This controller is for fetching all employees PLI data
 * <AUTHOR>
 */
const AllEmployeePliService = require('./allEmployeePliService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for all employees PLI data
 */
class AllEmployeePliController {
    /**
     * @param {object} req - Request object
     * @param {object} res - Response object
     * @description - Get all employees PLI data for admin view
     */
    static async getAllEmployeeProfile (req, res) {
        try {
            const data = await AllEmployeePliService.getAllEmployeeProfile();
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            // Database errors should return 400 status code
            if (error.message.includes('Database')) {
                Utils.sendResponse(error, null, res);
                Utils.sendResponse(
                    { message: res.__('ERROR_IN_DATABASE'), statusCode: 400 },
                    null,
                    res
                );
            } else {
                // For other errors, return an empty array with status 200
                Utils.sendResponse(null, [], res, res.__('SUCCESS'));
            }
        }
    }
}

module.exports = AllEmployeePliController;
