/**
 * Migration for pliParameters collection
 * Up: Create collection if it doesn't exist
 * Down: Drop collection
 */
module.exports = {
  async up(db, client) {
    // Create the collection if it doesn't exist
    const collections = await db.listCollections({ name: 'pliParameters' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('pliParameters');
    }
  },

  async down(db, client) {
    // Drop the collection if it exists
    const collections = await db.listCollections({ name: 'pliParameters' }).toArray();
    if (collections.length > 0) {
      await db.collection('pliParameters').drop();
    }
  }
};