const TestCase = require('./addCategoryWeightage');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const admin = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadAdmin = {
    token: jwt.sign(admin, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Add category weightage', () => {

    TestCase.addCategoryWeightage.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .post('/kra/category-weightage')
                .set({ Authorization: requestPayloadAdmin.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should not able to add category Weightage', async () => {
        const data = {
            'categoryWeightages': [
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Business Result',
                    quarter: 'Q2',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Value Add',
                    quarter: 'Q2',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 30,
                    category: 'Impact to customer',
                    quarter: 'Q2',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 20,
                    category: 'Process Adherence',
                    quarter: 'Q2',
                    year: 2022

                },
                {
                    designation: 'Data Scientist',
                    weightage: 10,
                    category: 'Development Goals',
                    quarter: 'Q2',
                    year: 2022
                }
            ]
        };
        const res = await request(process.env.BASE_URL)
            .post('/kra/category-weightage')
            .set({ Authorization: requestPayloadAdmin.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });
});
