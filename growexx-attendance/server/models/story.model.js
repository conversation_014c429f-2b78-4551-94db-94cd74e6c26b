/**
 * @name story model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const constants = require('../util/constants');

const schema = new appMongoose.Schema({
    epicId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'epic'
    },
    boardId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'projectBoard'
    },
    jiraStoryId: {
        type: String
    },
    sprintId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'sprint'
    },
    title: {
        type: String
    },
    description: {
        type: String
    },
    jiraStoryNo: {
        type: String
    },
    jiraStoryUrl: {
        type: String
    },
    originalEstimate: {
        type: Number
    },
    loggedEffort: {
        type: Number
    },
    startDate: {
        type: Date,
        default: null
    },
    endDate: {
        type: Date,
        default: null
    },
    status: {
        type: String
    },
    issueType: {
        type: String,
        enum: Object.values(constants.ISSUE_TYPE)
    },
    belongsTo: {
        type: String,
        enum: Object.values(constants.PROJECT_TRACKER.USER_TYPE)
    },
    linkedStoryId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'story'
    },
    subtasks: {
       type: [],
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('story', schema);
