const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.paths['/jira/portal'] = {
        post: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Jira'
            ],
            description: 'Add jira portal by entering website and user details',
            summary: 'Add jira portal by entering website and user details',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Add jira portal by entering website and user details',
                required: true,
                schema: {
                    $ref: '#/definitions/addJiraPortal'
                }
            }],
            responses: {
                200: {
                    description: 'Add Jira portal',
                    schema: {
                        $ref: '#/definitions/successAddJiraPortal'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        },
        put: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Jira'
            ],
            description: 'Edit jira portal by entering website and user details',
            summary: 'Edit jira portal by entering website and user details',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Edit jira portal by entering website and user details',
                required: true,
                schema: {
                    $ref: '#/definitions/editJiraPortal'
                }
            }],
            responses: {
                200: {
                    description: 'Edit Jira portal',
                    schema: {
                        $ref: '#/definitions/successEditJiraPortal'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        },
        delete: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Jira'
            ],
            description: 'Delete jira port from system',
            summary: 'Delete jira port from system',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Delete jira port from system',
                required: true,
                schema: {
                    $ref: '#/definitions/deleteJiraPortal'
                }
            }],
            responses: {
                200: {
                    description: 'Delete jira port from system',
                    schema: {
                        $ref: '#/definitions/successDeleteJiraPortal'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    swaggerJson.definitions.addJiraPortal = {
        type: 'object',
        properties: {
            name: {
                type: 'string',
                example: 'JIRA project name'
            },
            url: {
                type: 'string',
                example: 'JIRA URL'
            },
            email: {
                type: 'string',
                example: 'JIRA EMAIL'
            },
            token: {
                type: 'string',
                example: 'JIRA TOKEN'
            }
        }
    };

    swaggerJson.definitions.editJiraPortal = {
        type: 'object',
        properties: {
            id: {
                type: 'string',
                example: 'JIRA project id'
            },
            name: {
                type: 'string',
                example: 'JIRA prject name'
            },
            email: {
                type: 'string',
                example: 'JIRA EMAIL'
            },
            token: {
                type: 'string',
                example: 'JIRA TOKEN'
            }
        }
    };

    swaggerJson.definitions.deleteJiraPortal = {
        type: 'object',
        properties: {
            id: {
                type: 'string',
                example: 'JIRA project id'
            }
        }
    };

    swaggerJson.definitions.successAddJiraPortal = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    _id: '605e1e14e69cd416e5f6064d',
                    url: 'growbyexx.atlassian.net',
                    name: 'Growexx',
                    email: '<EMAIL>',
                    token: 'Token of Growexx',
                    isActive: 1,
                    lastFetch: '2021-03-26T17:47:00.583Z',
                    createdAt: '2021-03-26T17:47:00.583Z',
                    updatedAt: '2021-03-26T17:47:00.583Z'
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successEditJiraPortal = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    n: 1,
                    nModified: 1,
                    ok: 1
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successDeleteJiraPortal = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    return swaggerJson;
};
