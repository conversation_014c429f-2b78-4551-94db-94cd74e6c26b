module.exports = {
    async up (db) {
        const businessUnitList =
            [
                { employeeId: 1, businessUnit: 'Management' },
                { employeeId: 2, businessUnit: 'Management' },
                { employeeId: 3, businessUnit: 'Management' },
                { employeeId: 4, businessUnit: 'HR & Operations' },
                { employeeId: 5, businessUnit: 'Web & Mobile' },
                { employeeId: 7, businessUnit: 'HR & Operations' },
                { employeeId: 8, businessUnit: 'Web & Mobile' },
                { employeeId: 9, businessUnit: 'Web & Mobile' },
                { employeeId: 11, businessUnit: 'Web & Mobile' },
                { employeeId: 12, businessUnit: 'HR & Operations' },
                { employeeId: 13, businessUnit: 'Web & Mobile' },
                { employeeId: 15, businessUnit: 'Web & Mobile' },
                { employeeId: 19, businessUnit: 'Web & Mobile' },
                { employeeId: 20, businessUnit: 'Web & Mobile' },
                { employeeId: 21, businessUnit: 'Web & Mobile' },
                { employeeId: 22, businessUnit: 'Web & Mobile' },
                { employeeId: 25, businessUnit: 'Web & Mobile' },
                { employeeId: 26, businessUnit: 'N/A' },
                { employeeId: 28, businessUnit: 'Web & Mobile' },
                { employeeId: 30, businessUnit: 'Web & Mobile' },
                { employeeId: 31, businessUnit: 'Web & Mobile' },
                { employeeId: 32, businessUnit: 'Web & Mobile' },
                { employeeId: 33, businessUnit: 'Web & Mobile' },
                { employeeId: 34, businessUnit: 'Web & Mobile' },
                { employeeId: 35, businessUnit: 'Web & Mobile' },
                { employeeId: 37, businessUnit: 'Web & Mobile' },
                { employeeId: 40, businessUnit: 'Web & Mobile' },
                { employeeId: 42, businessUnit: 'Web & Mobile' },
                { employeeId: 46, businessUnit: 'Data Science' },
                { employeeId: 49, businessUnit: 'Web & Mobile' },
                { employeeId: 50, businessUnit: 'Data Science' },
                { employeeId: 51, businessUnit: 'HR & Operations' },
                { employeeId: 52, businessUnit: 'Sales' },
                { employeeId: 53, businessUnit: 'Web & Mobile' },
                { employeeId: 55, businessUnit: 'Data Science' },
                { employeeId: 56, businessUnit: 'Web & Mobile' },
                { employeeId: 57, businessUnit: 'Data Science' },
                { employeeId: 60, businessUnit: 'Management' },
                { employeeId: 62, businessUnit: 'Web & Mobile' },
                { employeeId: 63, businessUnit: 'HR & Operations' },
                { employeeId: 64, businessUnit: 'Web & Mobile' },
                { employeeId: 67, businessUnit: 'Data Science' },
                { employeeId: 71, businessUnit: 'Web & Mobile' },
                { employeeId: 72, businessUnit: 'HR & Operations' },
                { employeeId: 73, businessUnit: 'Web & Mobile' },
                { employeeId: 75, businessUnit: 'Web & Mobile' },
                { employeeId: 76, businessUnit: 'Web & Mobile' },
                { employeeId: 80, businessUnit: 'Data Science' },
                { employeeId: 83, businessUnit: 'Web & Mobile' },
                { employeeId: 84, businessUnit: 'Web & Mobile' },
                { employeeId: 86, businessUnit: 'Web & Mobile' },
                { employeeId: 87, businessUnit: 'Data Science' },
                { employeeId: 88, businessUnit: 'Web & Mobile' },
                { employeeId: 89, businessUnit: 'Data Science' },
                { employeeId: 90, businessUnit: 'Web & Mobile' },
                { employeeId: 96, businessUnit: 'Web & Mobile' },
                { employeeId: 100, businessUnit: 'Web & Mobile' },
                { employeeId: 101, businessUnit: 'Web & Mobile' },
                { employeeId: 102, businessUnit: 'Web & Mobile' },
                { employeeId: 104, businessUnit: 'Web & Mobile' },
                { employeeId: 108, businessUnit: 'Web & Mobile' },
                { employeeId: 109, businessUnit: 'Sales' },
                { employeeId: 112, businessUnit: 'HR & Operations' },
                { employeeId: 113, businessUnit: 'Web & Mobile' },
                { employeeId: 118, businessUnit: 'Web & Mobile' },
                { employeeId: 119, businessUnit: 'Web & Mobile' },
                { employeeId: 121, businessUnit: 'Data Science' },
                { employeeId: 123, businessUnit: 'HR & Operations' },
                { employeeId: 124, businessUnit: 'Data Science' },
                { employeeId: 125, businessUnit: 'Data Science' },
                { employeeId: 126, businessUnit: 'Web & Mobile' },
                { employeeId: 127, businessUnit: 'HR & Operations' },
                { employeeId: 128, businessUnit: 'Web & Mobile' },
                { employeeId: 129, businessUnit: 'Data Science' },
                { employeeId: 130, businessUnit: 'Web & Mobile' },
                { employeeId: 131, businessUnit: 'Web & Mobile' },
                { employeeId: 132, businessUnit: 'Web & Mobile' },
                { employeeId: 133, businessUnit: 'Web & Mobile' },
                { employeeId: 134, businessUnit: 'Web & Mobile' },
                { employeeId: 135, businessUnit: 'Web & Mobile' },
                { employeeId: 136, businessUnit: 'Web & Mobile' },
                { employeeId: 137, businessUnit: 'Web & Mobile' },
                { employeeId: 138, businessUnit: 'Web & Mobile' },
                { employeeId: 139, businessUnit: 'Web & Mobile' },
                { employeeId: 140, businessUnit: 'Web & Mobile' },
                { employeeId: 141, businessUnit: 'Web & Mobile' },
                { employeeId: 142, businessUnit: 'Web & Mobile' },
                { employeeId: 143, businessUnit: 'Data Science' },
                { employeeId: 144, businessUnit: 'Web & Mobile' },
                { employeeId: 145, businessUnit: 'Microsoft Services' },
                { employeeId: 146, businessUnit: 'Web & Mobile' },
                { employeeId: 148, businessUnit: 'Web & Mobile' },
                { employeeId: 150, businessUnit: 'Web & Mobile' },
                { employeeId: 151, businessUnit: 'Sales' },
                { employeeId: 152, businessUnit: 'Web & Mobile' },
                { employeeId: 153, businessUnit: 'Sales' },
                { employeeId: 154, businessUnit: 'Web & Mobile' },
                { employeeId: 155, businessUnit: 'Web & Mobile' },
                { employeeId: 156, businessUnit: 'Web & Mobile' },
                { employeeId: 159, businessUnit: 'Web & Mobile' },
                { employeeId: 160, businessUnit: 'Web & Mobile' },
                { employeeId: 161, businessUnit: 'Microsoft Services' },
                { employeeId: 162, businessUnit: 'Web & Mobile' },
                { employeeId: 164, businessUnit: 'Web & Mobile' },
                { employeeId: 165, businessUnit: 'Web & Mobile' },
                { employeeId: 166, businessUnit: 'Data Science' },
                { employeeId: 167, businessUnit: 'Microsoft Services' },
                { employeeId: 168, businessUnit: 'Web & Mobile' },
                { employeeId: 169, businessUnit: 'Web & Mobile' },
                { employeeId: 170, businessUnit: 'Sales' },
                { employeeId: 171, businessUnit: 'Web & Mobile' },
                { employeeId: 172, businessUnit: 'Management' },
                { employeeId: 173, businessUnit: 'Web & Mobile' },
                { employeeId: 174, businessUnit: 'Sales' },
                { employeeId: 175, businessUnit: 'Web & Mobile' },
                { employeeId: 176, businessUnit: 'Sales' },
                { employeeId: 177, businessUnit: 'Web & Mobile' },
                { employeeId: 178, businessUnit: 'Sales' },
                { employeeId: 179, businessUnit: 'Sales' },
                { employeeId: 180, businessUnit: 'Sales' },
                { employeeId: 181, businessUnit: 'Sales' },
                { employeeId: 182, businessUnit: 'Web & Mobile' },
                { employeeId: 183, businessUnit: 'Web & Mobile' },
                { employeeId: 185, businessUnit: 'HR & Operations' },
                { employeeId: 186, businessUnit: 'Web & Mobile' },
                { employeeId: 187, businessUnit: 'Sales' },
                { employeeId: 188, businessUnit: 'HR & Operations' },
                { employeeId: 189, businessUnit: 'Web & Mobile' },
                { employeeId: 190, businessUnit: 'Web & Mobile' },
                { employeeId: 191, businessUnit: 'Web & Mobile' },
                { employeeId: 192, businessUnit: 'HR & Operations' },
                { employeeId: 194, businessUnit: 'Web & Mobile' },
                { employeeId: 196, businessUnit: 'Web & Mobile' },
                { employeeId: 197, businessUnit: 'Web & Mobile' }
            ];

        businessUnitList.map(async obj => {
            return await db.collection('users').updateOne({ employeeId: obj.employeeId }, {
                $set: { businessUnit: obj.businessUnit }
            });
        });

    }
};
