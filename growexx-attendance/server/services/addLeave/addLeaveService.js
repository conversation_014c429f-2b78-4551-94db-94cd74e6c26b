const AddUserLeaveValidator = require('./addLeaveValidator');
const Leave = require('../../models/leave.model');
const User = require('../../models/user.model');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for add user leave.
 */
class AddUserLeaveService {

    /**
     * @desc This function is being used to add user leave
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addUserLeave (req, locale) {
        let {
            startDate,
            endDate
        } = req.body;

        const {
            employeeId,
            leaveType
        } = req.body;

        const Validator = new AddUserLeaveValidator(req.body, locale);
        Validator.validationAddUserLeave();
        const user = await User.findOne({
            employeeId
        }).select('_id');
        if (!user) {
            throw 'EMPLOYEE_NOT_FOUND';
        }
        const leaves = [];
        const leaveDay = MOMENT(startDate, 'YYYY-MM-DD');
        endDate = MOMENT(endDate, 'YYYY-MM-DD');
        startDate = MOMENT(startDate, 'YYYY-MM-DD');

        if (!startDate.isValid() || !endDate.isValid()) {
            throw 'INVALID_DATE';
        }

        while (leaveDay.isSameOrBefore(endDate)) {
            const leave = await this.addLeave(startDate, endDate, leaveDay, 'full', user._id, leaveType);
            leaveDay.add(1, 'day');
            if (leave) {
                leaves.push(leave);
            }
        }

        return await Leave.insertMany(leaves);
    }

    static async addLeave (startDate, endDate, leaveDate, duration, userId, leaveType) {
        const isWeekend = leaveDate.weekday() % 6 === 0;
        if (isWeekend) {
            return null;
        }
        return new Leave({
            userId,
            leaveDate,
            startDate,
            endDate,
            leaveType,
            duration,
            timeSpentHours: Utils.getTimeSpentHours(duration)
        });
    }

}

module.exports = AddUserLeaveService;
