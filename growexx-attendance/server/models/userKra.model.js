/**
 * @name userKra model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const { KRA_CATAGORY } = require('../util/constants');

const schema = new appMongoose.Schema({
    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user'
    },
    projectId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'projects',
        required: true
    },
    kras: [{
        outcome: {
            type: String,
            required: true
        },
        category: {
            type: String,
            required: true,
            enum: KRA_CATAGORY
        },
        weightage: {
            type: Number,
            required: true,
            min: 0.01,
            max: 100.00
        },
        reportingManagerRating: {
            type: Number,
            min: 0,
            max: 5,
            default: 0
        },
        selfComment: {
            type: String
        },
        reportingManagerComment: {
            type: String
        },
        reviewManagerRating: {
            type: Number,
            min: 0,
            max: 5,
            default: 0
        },
        reviewManagerComment: {
            type: String
        },
        reportingManagerAttachments: [{
            type: String
        }],
        reviewManagerAttachments: [{
            type: String
        }],
        selfAttachments: [{
            type: String
        }],
        target: {
            type: String
        },
        measure: {
            type: String
        }
    }],
    quarter: {
        type: String,
        required: true
    },
    year: {
        type: Number,
        required: true
    },
    overallReportingManagerComment: {
        type: String
    },
    overallReviewManagerComment: {
        type: String
    },
    isPublished: {
        type: Number,
        default: 0,
        // 0 = Not Published, 1 = Published
        enum: [0, 1]
    },
    freezedByReportingManager: {
        type: Number,
        default: 0,
        // 0 = Not freezed, 1 = Freezed
        enum: [0, 1]
    },
    freezedByReviewManager: {
        type: Number,
        default: 0,
        // 0 = Not freezed, 1 = Freezed
        enum: [0, 1]
    },
    freezedByUser: {
        type: Number,
        default: 0,
        // 0 = Not freezed, 1 = Freezed
        enum: [0, 1]
    },
    overallSelfComment: {
        type: String
    },
    selfAssessmentEndDate: {
        type: Date,
        default: null
    },
    reportingManagerAssessmentEndDate: {
        type: Date,
        default: null
    },
    reviewManagerAssessmentEndDate: {
        type: Date,
        default: null
    },
    meetingDate: {
        type: Date,
        default: null
    },
    ratingReleaseDate: {
        type: Date,
        default: null
    },
    reportingManagerWeightedAverageRating: {
        type: Number,
        default: 0
    },
    reviewManagerWeightedAverageRating: {
        type: Number,
        default: 0
    },
    feedbackMeetingDate: {
        type: Date,
        default: null
    },
    status: {
        type: String
    },
    isUserReportingManager: {
        type: Number,
        default: 0
    },
    allProjectWeightedAverage: {
        type: Number,
        default: 0
    },
    hasReviewManager: {
        type: Number,
        default: 1
    },
    isFeedbackMeetingStarted: {
        type: Number,
        default: 0
    },
    isProcessCompleted: {
        type: Number,
        default: 0
    },
    freezedByUserLog: {
        freezedAt: {
            type: Date
        },
        userId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        }
    },
    freezedByReportingManagerLog: {
        freezedAt: {
            type: Date
        },
        userId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        }
    },
    freezedByReviewManagerLog: {
        freezedAt: {
            type: Date
        },
        userId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        }
    },
    freezedByBULog: {
        freezedAt: {
            type: Date
        },
        userId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        }
    },
    kraAssignedLog: {
        assignedAt: {
            type: Date
        },
        assignedBy: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        }
    }
}, {
    timestamps: true
});
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('userKra', schema);
