const router = require("express").Router();
const auth = require("../middleware/auth");
const roleAuth = require("../middleware/roleAuth");
const PLIParametersController = require("../services/pliParameters/pliParametersController");

router.post(
    '/pli-parameters',
    auth,
    roleAuth.checkRole(['Admin', 'BU', 4, 5]),
    PLIParametersController.createPLIParameter
);
 
// Get PLI parameters (new schema)
router.get('/pli-parameters', auth, PLIParametersController.getPLIParameters);
 
// Update PLI parameter (new schema)
router.put(
    '/pli-parameters/:id',
    auth,
    roleAuth.checkRole(['Admin', 'BU', 4, 5]),
    PLIParametersController.updatePLIParameter
);
 
// Delete PLI parameter (new schema)
router.delete(
    '/pli-parameters/:id',
    auth,
    roleAuth.checkRole(['Admin', 'BU', 4, 5]),
    PLIParametersController.deletePLIParameter
);

module.exports = router;
