/**
 * @name sprint model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    projectId: {
        type: appMongoose.Schema.Types.ObjectId
    },
    jiraBoardId: {
        type: String
    },
    sprintId: {
        type: String
    },
    projectName: {
        type: String
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    state: {
        type: String
    },
    sprintGoal: {
        type: String
    },
    sprintUrl: {
        type: String
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
module.exports = appMongoose.model('runningSprint', schema);
