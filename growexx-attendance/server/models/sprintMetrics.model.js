/**
 * @name sprintMetrics model
 * <AUTHOR>
 */
const { Schema, model } = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new Schema({
    projectName: {
        type: String,
        required: true,
        trim: true
    },
    resource: {
        type: String,
        required: true,
        trim: true
    },
    repo: {
        type: String,
        required: true,
        trim: true
    },
    prCount: {
        type: String,
        trim: true
    },
    rejectedPrCount: {
        type: String,
        trim: true
    },
    codeSmell: {
        type: String,
        trim: true
    },
    vulnerabilities: {
        type: String,
        trim: true
    },
    bugs: {
        type: String,
        trim: true
    },
    coverage: {
        type: String,
        trim: true
    },
    duplication: {
        type: String,
        trim: true
    },
    apiCreated: {
        type: String,
        trim: true
    },
    availableAPI: {
        type: String,
        trim: true
    },
    prEfficiency: {
        type: String,
        trim: true
    },
    swaggerRating: {
        type: String,
        trim: true
    },
    prRating: {
        type: String,
        trim: true
    },
    sonarRating: {
        type: String,
        trim: true
    },
    swaggerFinalRating: {
        type: String,
        trim: true
    },
    coverageRating: {
        type: String,
        trim: true
    },
    sprintAverage: {
        type: String,
        trim: true
    },
    sprintNumber: {
        type: String
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    freeze: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = model('sprintMetrics', schema);
