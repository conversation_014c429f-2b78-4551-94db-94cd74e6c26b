// original password Test@123
// eslint-disable-next-line max-len
const pass = '04c5b6a7ac84244988de9b1795e557e4:c32b8a51e1bad49ca1bf838e8faa6f5bab81bd897d089a7d332d9ceba845a53a6cca5889a979dc826c14a7df747a11fcc5af79fd0ffa9a62e89fd00009c58559dc6e56744b3ee720c9a140763f5f4527'; // Growexx@321

module.exports = {
    async up (db) {
        await db.collection('users').insert({
            email: '<EMAIL>',
            password: pass,
            role: 4,
            isActive: 1,
            firstName: 'Growexx-IT',
            lastName: 'Admin'
        });
    }
};
