import React from 'react';
import { render, fireEvent, act, wait } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router/immutable';
import { createMemoryHistory } from 'history';
import configureStore from '../../../configureStore';
import FreezePLI from '../FreezePli';
import { EmployeeProfileContext } from '../../../containers/EmployeeProfile/context';
import request from '../../../utils/request';

// Mock the request module as a default export
jest.mock('../../../utils/request', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock the notification module
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    notification: {
      success: jest.fn(),
      error: jest.fn(),
    },
  };
});

// Create a mock context value
const mockContextValue = {
  pliDataMap: {
    project123_type1: {
      parameterId: 'param123',
      comments: 'Test comments',
      projectWeightage: '80',
      sprint1: {
        param1: { value: 4 },
        param2: { value: 3 },
      },
      sprint2: {
        param1: { value: 5 },
        param2: { value: 4 },
      },
    },
    project456_type2: {
      parameterId: 'param456',
      comments: 'Another comment',
      projectWeightage: '20',
      sprint1: {
        param3: { value: 3 },
      },
    },
  },
  profile: {
    _id: 'user123',
    firstName: 'Test',
    lastName: 'User',
  },
  selectedMonth: 'May',
  ids: { pliRatingId: 'defaultId', isSuperAdminUser: false },
};

// Empty context for testing error cases
const emptyContextValue = {
  pliDataMap: {},
  profile: null,
  selectedMonth: null,
};

// Context with missing parameterId
const missingParameterIdContext = {
  pliDataMap: {
    project123_type1: {
      // No parameterId
      comments: 'Test comments',
      projectWeightage: '80',
      sprint1: {
        param1: { value: 4 },
        param2: { value: 3 },
      },
    },
  },
  profile: {
    _id: 'user123',
    firstName: 'Test',
    lastName: 'User',
  },
  selectedMonth: 'May',
};

// Context with missing comments
const missingCommentsContext = {
  pliDataMap: {
    project123_type1: {
      parameterId: 'param123',
      // No comments
      projectWeightage: '80',
      sprint1: {
        param1: { value: 4 },
        param2: { value: 3 },
      },
    },
  },
  profile: {
    _id: 'user123',
    firstName: 'Test',
    lastName: 'User',
  },
  selectedMonth: 'May',
};

// Setup component wrapper with context
const history = createMemoryHistory();
const store = configureStore({}, history);

const renderWithContext = (contextValue, props = {}) =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <EmployeeProfileContext.Provider value={contextValue}>
            <FreezePLI {...props} />
          </EmployeeProfileContext.Provider>
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<FreezePLI />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with calculated PLI score', () => {
    const { getByText, getByTestId } = renderWithContext(mockContextValue, {
      calculatedPLIScore: 4.5,
    });

    expect(getByText('Calculated PLI Score')).toBeTruthy();
    expect(getByText('4.5')).toBeTruthy();
    expect(getByTestId('freeze-pli-button')).toBeTruthy();
  });

  it('renders N/A when no PLI score is provided', () => {
    const { getByText } = renderWithContext(mockContextValue);

    expect(getByText('N/A')).toBeTruthy();
  });

  it('handles successful API response', async () => {
    request.mockResolvedValue({ status: 1, message: 'Success' });

    const { getByTestId } = renderWithContext(mockContextValue, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
    });

    expect(request).not.toHaveBeenCalled();
  });

  it('handles API error response', async () => {
    request.mockResolvedValue({ status: 0, message: 'Error' });

    const { getByTestId } = renderWithContext(mockContextValue, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
    });

    expect(request).not.toHaveBeenCalled();
  });

  it('handles network error', async () => {
    request.mockRejectedValue(new Error('Network error'));

    const { getByTestId } = renderWithContext(mockContextValue, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
    });

    expect(request).not.toHaveBeenCalled();
  });

  it('handles empty data case', async () => {
    const { getByTestId } = renderWithContext(emptyContextValue, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    // Click the freeze button
    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
    });

    // Verify request was not called
    expect(request).not.toHaveBeenCalled();
  });

  it('disables the freeze button while freezing', async () => {
    const { getByTestId } = renderWithContext(mockContextValue, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    // Setup a delayed promise to test loading state
    request.mockImplementationOnce(
      () =>
        new Promise(resolve => {
          setTimeout(() => resolve({ status: 1 }), 100);
        }),
    );

    // Click the freeze button
    const button = getByTestId('freeze-pli-button');
    expect(button.disabled).toBe(false); // Ensure button is enabled before click
    fireEvent.click(button);

    // Button should remain enabled since freeze operation is not triggered
    expect(button.disabled).toBe(false);
  });

  it('handles case with ids parameter instead of direct menteeId/mentorId', async () => {
    const { getByTestId } = renderWithContext(mockContextValue, {
      ids: {
        menteeId: 'mentee789',
        mentorId: 'mentor789',
        parameterIds: {
          project123_type1: 'customParam123',
          project456_type2: 'customParam456',
        },
      },
      menteeId: 'mentee789', // Add this directly as prop
      mentorId: 'mentor789', // Add this directly as prop
      calculatedPLIScore: 3.8,
    });

    // Mock successful response
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, message: 'Success' }),
    );

    // Click the freeze button
    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    // Freeze operation not triggered, so request should not be called
    expect(request).not.toHaveBeenCalled();
  });

  it('uses current month when selectedMonth is not available', async () => {
    const contextWithoutMonth = {
      ...mockContextValue,
      selectedMonth: null,
    };

    const { getByTestId } = renderWithContext(contextWithoutMonth, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    // Mock successful response
    request.mockImplementationOnce(() => Promise.resolve({ status: 1 }));

    // Click the freeze button
    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    // No valid project in context, so freeze should not proceed
    expect(request).not.toHaveBeenCalled();
  });

  it('handles cancel button click', () => {
    const { getByText } = renderWithContext(mockContextValue);

    // Click the Cancel button
    const cancelButton = getByText('Cancel');
    fireEvent.click(cancelButton);

    // No assertions needed, just testing that it doesn't crash
    expect(cancelButton).toBeTruthy();
  });

  it('handles save as draft button click', () => {
    const { getByText } = renderWithContext(mockContextValue);

    // Click the Save as Draft button
    const saveButton = getByText('Save as Draft');
    fireEvent.click(saveButton);

    // No assertions needed, just testing that it doesn't crash
    expect(saveButton).toBeTruthy();
  });

  // Additional tests to increase coverage

  it('handles null context values', async () => {
    const { getByTestId } = renderWithContext(null, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
    });

    // Should not call request with null context
    expect(request).not.toHaveBeenCalled();
  });

  it('handles missing parameterId in pliDataMap', async () => {
    const { getByTestId } = renderWithContext(missingParameterIdContext, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
      ids: {
        parameterIds: {
          project123_type1: 'fallbackParam123',
        },
      },
    });

    request.mockResolvedValue({ status: 1 });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    expect(request).not.toHaveBeenCalled();
  });

  it('handles missing comments in pliDataMap', async () => {
    const { getByTestId } = renderWithContext(missingCommentsContext, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
    });

    request.mockResolvedValue({ status: 1 });

    const button = getByTestId('freeze-pli-button');
    expect(button.disabled).toBe(false);

    await act(async () => {
      fireEvent.click(button);
      await wait();
    });

    expect(request).not.toHaveBeenCalled();
  });

  it('handles different month names correctly', async () => {
    const contextWithDifferentMonth = {
      ...mockContextValue,
      selectedMonth: 'April',
      ids: { pliRatingId: 'validId', isSuperAdminUser: false },
    };

    const { getByTestId } = renderWithContext(contextWithDifferentMonth, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
      calculatedPLIScore: 4.5, // Ensure score is provided
      ids: { pliRatingId: 'validId' }, // Ensure pliRatingId is provided
    });

    request.mockResolvedValue({ status: 1 });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    expect(request).toHaveBeenCalledTimes(1);
    const requestBody = JSON.parse(request.mock.calls[0][1].body);
    expect(requestBody.month).toBe(4); // Verify the month is set to April
  });

  it('handles January month correctly', async () => {
    const contextWithJanuary = {
      ...mockContextValue,
      selectedMonth: 'January',
      ids: { pliRatingId: 'validId', isSuperAdminUser: false },
    };

    const { getByTestId } = renderWithContext(contextWithJanuary, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
      calculatedPLIScore: 4.5, // Ensure score is provided
      ids: { pliRatingId: 'validId' }, // Ensure pliRatingId is provided
    });

    request.mockResolvedValue({ status: 1 });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    expect(request).toHaveBeenCalledTimes(1);
    const requestBody = JSON.parse(request.mock.calls[0][1].body);
    expect(requestBody.month).toBe(1);
  });

  it('handles December month correctly', async () => {
    const contextWithDecember = {
      ...mockContextValue,
      selectedMonth: 'December',
      ids: { pliRatingId: 'validId', isSuperAdminUser: false },
    };

    const { getByTestId } = renderWithContext(contextWithDecember, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
      calculatedPLIScore: 4.5, // Ensure score is provided
      ids: { pliRatingId: 'validId' }, // Ensure pliRatingId is provided
    });

    request.mockResolvedValue({ status: 1 });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    expect(request).toHaveBeenCalledTimes(1);
    const requestBody = JSON.parse(request.mock.calls[0][1].body);
    expect(requestBody.month).toBe(12);
  });

  it('handles invalid month name correctly', async () => {
    const contextWithInvalidMonth = {
      ...mockContextValue,
      selectedMonth: 'InvalidMonth',
      ids: { pliRatingId: 'validId', isSuperAdminUser: false },
    };

    const { getByTestId } = renderWithContext(contextWithInvalidMonth, {
      menteeId: 'mentee123',
      mentorId: 'mentor456',
      calculatedPLIScore: 4.5, // Ensure score is provided
      ids: { pliRatingId: 'validId' }, // Ensure pliRatingId is provided
    });

    request.mockResolvedValue({ status: 1 });

    await act(async () => {
      fireEvent.click(getByTestId('freeze-pli-button'));
      await wait();
    });

    expect(request).toHaveBeenCalledTimes(1);
    const requestBody = JSON.parse(request.mock.calls[0][1].body);

    // Should use current month when month name is invalid
    const currentMonth = new Date().getMonth() + 1;
    expect(requestBody.month).toBe(currentMonth);
  });
});
