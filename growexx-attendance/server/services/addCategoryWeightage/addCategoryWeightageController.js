const AddCategoryWeightageService = require('./addCategoryWeightageService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add  category weightage
 */
class AddCategoryWeightageController {

    /**
     * @desc This function is being used to add a category weightage
     * <AUTHOR>
     * @since 24/08/2022
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addCategoryWeightage (req, res) {
        try {
            const data = await AddCategoryWeightageService.addCategoryWeightage(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddCategoryWeightageController;
