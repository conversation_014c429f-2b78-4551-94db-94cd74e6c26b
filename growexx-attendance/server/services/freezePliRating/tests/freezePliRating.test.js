const chai = require("chai");
const sinon = require("sinon");
const expect = chai.expect;
const mongoose = require("mongoose");
const FreezePliRatingService = require("../freezePliRatingService");
const FreezePliRatingValidator = require("../freezePliRatingValidator");
const PLIRating = require("../../../models/pliRating.model");
const User = require("../../../models/user.model");
const GeneralError = require("../../../util/GeneralError");
const notificationPath =
  "../../../services/notificationMentee/sendFrozenPliNotification";

describe("FreezePliRating Service", () => {
  let sandbox;
  let req;
  let locale;
  const mockPliRatingId = new mongoose.Types.ObjectId();
  const mockMentorId = new mongoose.Types.ObjectId();
  const mockMenteeId = new mongoose.Types.ObjectId();

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        locale = sinon.stub();
        locale.withArgs("PLI_RATING_NOT_FOUND").returns("PLI rating not found");
        locale.withArgs("UNAUTHORIZED_MENTOR").returns("Unauthorized mentor");
        locale.withArgs("PLI_ALREADY_FROZEN").returns("PLI already frozen");
        locale.withArgs("UNAUTHORIZED_MENTEE").returns("Unauthorized mentee");
        locale.withArgs("PLI_NOT_FROZEN").returns("PLI not frozen");
        locale.withArgs("PLI_ALREADY_ACCEPTED").returns("PLI already accepted");

    const mockSendNotification = function () {
      return Promise.resolve(true);
        };
        require.cache[require.resolve(notificationPath)] = {
            exports: mockSendNotification,
        };
  });

  afterEach(() => {
    sandbox.restore();
    delete require.cache[require.resolve(notificationPath)];
  });

  describe("freezePliRating", () => {
        beforeEach(() => {
            req = {
                body: {
                    pliRatingId: mockPliRatingId.toString(),
                    mentorId: mockMentorId.toString(),
                    menteeId: mockMenteeId.toString(),
                    month: 5,
                    year: 2025,
        },
            };
    });

    it("should freeze PLI rating successfully with pliRatingId", async () => {
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: false,
        status: "Draft",
        month: 5,
        year: 2025,
        save: sinon.stub().resolves(),
            };

      const mockMentee = {
        _id: mockMenteeId,
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
            };

      const mockMentor = {
        _id: mockMentorId,
        firstName: "Mentor",
        lastName: "Test",
        email: "<EMAIL>",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);
      const userFindByIdStub = sandbox.stub(User, "findById");
      userFindByIdStub.withArgs(mockMenteeId).resolves(mockMentee);
      userFindByIdStub.withArgs(mockMentorId).resolves(mockMentor);

      const result = await FreezePliRatingService.freezePliRating(req, locale);

            expect(result.status).to.equal('success');
            expect(result.data._id.toString()).to.equal(mockPliRatingId.toString());
            expect(result.data.isFrozen).to.be.true;
            expect(result.data.status).to.equal('Submitted');
      expect(mockPliRating.save.calledOnce).to.be.true;
    });

    it("should freeze PLI rating with superAdminOverride", async () => {
      req.body.superAdminOverride = true;

      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: false,
        status: "Draft",
        month: 5,
        year: 2025,
        save: sinon.stub().resolves(),
            };

      const mockMentee = {
        _id: mockMenteeId,
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
            };

      const mockMentor = {
        _id: mockMentorId,
        firstName: "Mentor",
        lastName: "Test",
        email: "<EMAIL>",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findOne").resolves(mockPliRating);
      const userFindByIdStub = sandbox.stub(User, "findById");
      userFindByIdStub.withArgs(mockMenteeId).resolves(mockMentee);
      userFindByIdStub.withArgs(mockMentorId).resolves(mockMentor);

      const result = await FreezePliRatingService.freezePliRating(req, locale);

      expect(result.status).to.equal('success');
      expect(result.data._id.toString()).to.equal(mockPliRatingId.toString());
      expect(result.data.isFrozen).to.be.true;
      expect(result.data.status).to.equal('Finalized');
      expect(result.data.superAdminOverride).to.be.true;
      expect(mockPliRating.save.calledOnce).to.be.true;
    });

    it("should handle notification failure gracefully", async () => {
      const mockSendNotification = function () {
        return Promise.reject(new Error("Notification failed"));
            };
          require.cache[require.resolve(notificationPath)] = {
                exports: mockSendNotification,
            };

      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: false,
        status: "Draft",
        month: 5,
        year: 2025,
        save: sinon.stub().resolves(),
            };

      const mockMentee = {
        _id: mockMenteeId,
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
            };

      const mockMentor = {
        _id: mockMentorId,
        firstName: "Mentor",
        lastName: "Test",
        email: "<EMAIL>",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);
      const userFindByIdStub = sandbox.stub(User, "findById");
      userFindByIdStub.withArgs(mockMenteeId).resolves(mockMentee);
      userFindByIdStub.withArgs(mockMentorId).resolves(mockMentor);

      const result = await FreezePliRatingService.freezePliRating(req, locale);

            expect(result.status).to.equal('success');
            expect(result.data._id.toString()).to.equal(mockPliRatingId.toString());
            expect(result.data.isFrozen).to.be.true;
            expect(result.data.status).to.equal('Submitted');
      expect(mockPliRating.save.calledOnce).to.be.true;
    });

    it("should throw error if PLI rating not found", async () => {
      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(null);

      try {
        await FreezePliRatingService.freezePliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("PLI rating not found");
      }
    });

    it("should throw error if unauthorized mentor", async () => {
      const differentMentorId = new mongoose.Types.ObjectId();
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: differentMentorId,
        menteeId: mockMenteeId,
        isFrozen: false,
        status: "Draft",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      try {
        await FreezePliRatingService.freezePliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("Unauthorized mentor");
      }
    });

    it("should throw error if PLI already frozen", async () => {
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: true,
        status: "Frozen",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateFreezePliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      try {
        await FreezePliRatingService.freezePliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("PLI already frozen");
      }
    });
  });

  describe("acceptPliRating", () => {
        beforeEach(() => {
            req = {
                body: {
                    pliRatingId: mockPliRatingId.toString(),
                    menteeId: mockMenteeId.toString(),
        },
            };
    });

    it("should accept PLI rating successfully", async () => {
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: true,
        status: "Frozen",
        save: sinon.stub().resolves(),
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateAcceptPliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      const result = await FreezePliRatingService.acceptPliRating(req, locale);

      expect(result.status).to.equal('success');
      expect(result.data._id.toString()).to.equal(mockPliRatingId.toString());
      expect(result.data.status).to.equal('Accepted');
      expect(mockPliRating.save.calledOnce).to.be.true;
    });

    it("should throw error if PLI rating not found", async () => {
      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateAcceptPliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(null);

      try {
        await FreezePliRatingService.acceptPliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("PLI rating not found");
      }
    });

    it("should throw error if unauthorized mentee", async () => {
      const differentMenteeId = new mongoose.Types.ObjectId();
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: differentMenteeId,
        isFrozen: true,
        status: "Frozen",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateAcceptPliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      try {
        await FreezePliRatingService.acceptPliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("Unauthorized mentee");
      }
    });

    it("should throw error if PLI not frozen", async () => {
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: false,
        status: "Draft",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateAcceptPliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      try {
        await FreezePliRatingService.acceptPliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("PLI not frozen");
      }
    });

    it("should throw error if PLI already accepted", async () => {
      const mockPliRating = {
        _id: mockPliRatingId,
        mentorId: mockMentorId,
        menteeId: mockMenteeId,
        isFrozen: true,
        status: "Accepted",
            };

      sandbox
        .stub(FreezePliRatingValidator.prototype, "validateAcceptPliRating")
        .resolves();
      sandbox.stub(PLIRating, "findById").resolves(mockPliRating);

      try {
        await FreezePliRatingService.acceptPliRating(req, locale);
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error).to.be.instanceOf(GeneralError);
        expect(error.message).to.equal("PLI already accepted");
      }
    });
  });
});
