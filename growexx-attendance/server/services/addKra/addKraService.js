const kra = require('../../models/kra.model');
const AddKraValidator = require('./addKraValidator');
/**
 * Class represents services for add a kra.
 */
class AddKraService {

    /**
     * @desc This function is being used to add a kra
     * <AUTHOR>
     * @since 07/07/2022
     * @param {Object} req Request
     */
    static async addKra (req, user, locale) {
        const Validator = new AddKraValidator(req.body, locale);
        Validator.validationAddKra();

        const { category, outcome, measure, target, designation, quarter, year } = req.body;

        return await kra.create({
            category,
            outcome,
            measure,
            target,
            designation,
            quarter,
            year
        });
    }

}

module.exports = AddKraService;
