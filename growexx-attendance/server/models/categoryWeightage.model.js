/**
* @name categoryWeightage model
* <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const { KRA_CATAGORY } = require('../util/constants');

const schema = new appMongoose.Schema({
    category: {
        type: String,
        required: true,
        enum: KRA_CATAGORY
    },
    weightage: {
        type: Number,
        required: true,
        min: 0.01,
        max: 100.00
    },
    designation: {
        type: String,
        required: true,
    },
    quarter: {
        type: String,
        required: true
    },
    year: {
        type: Number,
        required: true
    },
    isWeightageAssignedToKra: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('categoryWeightage', schema);

