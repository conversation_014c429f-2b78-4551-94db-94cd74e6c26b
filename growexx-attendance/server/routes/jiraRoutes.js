const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const addJiraPortalController = require('../services/addJiraPortal/addJiraPortalController');
const editJiraPortalController = require('../services/editJiraPortal/editJiraPortalController');
const deleteJiraPortalController = require('../services/deleteJiraPortal/deleteJiraPortalController');
const changeStatusJiraPortalController = require('../services/changeStatusJiraPortal/changeStatusJiraPortalController');
const listJiraPortalController = require('../services/listJiraPortal/listJiraPortalController');
const fetchJiraLogsController = require('../services/fetchJiraLogs/fetchJiraLogsController');

router.post('/portal', AuthMiddleWare, ACLMiddleWare, addJiraPortalController.addJiraPortal);
router.put('/portal', AuthMiddleWare, ACLMiddleWare, editJiraPortalController.editJiraPortal);
router.patch('/portal/status', AuthMiddleWare, ACLMiddleWare, changeStatusJiraPortalController.changeStatusJiraPortal);
router.delete('/portal', AuthMiddleWare, ACLMiddleWare, deleteJiraPortalController.deleteJiraPortal);

router.get('/portals', AuthMiddleWare, ACLMiddleWare, listJiraPortalController.listJiraPortals);
router.get('/logs', AuthMiddleWare, ACLMiddleWare, fetchJiraLogsController.fetchJiraLogs);

module.exports = router;
