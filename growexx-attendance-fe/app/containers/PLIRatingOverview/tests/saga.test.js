import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import { notification } from 'antd';
import { API_ENDPOINTS } from '../../constants';
import sagaModule, {
  fetchPLIRatingsSaga,
  fetchPLIRatingDetailSaga,
  overridePLIRating<PERSON>aga,
  finalizePL<PERSON>ating<PERSON>aga,
} from '../saga';
import {
  fetchPLIRatingsSuccess,
  fetchPLIRatingsError,
  fetchPLIRatingDetailSuccess,
  fetchPLIRatingDetailError,
  overridePLIRatingSuccess,
  overridePLIRatingError,
  finalizePLIRatingSuccess,
  finalizePLIRatingError,
} from '../actions';
import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATING_DETAIL,
  OVERRIDE_PLI_RATING,
  FINALIZE_PLI_RATING,
  PROCESS_BULK_ASSIGNMENTS,
  FETCH_MENTOR_MENTEE_DATA,
} from '../constants';

// Mock request utility
jest.mock('utils/request');

describe('PLI Rating Sagas', () => {
  // Mock notification module
  beforeEach(() => {
    jest.clearAllMocks();
    notification.success = jest.fn();
    notification.error = jest.fn();
  });

  describe('pliRatingSaga', () => {
    it('should start all sagas', () => {
      const generator = sagaModule();

      // First yield: takeLatest for FETCH_PLI_RATINGS
      const firstYield = generator.next().value;
      expect(firstYield).toEqual(
        takeLatest(FETCH_PLI_RATINGS, fetchPLIRatingsSaga),
      );

      // Second yield: takeLatest for FETCH_PLI_RATING_DETAIL
      const secondYield = generator.next().value;
      expect(secondYield).toEqual(
        takeLatest(FETCH_PLI_RATING_DETAIL, fetchPLIRatingDetailSaga),
      );

      // Third yield: takeLatest for OVERRIDE_PLI_RATING
      const thirdYield = generator.next().value;
      expect(thirdYield).toEqual(
        takeLatest(OVERRIDE_PLI_RATING, overridePLIRatingSaga),
      );

      // Fourth yield: takeLatest for FINALIZE_PLI_RATING
      const fourthYield = generator.next().value;
      expect(fourthYield).toEqual(
        takeLatest(FINALIZE_PLI_RATING, finalizePLIRatingSaga),
      );

      // Fifth yield: takeLatest for PROCESS_BULK_ASSIGNMENTS
      const fifthYield = generator.next().value;
      expect(fifthYield).toEqual(
        takeLatest(PROCESS_BULK_ASSIGNMENTS, expect.any(Function)),
      );

      // Sixth yield: takeLatest for FETCH_MENTOR_MENTEE_DATA
      const sixthYield = generator.next().value;
      expect(sixthYield).toEqual(
        takeLatest(FETCH_MENTOR_MENTEE_DATA, expect.any(Function)),
      );

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });
  });

  describe('fetchPLIRatingsSaga', () => {
    const action = {
      type: FETCH_PLI_RATINGS,
      params: {
        department: 'IT',
        status: 'pending',
        cycleMonth: '2023-01',
        search: 'John',
      },
    };

    it('should call API and handle success', () => {
      const generator = fetchPLIRatingsSaga(action);

      // First yield: API call
      const firstYield = generator.next().value;
      expect(firstYield).toEqual(
        call(request, API_ENDPOINTS.ALL_EMPLOYEE_PLI, {
          method: 'GET',
        }),
      );

      // Mock successful response
      const response = {
        status: 1,
        data: [
          {
            id: '1',
            department: 'IT',
            status: 'pending',
            employee: { name: 'John' },
            cycleMonth: '2023-01',
          },
          {
            id: '2',
            department: 'HR',
            status: 'completed',
            employee: { name: 'Jane' },
            cycleMonth: '2023-02',
          },
        ],
      };

      // Second yield: put success action with filtered data
      const secondYield = generator.next(response).value;
      // Just check that a success action is dispatched with the correct type
      // without checking the exact filtered data
      expect(secondYield.type).toBe('PUT');
      expect(secondYield.payload.action.type).toBe(
        fetchPLIRatingsSuccess().type,
      );

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });

    it('should handle error', () => {
      const generator = fetchPLIRatingsSaga(action);

      // Skip the API call
      generator.next();

      // Throw an error
      const error = new Error('API Error');
      const errorYield = generator.throw(error).value;

      // Should dispatch error action
      expect(errorYield).toEqual(put(fetchPLIRatingsError(error)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the error notification was called with expected arguments
      expect(notification.error).toHaveBeenCalledWith({
        message: 'Error',
        description: 'API Error',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });
  });

  describe('fetchPLIRatingDetailSaga', () => {
    const action = {
      type: FETCH_PLI_RATING_DETAIL,
      id: '123',
    };

    it('should call API and handle success', () => {
      const generator = fetchPLIRatingDetailSaga(action);

      // First yield: API call
      const firstYield = generator.next().value;
      expect(firstYield).toEqual(
        call(
          request,
          API_ENDPOINTS.PLI_RATING_DETAIL.replace(':id', action.id),
          {
            method: 'GET',
          },
        ),
      );

      // Mock successful response
      const response = {
        success: true,
        data: { id: '123' },
      };

      // Second yield: put success action
      const secondYield = generator.next(response).value;
      expect(secondYield).toEqual(
        put(fetchPLIRatingDetailSuccess(response.data)),
      );

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });

    it('should handle error', () => {
      const generator = fetchPLIRatingDetailSaga(action);

      // Skip the API call
      generator.next();

      // Throw an error
      const error = new Error('API Error');
      const errorYield = generator.throw(error).value;

      // Should dispatch error action
      expect(errorYield).toEqual(put(fetchPLIRatingDetailError(error)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the error notification was called with expected arguments
      expect(notification.error).toHaveBeenCalledWith({
        message: 'Error',
        description: 'API Error',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });
  });

  describe('overridePLIRatingSaga', () => {
    const action = {
      type: OVERRIDE_PLI_RATING,
      id: '123',
      data: {
        comments: 'Override comments',
        score: 4.5,
      },
    };

    it('should call API and handle success', () => {
      const generator = overridePLIRatingSaga(action);

      // First yield: API call
      const firstYield = generator.next().value;
      expect(firstYield).toEqual(
        call(
          request,
          API_ENDPOINTS.PLI_RATING_DETAIL.replace(':id', action.id),
          {
            method: 'PATCH',
            body: JSON.stringify({
              superAdminOverride: true,
              superAdminComments: action.data.comments,
              finalScore: action.data.score,
            }),
          },
        ),
      );

      // Mock successful response
      const response = {
        success: true,
        data: { id: '123' },
      };

      // Second yield: put success action
      const secondYield = generator.next(response).value;
      expect(secondYield).toEqual(put(overridePLIRatingSuccess(response.data)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the success notification was called with expected arguments
      expect(notification.success).toHaveBeenCalledWith({
        message: 'Success',
        description: 'PLI rating overridden successfully',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });

    it('should handle error', () => {
      const generator = overridePLIRatingSaga(action);

      // Skip the API call
      generator.next();

      // Throw an error
      const error = new Error('API Error');
      const errorYield = generator.throw(error).value;

      // Should dispatch error action
      expect(errorYield).toEqual(put(overridePLIRatingError(error)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the error notification was called with expected arguments
      expect(notification.error).toHaveBeenCalledWith({
        message: 'Error',
        description: 'API Error',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });
  });

  describe('finalizePLIRatingSaga', () => {
    const action = {
      type: FINALIZE_PLI_RATING,
      id: '123',
    };

    it('should call API and handle success', () => {
      const generator = finalizePLIRatingSaga(action);

      // First yield: API call
      const firstYield = generator.next().value;
      expect(firstYield).toEqual(
        call(
          request,
          API_ENDPOINTS.PLI_RATING_FINALIZE.replace(':id', action.id),
          {
            method: 'PATCH',
          },
        ),
      );

      // Mock successful response
      const response = {
        success: true,
        data: { id: '123' },
      };

      // Second yield: put success action
      const secondYield = generator.next(response).value;
      expect(secondYield).toEqual(put(finalizePLIRatingSuccess(response.data)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the success notification was called with expected arguments
      expect(notification.success).toHaveBeenCalledWith({
        message: 'Success',
        description: 'PLI rating finalized successfully',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });

    it('should handle error', () => {
      const generator = finalizePLIRatingSaga(action);

      // Skip the API call
      generator.next();

      // Throw an error
      const error = new Error('API Error');
      const errorYield = generator.throw(error).value;

      // Should dispatch error action
      expect(errorYield).toEqual(put(finalizePLIRatingError(error)));

      // Next step in the generator should call notification directly
      generator.next();

      // Verify that the error notification was called with expected arguments
      expect(notification.error).toHaveBeenCalledWith({
        message: 'Error',
        description: 'API Error',
      });

      // Saga should be done
      expect(generator.next().done).toBe(true);
    });
  });
});
