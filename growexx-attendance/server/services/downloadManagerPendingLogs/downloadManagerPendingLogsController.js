const DownloadManagerPendingLogsService = require('./downloadManagerPendingLogsService');
const Utils = require('../../util/utilFunctions');
const { parse } = require("json2csv");

/**
 * Class represents controller for download manager pending logs report
 */
class DownloadManagerPendingLogsController {

    /**
     * @desc This function is being used to download manager pending logs report
     * <AUTHOR>
     * @since 25/12/2024
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async downloadManagerPendingLogs (req, res) {
        try {
            const data = await DownloadManagerPendingLogsService.downloadManagerPendingLogs(req, res.locals.user);
            Utils.downloadFile(res, data);
        } catch (error) {
            console.error('Controller error in downloadManagerPendingLogs:', error);
        }
    }
}

module.exports = DownloadManagerPendingLogsController; 