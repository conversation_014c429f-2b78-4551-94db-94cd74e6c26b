module.exports = {
    addKra: [{
        it: 'As a admin I should validate if category is not pass',
        options: {
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category is pass as blank',
        options: {
            category: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category is pass as invalid',
        options: {
            category: 40,
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if category must be passed from defined list',
        options: {
            category: 'ABC',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if outcome is not pass',
        options: {
            category: 'Business Result',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if outcome is pass as blank',
        options: {
            outcome: '',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if outcome is pass as invalid',
        options: {
            category: 'Business Result',
            outcome: 40,
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if outcome is pass more than five hundred characters',
        options: {
            category: 'Business Result',
            outcome: `Lorem ipsum dolor sit amet consectetuer adipiscing elit 
            Aenean commodo ligula eget dolor.Aenean massa.Cum sociis natoque penatibus e
            t magnis dis parturient montes, nascetur ridiculus mus.Donec quam felis, ultricies 
            nec, pellentesque eu, pretium quis, sem.Nulla consequat massa quis enim.Donec pede 
            justo, fringilla vel, aliquet nec, vulputate eget, arcu.In enim justo, rhoncus ut,
            imperdiet a, venenatis vitae, justo.Nullam dictum felis eu pede mollis pretium.Integer 
            tincidunt.Cras dapibus.Vivamus elementum semper nisi.Aenean vulputate eleifend tellus .
            Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim.Aliquam lorem ante,
            dapibus in, viverra quis, feugiat a, tellus.Phasellus viverra nulla ut metus varius laoreet.
            Quisque rutrum.Aenean imperdiet.Etiam ultricies nisi vel augue.Curabitur ullamcorper ultricies
             nisi.Nam eget dui.Etiam rhoncus.Maecenas tempus, tellus eget condimentum rhoncus, sem quam semper 
             libero, sit amet adipiscing sem neque sed ipsum.Nam quam nunc, blandit vel, luctus pulvinar, hendrerit 
             id, lorem.Maecenas nec odio et ante tincidunt tempus.Donec vitae sapien ut libero venenatis faucibus.
             Nullam quis ante.Etiam sit amet orci eget eros faucibus tincidunt.Duis leo.Sed fringilla mauris sit amet
              nibh.Donec sodales sagittis magna.Sed consequat, leo eget bibendum sodales, augue velit cursus nunc, quis 
              gravida magna mi a libero.Fusce vulputate eleifend sapien.Vestibulum purus quam, scelerisque ut, mollis sed,
            nonummy id, metus.Nullam accumsan lorem in dui.Cras ultricies mi eu turpis hendrerit fringilla.Vestibulum ante 
            ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; In ac dui quis mi consectetuer lacinia.Nam 
            pretium turpis et arcu.Duis arcu tortor, suscipit eget, imperdiet nec, imperdiet iaculis, ipsum.Sed aliquam ultrices 
            mauris.Integer ante arcu, accumsan a, consectetuer eget, posuere ut, mauris.Praesent adipiscing.Phasellus ullamcorper 
            ipsum rutrum nunc.Nunc nonummy metus.Vestibulum volutpat pretium libero.Cras id dui.Aenean ut eros et nisl sagittis vestibulum.
            Nullam nulla eros, ultricies sit amet, nonummy id, imperdiet feugiat, pede.Sed lectus.Donec mollis hendrerit risus.Phasellus nec sem in justo pellentesque facilisis.Etiam imperdiet imperdiet orci.Nunc nec neque.Phasellus leo 
            dolor, tempus non, auctor et, hendrerit quis, nisi.Curabitur ligula sapien, tincidunt non, euismod vitae, posuere imperdiet, leo.Maecenas 
            malesuada.Praesent congue erat at massa.Sed cursus turpis vitae tortor.Donec posuere vulputate arcu.Phasellus accumsan cursus velit.Vestibulum 
            ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Sed aliquam, nisi quis porttitor congue, elit erat euismod orci, ac placerat dolor lectus quis orci.Phasellus consectetuer vestibulum elit.Aenean tellus metus, bibendum sed, posuere ac, mattis non, nunc.
            Vestibulum fringilla pede sit amet augue.In turpis.Pellentesque posuere.Praesent turpis.Aenean posuere,
             tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus.Donec elit libero,
             sodales nec, volutpat a, suscipit non, turpis.Nullam sagittis.Suspendisse pulvinar, augue ac venenatis condimentum, sem libero volutpat nibh, nec pellentesque velit pede quis nunc.Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia
             Curae; Fusce id purus.Ut varius tincidunt libero.Phasellus dolor.Maecenas vestibulum mollis diam.Pellentesque ut neque.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.In dui magna, posuere eget, vestibulum et, tempor auctor,
             justo.In ac felis quis tortor malesuada pretium.Pellentesque auctor neque nec urna.Proin sapien ipsum, porta a, auctor quis, euismod ut, mi.Aenean viverra rhoncus pede.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.Ut non enim eleifend felis pretium feugiat.Vivamus quis mi.Phasellus a est.Phasellus magna.In hac habitasse platea dictumst.Curabitur at lacus ac velit ornare lobortis.Curabitur a felis in nunc fringilla tristique`,
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if measure is not pass',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if measure is pass as blank',
        options: {
            measure: '',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if measure is pass as invalid',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 40,
            target: 'This data can be obtained from Sprint report',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if target is not pass',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },

    {
        it: 'As a admin I should validate if target is pass as blank',
        options: {
            target: '',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if target is pass as invalid',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 40,
            designation: 'Software engineer',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if target is pass pass more than five hundred characters',
        options: {
            category: 'Business Result',
            outcome: 'Estimation vs spent',
            measure: 'Estimation vs spent',
            target: `Lorem ipsum dolor sit amet consectetuer adipiscing elit 
                Aenean commodo ligula eget dolor.Aenean massa.Cum sociis natoque penatibus e
                t magnis dis parturient montes, nascetur ridiculus mus.Donec quam felis, ultricies 
                nec, pellentesque eu, pretium quis, sem.Nulla consequat massa quis enim.Donec pede 
                justo, fringilla vel, aliquet nec, vulputate eget, arcu.In enim justo, rhoncus ut,
                imperdiet a, venenatis vitae, justo.Nullam dictum felis eu pede mollis pretium.Integer 
                tincidunt.Cras dapibus.Vivamus elementum semper nisi.Aenean vulputate eleifend tellus .
                Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim.Aliquam lorem ante,
                dapibus in, viverra quis, feugiat a, tellus.Phasellus viverra nulla ut metus varius laoreet.
                Quisque rutrum.Aenean imperdiet.Etiam ultricies nisi vel augue.Curabitur ullamcorper ultricies
                 nisi.Nam eget dui.Etiam rhoncus.Maecenas tempus, tellus eget condimentum rhoncus, sem quam semper 
                 libero, sit amet adipiscing sem neque sed ipsum.Nam quam nunc, blandit vel, luctus pulvinar, hendrerit 
                 id, lorem.Maecenas nec odio et ante tincidunt tempus.Donec vitae sapien ut libero venenatis faucibus.
                 Nullam quis ante.Etiam sit amet orci eget eros faucibus tincidunt.Duis leo.Sed fringilla mauris sit amet
                  nibh.Donec sodales sagittis magna.Sed consequat, leo eget bibendum sodales, augue velit cursus nunc, quis 
                  gravida magna mi a libero.Fusce vulputate eleifend sapien.Vestibulum purus quam, scelerisque ut, mollis sed,
                nonummy id, metus.Nullam accumsan lorem in dui.Cras ultricies mi eu turpis hendrerit fringilla.Vestibulum ante 
                ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; In ac dui quis mi consectetuer lacinia.Nam 
                pretium turpis et arcu.Duis arcu tortor, suscipit eget, imperdiet nec, imperdiet iaculis, ipsum.Sed aliquam ultrices 
                mauris.Integer ante arcu, accumsan a, consectetuer eget, posuere ut, mauris.Praesent adipiscing.Phasellus ullamcorper 
                ipsum rutrum nunc.Nunc nonummy metus.Vestibulum volutpat pretium libero.Cras id dui.Aenean ut eros et nisl sagittis vestibulum.
                Nullam nulla eros, ultricies sit amet, nonummy id, imperdiet feugiat, pede.Sed lectus.Donec mollis hendrerit risus.Phasellus nec sem in justo pellentesque facilisis.Etiam imperdiet imperdiet orci.Nunc nec neque.Phasellus leo 
                dolor, tempus non, auctor et, hendrerit quis, nisi.Curabitur ligula sapien, tincidunt non, euismod vitae, posuere imperdiet, leo.Maecenas 
                malesuada.Praesent congue erat at massa.Sed cursus turpis vitae tortor.Donec posuere vulputate arcu.Phasellus accumsan cursus velit.Vestibulum 
                ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Sed aliquam, nisi quis porttitor congue, elit erat euismod orci, ac placerat dolor lectus quis orci.Phasellus consectetuer vestibulum elit.Aenean tellus metus, bibendum sed, posuere ac, mattis non, nunc.
                Vestibulum fringilla pede sit amet augue.In turpis.Pellentesque posuere.Praesent turpis.Aenean posuere,
                 tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus.Donec elit libero,
                 sodales nec, volutpat a, suscipit non, turpis.Nullam sagittis.Suspendisse pulvinar, augue ac venenatis condimentum, sem libero volutpat nibh, nec pellentesque velit pede quis nunc.Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia
                 Curae; Fusce id purus.Ut varius tincidunt libero.Phasellus dolor.Maecenas vestibulum mollis diam.Pellentesque ut neque.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.In dui magna, posuere eget, vestibulum et, tempor auctor,
                 justo.In ac felis quis tortor malesuada pretium.Pellentesque auctor neque nec urna.Proin sapien ipsum, porta a, auctor quis, euismod ut, mi.Aenean viverra rhoncus pede.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.Ut non enim eleifend felis pretium feugiat.Vivamus quis mi.Phasellus a est.Phasellus magna.In hac habitasse platea dictumst.Curabitur at lacus ac velit ornare lobortis.Curabitur a felis in nunc fringilla tristique`,
            designation: 'Software engineer'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is not pass',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is pass as blank',
        options: {
            designation: '',
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is pass as invalid',
        options: {
            category: 'Business Result',
            outcome: 'Developing high quality user story',
            measure: 'Estimation vs spent',
            target: 'This data can be obtained from Sprint report',
            designation: 40,
            quarter: 'Q3',
            year: 2022
        },
        status: 0
    }
    ]
};
