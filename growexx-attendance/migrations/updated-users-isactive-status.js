const fs = require("fs");
const path = require("path");

const EMPLOYEE_EMAILS_PATH = path.join(__dirname, "employeeEmails.json");

function getEmployeeEmails() {
  try {
    const rawData = fs.readFileSync(EMPLOYEE_EMAILS_PATH);
    const data = JSON.parse(rawData);
    return {
      employeeEmails: data.employeeEmails.map((email) => email.toLowerCase()),
      internEmails: data.internEmail.map((email) => email.toLowerCase()),
      outsourceEmail: data.outsourceEmail.map((email) => email.toLowerCase()),
    };
  } catch (error) {
    throw new Error(`❌ Error reading employeeEmails.json: ${error.message}`);
  }
}

module.exports = {
  async up(db, client) {
    const { employeeEmails, internEmails, outsourceEmail } =
      getEmployeeEmails();
    const allUsers = await db.collection("users").find({}).toArray();

    for (const user of allUsers) {
      const userEmail = user.email?.toLowerCase();
      const shouldBeActive =
        employeeEmails.includes(userEmail) ||
        internEmails.includes(userEmail) ||
        outsourceEmail.includes(userEmail);
      const newIsActive = shouldBeActive ? 1 : 0;

      // Log every user
      console.log(
        `User ${userEmail} should be ${newIsActive}. Current isActive: ${user.isActive}`
      );

      if (user.isActive === newIsActive) continue;

      // Update isActive to 1 or 0
      await db.collection("users").updateOne(
        { email: user.email },
        {
          $set: {
            isActive: newIsActive,
            updatedAt: new Date(),
          },
        }
      );

      // Log in migration_logs
      await db.collection("migration_logs").insertOne({
        email: user.email,
        collection: "users",
        action: "BULK_SET_ACTIVE",
        timestamp: new Date(),
        previousIsActive: user.isActive,
        newIsActive: newIsActive,
      });

      console.log(`✅ Updated: ${user.email} -> isActive: ${newIsActive}`);
    }
  },

  async down(db, client) {
    const logs = await db
      .collection("migration_logs")
      .find({ action: "BULK_SET_ACTIVE" })
      .toArray();

    for (const log of logs) {
      await db.collection("users").updateOne(
        { email: log.email },
        {
          $set: {
            isActive: log.previousIsActive,
            updatedAt: new Date(),
          },
        }
      );

      await db.collection("migration_logs").insertOne({
        email: log.email,
        collection: "users",
        action: "ROLLBACK_BULK_SET_ACTIVE",
        timestamp: new Date(),
        previousIsActive: log.newIsActive,
        newIsActive: log.previousIsActive,
      });

      console.log(
        `⏪ Rollback: ${log.email} isActive restored to ${log.previousIsActive}`
      );
    }
  },
};
