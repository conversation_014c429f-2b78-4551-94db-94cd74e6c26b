const AddKraService = require('./addKraService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add kra
 */
class AddKraController {

    /**
     * @desc This function is being used to add a kra
     * <AUTHOR>
     * @since 07/07/2022
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addKra (req, res) {
        try {
            const data = await AddKraService.addKra(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddKraController;
