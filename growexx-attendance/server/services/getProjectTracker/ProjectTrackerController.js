const Utils = require('../../util/utilFunctions');
const ProjectTrackerService = require('./ProjectTrackerService');

/**
 * Class represents controller for Get Project Tracker.
 */
class ProjectTrackerController {
    /**
   * @desc This function is being used to get the Project Trakcer List
   * <AUTHOR>
   * @since 24/11/2023
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */

    static async getProjectTracker (req, res) {
        try {
            const data = await ProjectTrackerService.getProjectTracker(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    /**
     * @desc This function is being used to get the Epic Names List for filtering
     * <AUTHOR>
     * @since [Current Date]
     * @param {Object} req Request
     * @param {Object} req.query.boardId Board ID
     * @param {Object} req.query.search Search term for epic names
     * @param {function} res Response
     */
    static async getEpicNames (req, res) {
        try {
            const data = await ProjectTrackerService.getEpicNames(req);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
    static async triggerCron (req, res) {
        try {
            const data = await ProjectTrackerService.triggerCron(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }

    static async getCronStatus (req, res) {
        try {
            const data = await ProjectTrackerService.getCronStatus();
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = ProjectTrackerController;
