module.exports = {
    async up (db) {
        const designationList =
            [
                { employeeId: 5, designation: 'Technical Lead' },
                { employeeId: 8, designation: 'Sr. Software Engineer' },
                { employeeId: 9, designation: 'Technical Lead' },
                { employeeId: 11, designation: 'Technical Lead' },
                { employeeId: 14, designation: 'Sr. Software Engineer' },
                { employeeId: 15, designation: 'Sr. Technical Lead' },
                { employeeId: 17, designation: 'Sr. Software Engineer' },
                { employeeId: 19, designation: 'Product Owner' },
                { employeeId: 20, designation: 'Sr. Technical Project Manager' },
                { employeeId: 21, designation: 'Technical Lead - DevOps' },
                { employeeId: 22, designation: 'Technical Lead' },
                { employeeId: 25, designation: 'Sr. Software Engineer' },
                { employeeId: 26, designation: 'Business Unit Head - Web & Mobile' },
                { employeeId: 28, designation: 'Sr. Software Engineer' },
                { employeeId: 30, designation: 'Sr. Software Engineer - QA' },
                { employeeId: 31, designation: 'Technical Lead' },
                { employeeId: 32, designation: 'Sr. Software Engineer' },
                { employeeId: 33, designation: 'Sr. Software Engineer' },
                { employeeId: 34, designation: 'Sr. Product Owner' },
                { employeeId: 35, designation: 'Sr. Software Engineer' },
                { employeeId: 37, designation: 'Sr. Software Engineer' },
                { employeeId: 40, designation: 'Software Engineer' },
                { employeeId: 42, designation: 'Software Engineer' },
                { employeeId: 46, designation: 'Practice Head – Data Science and Analytics' },
                { employeeId: 49, designation: 'Product Owner' },
                { employeeId: 50, designation: 'Manager - Data Engineering' },
                { employeeId: 53, designation: 'Sr. Software Engineer' },
                { employeeId: 55, designation: 'Lead - Data Engineering' },
                { employeeId: 56, designation: 'Sr. Software Engineer - DevOps' },
                { employeeId: 57, designation: 'Manager - Data Engineering' },
                { employeeId: 62, designation: 'Sr. Software Engineer' },
                { employeeId: 64, designation: 'Software Engineer' },
                { employeeId: 65, designation: 'Software Engineer' },
                { employeeId: 66, designation: 'Sr. Data Engineer' },
                { employeeId: 67, designation: 'Sr. Data Engineer' },
                { employeeId: 71, designation: 'Software Engineer' },
                { employeeId: 72, designation: 'Engineer - IT & DevOps' },
                { employeeId: 73, designation: 'Sr. Software Engineer' },
                { employeeId: 75, designation: 'Sr. Software Engineer' },
                { employeeId: 76, designation: 'Software Engineer' },
                { employeeId: 80, designation: 'Data Engineer' },
                { employeeId: 82, designation: 'Sr. Business Analyst' },
                { employeeId: 83, designation: 'Sr. Software Engineer - QA' },
                { employeeId: 84, designation: 'Sr. Software Engineer - QA' },
                { employeeId: 86, designation: 'Software Engineer' },
                { employeeId: 87, designation: 'Data Engineer' },
                { employeeId: 88, designation: 'Software Engineer' },
                { employeeId: 89, designation: 'Data Engineer' },
                { employeeId: 90, designation: 'Software Engineer' },
                { employeeId: 96, designation: 'Software Engineer' },
                { employeeId: 100, designation: 'Sr. Software Engineer' },
                { employeeId: 101, designation: 'Sr. Software Engineer' },
                { employeeId: 102, designation: 'Software Engineer' },
                { employeeId: 104, designation: 'Technical Project Manager' },
                { employeeId: 108, designation: 'Sr. Software Engineer' },
                { employeeId: 113, designation: 'Software Engineer' },
                { employeeId: 116, designation: 'Sr. Software Engineer' },
                { employeeId: 118, designation: 'Software Engineer' },
                { employeeId: 119, designation: 'Software Engineer' },
                { employeeId: 121, designation: 'Jr. Data Engineer' },
                { employeeId: 122, designation: 'Software Engineer' },
                { employeeId: 124, designation: 'Data Scientist' },
                { employeeId: 125, designation: 'Data Engineer' },
                { employeeId: 126, designation: 'Sr. Software Engineer' },
                { employeeId: 128, designation: 'Software Engineer' },
                { employeeId: 129, designation: 'Jr. Data Engineer' },
                { employeeId: 130, designation: 'Jr. Software Engineer' },
                { employeeId: 131, designation: 'Jr. Software Engineer' },
                { employeeId: 132, designation: 'Jr. Software Engineer' },
                { employeeId: 133, designation: 'Jr. Software Engineer' },
                { employeeId: 134, designation: 'Jr. Software Engineer' },
                { employeeId: 135, designation: 'Jr. Software Engineer' },
                { employeeId: 136, designation: 'Jr. Software Engineer' },
                { employeeId: 137, designation: 'Jr. Software Engineer' },
                { employeeId: 138, designation: 'Jr. Software Engineer' },
                { employeeId: 139, designation: 'Jr. Software Engineer' },
                { employeeId: 140, designation: 'Jr. Software Engineer' },
                { employeeId: 141, designation: 'Jr. Software Engineer' },
                { employeeId: 142, designation: 'Jr. Software Engineer' },
                { employeeId: 143, designation: 'Jr. Data Engineer' },
                { employeeId: 144, designation: 'Jr. Software Engineer' },
                { employeeId: 145, designation: 'Software Engineer' },
                { employeeId: 146, designation: 'Software Engineer' },
                { employeeId: 148, designation: 'Technical Lead' },
                { employeeId: 150, designation: 'Sr. Software Engineer' },
                { employeeId: 152, designation: 'Software Engineer' },
                { employeeId: 153, designation: 'Web Research Executive' },
                { employeeId: 154, designation: 'Software Engineer' },
                { employeeId: 155, designation: 'Software Engineer' },
                { employeeId: 156, designation: 'Technical Lead' },
                { employeeId: 157, designation: 'Software Engineer' },
                { employeeId: 158, designation: 'Sr. Digital Marketing Executive' },
                { employeeId: 159, designation: 'Software Engineer' },
                { employeeId: 160, designation: 'Software Engineer - QA' },
                { employeeId: 161, designation: 'Sr. Software Engineer' },
                { employeeId: 162, designation: 'Software Engineer - QA' },
                { employeeId: 163, designation: 'Software Engineer' },
                { employeeId: 164, designation: 'Jr. Software Engineer' },
                { employeeId: 165, designation: 'Sr. Business Analyst' },
                { employeeId: 166, designation: 'Data Engineer' },
                { employeeId: 167, designation: 'Sr. Software Engineer' },
                { employeeId: 168, designation: 'Jr. Software Engineer - DevOps' },
                { employeeId: 169, designation: 'Sr. Software Engineer' },
                { employeeId: 170, designation: 'Regional Head - Sales' },
                { employeeId: 171, designation: 'Software Engineer' },
                { employeeId: 172, designation: 'Co-Founder' },
                { employeeId: 173, designation: 'Software Engineer' },
                { employeeId: 174, designation: 'SEO Executive' },
                { employeeId: 175, designation: 'Sr. Software Engineer' },
                { employeeId: 176, designation: 'Sr. Executive - Business Development' },
                { employeeId: 177, designation: 'Software Engineer' },
                { employeeId: 178, designation: 'Executive - Lead Generation' },
                { employeeId: 179, designation: 'Executive - Lead Generation' },
                { employeeId: 180, designation: 'Executive - Lead Generation' },
                { employeeId: 181, designation: 'Digital Marketing Head' },
                { employeeId: 182, designation: 'Software Engineer - QA' },
                { employeeId: 183, designation: 'Software Engineer - QA' },
                { employeeId: 185, designation: 'Sr. Executive - Talent Acquisition' },
                { employeeId: 186, designation: 'Sr. Software Engineer' },
                { employeeId: 187, designation: 'Sr. Executive - Lead Generation' },
                { employeeId: 188, designation: 'Asst. Manager - Talent Acquisition' },
                { employeeId: 189, designation: 'Software Engineer' },
                { employeeId: 190, designation: 'Sr. Software Engineer' },
                { employeeId: 191, designation: 'Software Engineer' },
                { employeeId: 192, designation: 'Executive - Talent Acquisition' },
                { employeeId: 193, designation: 'SEO Executive' },
                { employeeId: 194, designation: 'Software Engineer - QA' }
            ];

        designationList.map(async obj => {
            return await db.collection('users').updateOne({ employeeId: obj.employeeId }, {
                $set: { designation: obj.designation }
            });
        });

    }
};
