import React from 'react';
import { render, fireEvent } from 'react-testing-library';
import PLIDetails from '../index';

describe('PLIDetails', () => {
  // Mock data for testing
  const mockProfile = {
    projectName: 'Test Project',
    projectType: 'Rated',
  };

  const mockSprintHeaders = ['Sprint 1', 'Sprint 2', 'Sprint 3'];

  const mockPliData = {
    'Sprint 1': {
      'Code Quality': {
        value: '4.5',
        calculation: '90%',
        weightageAverage: '36',
      },
      Delivery: {
        value: '4.0',
        calculation: '80%',
        weightageAverage: '32',
      },
    },
    'Sprint 2': {
      'Code Quality': {
        value: '4.2',
        calculation: '84%',
        weightageAverage: '33.6',
      },
      Delivery: {
        value: '4.3',
        calculation: '86%',
        weightageAverage: '34.4',
      },
    },
    'Sprint 3': {
      'Code Quality': {
        value: '4.7',
        calculation: '94%',
        weightageAverage: '37.6',
      },
      Delivery: {
        value: '4.5',
        calculation: '90%',
        weightageAverage: '36',
      },
    },
  };

  const mockComments = 'Test comments for the PLI';

  // Default props for testing
  const defaultProps = {
    profile: mockProfile,
    pliData: mockPliData,
    sprintHeaders: mockSprintHeaders,
    comments: mockComments,
    isEditing: false,
    setComments: jest.fn(),
    toggleEditing: jest.fn(),
    isEditable: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with all props', () => {
    const { getByText, container } = render(<PLIDetails {...defaultProps} />);

    // Check if the title is rendered correctly
    expect(getByText('Test Project - Rated')).toBeTruthy();

    // Check if parameters are rendered
    expect(getByText('Parameters')).toBeTruthy();
    expect(getByText('Code Quality')).toBeTruthy();
    expect(getByText('Delivery')).toBeTruthy();

    // Check if sprint headers are rendered
    mockSprintHeaders.forEach(header => {
      expect(getByText(header)).toBeTruthy();
    });

    // Check if calculation and weightage headers are rendered
    expect(getByText('Calculation')).toBeTruthy();
    expect(getByText('Weightage Avg')).toBeTruthy();

    // Check if values are rendered
    expect(getByText('4.5')).toBeTruthy();
    expect(getByText('90%')).toBeTruthy();
    expect(getByText('36')).toBeTruthy();

    // Check if comments section is rendered
    expect(getByText('Comments')).toBeTruthy();

    // Check if the textarea has the correct value
    const textarea = container.querySelector('textarea');
    expect(textarea).toBeTruthy();
    expect(textarea.value).toBe(mockComments);

    // Check if save button is rendered when isEditable is true
    expect(getByText('Save')).toBeTruthy();
  });

  it('returns null when pliData is missing', () => {
    const { container } = render(
      <PLIDetails {...defaultProps} pliData={null} />,
    );
    expect(container.firstChild).toBeNull();
  });

  it('returns null when sprintHeaders is empty', () => {
    const { container } = render(
      <PLIDetails {...defaultProps} sprintHeaders={[]} />,
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders N/A for missing values', () => {
    const incompleteData = {
      'Sprint 1': {
        'Code Quality': {
          // Missing value
          calculation: '90%',
          weightageAverage: '36',
        },
      },
      'Sprint 2': {
        // No data for Sprint 2
      },
    };

    const { getAllByText } = render(
      <PLIDetails
        {...defaultProps}
        pliData={incompleteData}
        sprintHeaders={['Sprint 1', 'Sprint 2']}
      />,
    );

    // Check if N/A is rendered for missing values
    const naElements = getAllByText('N/A');
    expect(naElements.length).toBeGreaterThan(0);
  });

  it('handles comments change correctly', () => {
    const mockSetComments = jest.fn();
    const { container } = render(
      <PLIDetails {...defaultProps} setComments={mockSetComments} isEditing />,
    );

    // Find the textarea and change its value
    const textarea = container.querySelector('textarea');
    fireEvent.change(textarea, { target: { value: 'New comment' } });

    // Check if setComments was called with the new value
    expect(mockSetComments).toHaveBeenCalledWith('New comment');
  });

  it('disables textarea when not editing or not editable', () => {
    // Case 1: Not editing
    const { container, rerender } = render(
      <PLIDetails {...defaultProps} isEditing={false} isEditable />,
    );

    let textarea = container.querySelector('textarea');
    expect(textarea.disabled).toBe(true);

    // Case 2: Not editable
    rerender(<PLIDetails {...defaultProps} isEditing isEditable={false} />);

    textarea = container.querySelector('textarea');
    expect(textarea.disabled).toBe(true);

    // Case 3: Both editing and editable
    rerender(<PLIDetails {...defaultProps} isEditing isEditable />);

    textarea = container.querySelector('textarea');
    expect(textarea.disabled).toBe(false);
  });

  it('shows different placeholder text based on isEditable', () => {
    // Case 1: Editable
    const { container, rerender } = render(
      <PLIDetails {...defaultProps} isEditable comments="" />,
    );

    let textarea = container.querySelector('textarea');
    expect(textarea.placeholder).toBe('Enter your comments here...');

    // Case 2: Not editable
    rerender(<PLIDetails {...defaultProps} isEditable={false} comments="" />);

    textarea = container.querySelector('textarea');
    expect(textarea.placeholder).toBe('Comments are read-only in this view');
  });

  it('calls toggleEditing when edit icon is clicked', () => {
    const mockToggleEditing = jest.fn();
    const { container } = render(
      <PLIDetails {...defaultProps} toggleEditing={mockToggleEditing} />,
    );

    // Find the edit icon and click it
    const editIcon = container.querySelector('.anticon-edit');
    fireEvent.click(editIcon);

    // Check if toggleEditing was called
    expect(mockToggleEditing).toHaveBeenCalledTimes(1);
  });

  it('does not show edit icon when not editable', () => {
    const { container } = render(
      <PLIDetails {...defaultProps} isEditable={false} />,
    );

    // Edit icon should not be present
    const editIcon = container.querySelector('.anticon-edit');
    expect(editIcon).toBeNull();
  });

  it('does not show save button when not editable', () => {
    const { queryByText } = render(
      <PLIDetails {...defaultProps} isEditable={false} />,
    );

    // Save button should not be present
    const saveButton = queryByText('Save');
    expect(saveButton).toBeNull();
  });

  it('handles weightage correctly when weightageAverage is undefined but weightage is defined', () => {
    const dataWithWeightage = {
      'Sprint 1': {
        'Code Quality': {
          value: '4.5',
          calculation: '90%',
          // No weightageAverage, only weightage
          weightage: '40',
        },
      },
    };

    const { getByText } = render(
      <PLIDetails
        {...defaultProps}
        pliData={dataWithWeightage}
        sprintHeaders={['Sprint 1']}
      />,
    );

    // Should display the weightage value
    expect(getByText('40')).toBeTruthy();
  });

  it('applies scrollable style when there are many columns', () => {
    // Create many sprint headers to force scrolling
    const manySprintHeaders = [
      'Sprint 1',
      'Sprint 2',
      'Sprint 3',
      'Sprint 4',
      'Sprint 5',
      'Sprint 6',
      'Sprint 7',
      'Sprint 8',
    ];

    // Create corresponding data
    const largeData = {};
    manySprintHeaders.forEach(sprint => {
      largeData[sprint] = {
        'Code Quality': {
          value: '4.5',
          calculation: '90%',
          weightageAverage: '36',
        },
      };
    });

    const { container } = render(
      <PLIDetails
        {...defaultProps}
        pliData={largeData}
        sprintHeaders={manySprintHeaders}
      />,
    );

    // Check if the container has overflow-x: auto style
    const scrollableDiv = container.querySelector(
      'div[style*="overflow-x: auto"]',
    );
    expect(scrollableDiv).toBeTruthy();
  });

  it('renders correctly with minimal data', () => {
    const minimalData = {
      'Sprint 1': {
        'Single Parameter': {
          value: '3.0',
          calculation: '60%',
          weightageAverage: '24',
        },
      },
    };

    const { getByText } = render(
      <PLIDetails
        {...defaultProps}
        pliData={minimalData}
        sprintHeaders={['Sprint 1']}
        comments=""
      />,
    );

    // Check if the minimal data is rendered correctly
    expect(getByText('Single Parameter')).toBeTruthy();
    expect(getByText('3.0')).toBeTruthy();
    expect(getByText('60%')).toBeTruthy();
    expect(getByText('24')).toBeTruthy();
  });
});
