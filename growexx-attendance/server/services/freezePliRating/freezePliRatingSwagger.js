const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    // Freeze PLI Rating Endpoint
    swaggerJson.paths['/api/pli/freeze'] = {
        post: {
            security: [
                {
                    bearerAuth: []
                }
            ],
            tags: ['PLI Rating'],
            description:
        'Freeze a PLI rating, making it immutable. Super admins can also override and freeze PLI ratings for any mentee.',
            summary: 'Freeze a PLI rating',
            parameters: [
                {
                    in: 'body',
                    name: 'body',
                    description: 'PLI rating freeze request',
                    required: true,
                    schema: {
                        $ref: '#/definitions/freezePliRatingRequest'
                    }
                }
            ],
            responses: {
                200: {
                    description: 'PLI rating frozen successfully.',
                    schema: {
                        $ref: '#/definitions/successFreezePliRating'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized',
                    schema: {
                        $ref: '#/definitions/unauthorizedError'
                    }
                },
                404: {
                    description: 'PLI Rating not found',
                    schema: {
                        $ref: '#/definitions/notFoundError'
                    }
                }
            }
        }
    };

    // Accept PLI Rating Endpoint
    swaggerJson.paths['/api/pli/accept'] = {
        post: {
            security: [
                {
                    bearerAuth: []
                }
            ],
            tags: ['PLI Rating'],
            description:
        'Accept a frozen PLI rating, indicating agreement with the assessment.',
            summary: 'Accept a frozen PLI rating',
            parameters: [
                {
                    in: 'body',
                    name: 'body',
                    description: 'PLI rating accept request',
                    required: true,
                    schema: {
                        $ref: '#/definitions/acceptPliRatingRequest'
                    }
                }
            ],
            responses: {
                200: {
                    description: 'PLI rating accepted successfully.',
                    schema: {
                        $ref: '#/definitions/successAcceptPliRating'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized',
                    schema: {
                        $ref: '#/definitions/unauthorizedError'
                    }
                },
                404: {
                    description: 'PLI Rating not found',
                    schema: {
                        $ref: '#/definitions/notFoundError'
                    }
                }
            }
        }
    };

    // Define request schema for freezePliRating
    swaggerJson.definitions.freezePliRatingRequest = {
        type: 'object',
        required: ['pliRatingId', 'mentorId'],
        properties: {
            pliRatingId: {
                type: 'string',
                example: '507f1f77bcf86cd799439011',
                description: 'The ID of the PLI rating to freeze'
            },
            mentorId: {
                type: 'string',
                example: '507f1f77bcf86cd799439012',
                description: 'The ID of the mentor freezing the PLI rating'
            },
            menteeId: {
                type: 'string',
                example: '507f1f77bcf86cd799439013',
                description: 'The ID of the mentee (required for Super Admin override)'
            },
            superAdminOverride: {
                type: 'boolean',
                example: false,
                description: 'Flag indicating if this is a Super Admin override'
            },
            month: {
                type: 'integer',
                example: 5,
                description: 'Month number (1-12) for Super Admin override'
            },
            year: {
                type: 'integer',
                example: 2025,
                description: 'Year for Super Admin override'
            }
        }
    };

    // Define request schema for acceptPliRating
    swaggerJson.definitions.acceptPliRatingRequest = {
        type: 'object',
        required: ['pliRatingId', 'menteeId'],
        properties: {
            pliRatingId: {
                type: 'string',
                example: '507f1f77bcf86cd799439011',
                description: 'The ID of the PLI rating to accept'
            },
            menteeId: {
                type: 'string',
                example: '507f1f77bcf86cd799439013',
                description: 'The ID of the mentee accepting the PLI rating'
            }
        }
    };

    // Define response schema for freezePliRating
    swaggerJson.definitions.successFreezePliRating = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                properties: {
                    status: {
                        type: 'string',
                        example: 'success'
                    },
                    data: {
                        type: 'object',
                        properties: {
                            _id: {
                                type: 'string',
                                example: '683c367e91e89d4224840c06'
                            },
                            menteeId: {
                                type: 'string',
                                example: '62b2c2c339e3367ae40f9dad'
                            },
                            mentorId: {
                                type: 'string',
                                example: '60c9ad01e3aa776b11d57edb'
                            },
                            month: {
                                type: 'integer',
                                example: 4
                            },
                            year: {
                                type: 'integer',
                                example: 2025
                            },
                            projectRatings: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        projectId: {
                                            type: 'string',
                                            example: '66b0e41b2d42e3e3b34ee150'
                                        },
                                        projectWeightage: {
                                            type: 'number',
                                            example: 100
                                        },
                                        parameterScores: {
                                            type: 'array',
                                            items: {
                                                type: 'object',
                                                properties: {
                                                    parameterId: {
                                                        type: 'string',
                                                        example: '682da959baf382b483a89aed'
                                                    },
                                                    projectType: {
                                                        type: 'string',
                                                        example: 'Fixed'
                                                    },
                                                    parentParameter: {
                                                        type: 'string',
                                                        example: 'Project'
                                                    },
                                                    autoFilled: {
                                                        type: 'boolean',
                                                        example: true
                                                    },
                                                    comments: {
                                                        type: 'string',
                                                        example: ''
                                                    },
                                                    childScores: {
                                                        type: 'array',
                                                        items: {
                                                            type: 'object',
                                                            properties: {
                                                                childParameterId: {
                                                                    type: 'string',
                                                                    example: 'RAG (Budget + Open/Close + B2D + Escalations)'
                                                                },
                                                                sprintScores: {
                                                                    type: 'array',
                                                                    items: {
                                                                        type: 'object',
                                                                        properties: {
                                                                            sprintNumber: {
                                                                                type: 'string',
                                                                                example: 'AMNI Sprint 16'
                                                                            },
                                                                            score: {
                                                                                type: 'number',
                                                                                example: -1
                                                                            },
                                                                            comment: {
                                                                                type: 'string',
                                                                                example: ''
                                                                            },
                                                                            _id: {
                                                                                type: 'string',
                                                                                example: '683c36bf91e89d4224841d2d'
                                                                            }
                                                                        }
                                                                    }
                                                                },
                                                                _id: {
                                                                    type: 'string',
                                                                    example: '683c36bf91e89d4224841d2c'
                                                                }
                                                            }
                                                        }
                                                    },
                                                    _id: {
                                                        type: 'string',
                                                        example: '683c36bf91e89d4224841d2b'
                                                    }
                                                }
                                            }
                                        },
                                        _id: {
                                            type: 'string',
                                            example: '683c36bf91e89d4224841d2a'
                                        }
                                    }
                                }
                            },
                            status: {
                                type: 'string',
                                example: 'Finalized',
                                enum: ['Draft', 'Submitted', 'Finalized', 'Accepted']
                            },
                            superAdminOverride: {
                                type: 'boolean',
                                example: true
                            },
                            isFrozen: {
                                type: 'boolean',
                                example: true
                            },
                            createdAt: {
                                type: 'string',
                                format: 'date-time',
                                example: '2025-06-01T11:16:14.256Z'
                            },
                            updatedAt: {
                                type: 'string',
                                format: 'date-time',
                                example: '2025-06-01T16:26:28.617Z'
                            },
                            proactivenessScores: {
                                type: 'array',
                                items: {},
                                example: []
                            },
                            __v: {
                                type: 'number',
                                example: 2
                            }
                        }
                    }
                }
            },
            message: {
                type: 'string',
                example: 'Success'
            }
        }
    };

    // Define response schema for acceptPliRating
    swaggerJson.definitions.successAcceptPliRating = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                properties: {
                    _id: {
                        type: 'string',
                        example: '507f1f77bcf86cd799439011'
                    },
                    mentorId: {
                        type: 'string',
                        example: '507f1f77bcf86cd799439012'
                    },
                    menteeId: {
                        type: 'string',
                        example: '507f1f77bcf86cd799439013'
                    },
                    isFrozen: {
                        type: 'boolean',
                        example: true
                    },
                    status: {
                        type: 'string',
                        example: 'Accepted',
                        enum: ['Draft', 'Submitted', 'Finalized', 'Accepted']
                    },
                    updatedAt: {
                        type: 'string',
                        example: '2025-05-27T10:30:00.000Z'
                    }
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    return swaggerJson;
};
