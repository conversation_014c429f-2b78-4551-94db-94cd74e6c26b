const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for freezing PLI Rating
 */
class FreezePliRatingValidator {
    constructor (body, locale) {
        this.body = body;
        this.locale = locale;
    }

    /**
     * @desc This function validates the request to freeze a PLI rating
     * <AUTHOR>
     * @since 27/05/2025
     */
    async validateFreezePliRating () {
        const { pliRatingId, mentorId } = this.body;

        // Validate PLI Rating ID
        if (!pliRatingId) {
            throw new GeneralError(this.locale('PLI_RATING_ID_REQUIRED'));
        }
        if (!validation.isValidObjectId(pliRatingId)) {
            throw new GeneralError(this.locale('INVALID_PLI_RATING_ID'));
        }

        // Validate Mentor ID
        if (!mentorId) {
            throw new GeneralError(this.locale('MENTOR_ID_REQUIRED'));
        }
        if (!validation.isValidObjectId(mentorId)) {
            throw new GeneralError(this.locale('INVALID_MENTOR_ID'));
        }
    }

    /**
     * @desc This function validates the request to accept a PLI rating
     * <AUTHOR>
     * @since 27/05/2025
     */
    async validateAcceptPliRating () {
        const { pliRatingId, menteeId } = this.body;

        // Validate PLI Rating ID
        if (!pliRatingId) {
            throw new GeneralError(this.locale('PLI_RATING_ID_REQUIRED'));
        }
        if (!validation.isValidObjectId(pliRatingId)) {
            throw new GeneralError(this.locale('INVALID_PLI_RATING_ID'));
        }

        // Validate Mentee ID
        if (!menteeId) {
            throw new GeneralError(this.locale('MENTEE_ID_REQUIRED'));
        }
        if (!validation.isValidObjectId(menteeId)) {
            throw new GeneralError(this.locale('INVALID_MENTEE_ID'));
        }
    }
}

module.exports = FreezePliRatingValidator;
