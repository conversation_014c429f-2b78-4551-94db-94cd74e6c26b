
const AddJiraPortalService = require('./addJiraPortalService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add jira portal
 */
class AddJiraPortalController {

    /**
     * @desc This function is being used to add jira portal
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addJiraPortal (req, res) {
        try {
            const data = await AddJiraPortalService.addJiraPortal(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddJiraPortalController;
