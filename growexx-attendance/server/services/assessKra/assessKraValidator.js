const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
/**
 * Class represents validations for KRA.
 */
class AssessKraValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.REQUIRED = REQUIRED;
        this.INVALID = INVALID;
        this.body = body;
    }

    /**
     * @desc This function is being used to validate assign a KRA
     * <AUTHOR>
     * @since 13/07/2022
     */
    validationAssessKra () {
        const { userKraId, selfAssessmentEndDate,
            reviewManagerAssessmentEndDate, reportingManagerAssessmentEndDate } = this.body;
        this.userKrasId(userKraId);
        this.assessmentEndDate(selfAssessmentEndDate, 'Self Assessment End Date');
        this.assessmentEndDate(reportingManagerAssessmentEndDate, 'Reporting Manager Assessment End Date');
        this.assessmentEndDate(reviewManagerAssessmentEndDate, 'Review Manager Assessment End Date');
    }

    /**
     * @desc This funciton is used to validate assessment end date
     * <AUTHOR>
     * @since 13/07/2022
     * @param {String} endDate endDate
     */

    assessmentEndDate (endDate, field) {
        const selectedEndDate = MOMENT(endDate, 'YYYY-MM-DD');
        const currentDate = MOMENT();

        if (!endDate) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
        if (!currentDate.isBefore(selectedEndDate)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    userKrasId (userKraId) {
        if (!userKraId || !userKraId.length) {
            throw new GeneralError(this.__(REQUIRED, 'userKraId'), 400);
        }
        for (const id of userKraId) {
            super.checkValidMongoId(id, 'userKraId');
        }
    }
}
module.exports = AssessKraValidator;
