module.exports = {
    addjiraPortal: [{
        it: 'As a admin I should validate if url is not pass',
        options: {
            name: 'Growexx'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if name is pass as blank',
        options: {
            url: 'A'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if url is pass as blank',
        options: {
            name: 'Growexx',
            url: 'A'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if url is pass but invalid',
        options: {
            name: 'Growexx',
            url: 'A'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if email is not pass',
        options: {
            name: '<PERSON>rowex<PERSON>',
            url: 'growbyexx.atlassian.net'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if email is pass but empty',
        options: {
            name: 'Growexx',
            url: 'growbyexx.atlassian.net',
            email: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if token is not pass',
        options: {
            name: '<PERSON><PERSON>ex<PERSON>',
            url: 'growbyexx.atlassian.net',
            email: '<EMAIL>'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if token is pass but empty',
        options: {
            url: 'growbyexx.atlassian.net',
            email: '<EMAIL>',
            token: ''
        },
        status: 0
    },
    {
        it: 'As a Admin, I should not able to add already added url',
        options: {
            name: 'Growexx',
            url: 'growexx.atlassian.net',
            email: '<EMAIL>',
            token: 'something'
        },
        status: 0
    }]
};
