const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const AuthMiddleWareManager = require('../middleware/managerAuth');
const ListProjectController = require('../services/listProject/listProjectController');
const AddProjectController = require('../services/addProject/addProjectController');
const EditProjectController = require('../services/editProject/editProjectController');
const DownloadPersonHoursReportController = require('../services/downloadProjectPersonHoursReport/downloadPersonHoursReportController');
const ChangeProjectStatusController = require('../services/changeStatusProject/changeStatusProjectController');

router.get('/', Auth<PERSON>iddle<PERSON>are, ACLMiddleWare, ListProjectController.listProjects);
router.get('/all', AuthMiddleWare, ACLMiddleWare, ListProjectController.allProjects);
router.get('/all/logs', AuthMiddleWare, ACLMiddleWare, ListProjectController.allLogProjects);
router.post('/', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, AddProjectController.addProject);
router.put('/', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, EditProjectController.editProject);
router.get('/person-day/download', AuthMiddleWare, ACLMiddleWare, DownloadPersonHoursReportController.downloadProjectPersonHoursReport);
router.post('/person-day/reset-headers', AuthMiddleWare, ACLMiddleWare,
    UploadMiddleWare.single('file'), DownloadPersonHoursReportController.resetProjectPersonHoursReportHeaders);
router.patch('/status', AuthMiddleWare, ACLMiddleWare, ChangeProjectStatusController.changeProjectStatus);
router.get('/all-by-log-count', AuthMiddleWare, ListProjectController.allProjectsByLogCount);

module.exports = router;
