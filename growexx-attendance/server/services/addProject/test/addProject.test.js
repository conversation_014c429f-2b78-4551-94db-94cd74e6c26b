const TestCase = require('./addProject');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Add project', () => {

    TestCase.addProject.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .post('/project')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should able to add project', async () => {
        const data = {
            projectName: 'Project Test 1',
            pmUser: '5f083c352a7908662c335552',
            reviewManager: '5f083c352a7908662c335553',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            hasReviewManager: true,
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to add project without review manager', async () => {
        const data = {
            projectName: 'Project Test 11',
            pmUser: '5f083c352a7908662c335552',
            hasReviewManager: false,
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to add project without project end date', async () => {
        const data = {
            projectName: 'Project Test 111',
            pmUser: '5f083c352a7908662c335552',
            reviewManager: '5f083c352a7908662c335553',
            hasReviewManager: true,
            startDate: '2022-07-07',
            endDate: null,
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to add project', async () => {
        const data = {
            projectName: 'Project Test 2',
            pmUser: '5f083c352a7908662c335552',
            reviewManager: '5f083c352a7908662c335553',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });
    it('As a Admin, while adding project it should throw an error if review manager is invalid', async () => {
        const data = {
            projectName: 'Project Test 199',
            pmUser: '5f083c352a7908662c335551',
            reviewManager: '62c2e83e5af6e6b8f4f63322',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });

    it('As a Admin, while adding project it should throw an error if reporting manager is invalid', async () => {
        const data = {
            projectName: 'Project Test 158',
            pmUser: '62c2e83e5af6e6b8f4f63322',
            reviewManager: '5f083c352a7908662c335551',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });

    it('As a Admin, while adding project it should throw an error if reporting manager is not passed', async () => {
        const data = {
            projectName: 'Project Test 158',
            pmUser: '',
            reviewManager: '5f083c352a7908662c335551',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });
    it('As a Admin, while adding project it should throw an error if employee role is invalid', async () => {
        const data = {
            projectName: 'Project Test 158',
            pmUser: '62c2e83e5af6e6b8f4f63322',
            reviewManager: '5f083c352a7908662c335551',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });
    it('As a Admin, while adding project it should throw an error if employee list is empty', async () => {
        const data = {
            projectName: 'Project Test 158',
            pmUser: '62c2e83e5af6e6b8f4f63322',
            reviewManager: '5f083c352a7908662c335551',
            startDate: '2022-07-07',
            endDate: '2022-08-07',
            businessUnitId: '640c9ae585c55dd4f0fdfc56'
        };
        const res = await request(process.env.BASE_URL)
            .post('/project')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });
});
