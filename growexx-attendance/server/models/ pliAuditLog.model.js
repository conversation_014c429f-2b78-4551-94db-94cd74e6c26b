/**
 * @name PLI Audit Log model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    action: {
        type: String,
        required: true,
        enum: ['Create', 'Update', 'Delete', 'StatusChange', 'OverrideScore']
    },
    entityType: {
        type: String,
        required: true,
        enum: ['PLIParameter', 'PLIRating', 'TechRoadmap']
    },
    entityId: {
        type: appMongoose.Schema.Types.ObjectId,
        required: true
    },
    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    previousValue: {
        type: Object
    },
    newValue: {
        type: Object
    },
    timestamp: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('pliAuditLog', schema);