const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');
const mongoose = require('mongoose');

// Fix for Mongoose deprecation warning
mongoose.set('strictQuery', false);

const Logs = require('../server/models/logs.model');
const User = require('../server/models/user.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: true,
    maxPoolSize: 10,
};

const connectDB = async () => {
    try {
        await mongoose.connect(dbUrl, options);
        console.log('MongoDB connected successfully.');
        return true;
    } catch (err) {
        CONSOLE_LOGGER.error('MongoDB connection error:', err);
        return false;
    }
};

const notifyRejectedLogs = async () => {
    try {
        const dbConnected = await connectDB();
        if (!dbConnected) {
            console.error('Database connection failed. Exiting process.');
            process.exit(1);
        }

        const currentDate = new Date();
        const startDate = new Date(currentDate.setHours(0, 0, 0, 0));
        const endDate = new Date(currentDate.setHours(23, 59, 59, 999));

        // Fetch logs with logStatus = 2 (rejected) and lastActionPerformedTimestamp = current date
        const rejectedLogs = await Logs.find({
            logStatus: 2,
            lastActionPerformedTimestamp: { $gte: startDate, $lte: endDate },
        })
            .select('_id userId jiraIssueUrl timeSpentHours logDate')
            .lean();

        if (rejectedLogs.length === 0) {
            console.log('No rejected logs found for today.');
            process.exit(0);
        }

        // Extract unique user IDs from logs
        const userIds = [...new Set(rejectedLogs.map(log => log.userId))];

        // Fetch user details for these user IDs
        const users = await User.find({ _id: { $in: userIds } }).lean();

        // Map user IDs to their corresponding logs
        const logsByUser = rejectedLogs.reduce((acc, log) => {
            acc[log.userId] = acc[log.userId] || [];
            acc[log.userId].push(log);
            return acc;
        }, {});

        // Prepare and send emails
        const appUrl = process.env.FRONTEND_URL;
        await Promise.all(
            users.map(async user => {
                const userLogs = logsByUser[user._id.toString()];
                if (userLogs && userLogs.length > 0) {
                    const tableData = bindLogData(userLogs);
                    const templateVariables = {
                        userName: `${user.firstName} ${user.lastName}`,
                        actionURL: `${appUrl}/logs`,
                        tableData: tableData,
                    };
                    const subject = 'Action Required: Log(s) Rejected by Reporting Manager';
                    const template = 'emailTemplates/rejectedWorkLogs.html';

                    try {
                        await Email.prepareAndSendEmail(
                            // [user.email],   // Uncomment for production and remove hardcoded values
                            ['<EMAIL>', '<EMAIL>'],
                            subject,
                            template,
                            templateVariables
                        );
                        console.log(`Email sent to ${user.email}`);
                    } catch (error) {
                        console.error(`Failed to send email to ${user.email}:`, error);
                    }
                }
            })
        );

        console.log('Notification emails sent successfully.');
        process.exit(0);
    } catch (error) {
        console.error('Error sending email notifications:', error);
        process.exit(1);
    }
};

function bindLogData(userLogs) {
    let tableData = '';
    userLogs.forEach(log => {
        const formattedDate = log.logDate
            ? new Date(log.logDate).toLocaleDateString('en-IN', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
            })
            : 'N/A';
        tableData += `<tr>
      <td style="font-size: 14px; padding: 10px; color: #000000; border: 1px solid #eeeeee; text-align: center;">
          ${formattedDate}
      </td>
      <td style="font-size: 14px; padding: 10px; color: #000000; border: 1px solid #eeeeee; text-align: center;">
          ${log.timeSpentHours || 'N/A'} hours
      </td>
      <td style="font-size: 14px; padding: 10px; color: #000000; border: 1px solid #eeeeee; text-align: center;">
        <a href="${log.jiraIssueUrl}" target="_blank">
            ${log.jiraIssueUrl ? log.jiraIssueUrl.split('/').pop() : 'N/A'}
        </a>
      </td>    
    </tr>`;
    });
    return tableData;
}

notifyRejectedLogs();
