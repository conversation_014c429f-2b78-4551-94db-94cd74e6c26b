const Utils = require('../util/utilFunctions');
const Project = require('../models/project.model');
const HTTPStatus = require('../util/http-status');

/**
 * @desc This function is being used to authenticate review manager and reporting manager
 * <AUTHOR>
 * @since 15/09/2022
 * @param {Object} req Request req.headers RequestBody req.headers.accessToken accessToken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */
module.exports = function (req, res, next) {
    Project.countDocuments({ $or: [ { pmUser: { $eq: res.locals.user._id } }, { reviewManager: { $eq: res.locals.user._id } } ] })
        .then((isValidUser) => {
            const responseObject = Utils.errorResponse();
            if (!isValidUser && res.locals.user.role !== CONSTANTS.ROLE.ADMIN && res.locals.user.role !== CONSTANTS.ROLE.BU) {
                responseObject.message = res.__('ACCESS_DENIED');
                res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
            } else {
                next();
            }
        }).catch(next);
};
