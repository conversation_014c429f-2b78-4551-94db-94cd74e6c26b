/**
 * @name Logs model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'users'
    },
    projectId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'portal'
    },
    issueId: {
        type: String
    },
    jiraLogId: {
        type: String
    },
    projectName: {
        type: String
    },
    jiraUserId: {
        type: String
    },
    jiraUserEmail: {
        type: String
    },
    jiraUserTimeZone: {
        type: String
    },
    label: {
        type: String
    },
    logDate: {
        type: Date
    },
    timeSpentSeconds: {
        type: Number
    },
    timeSpentHours: {
        type: Number
    },
    daysDeviation: {
        type: Number
    },
    deviations: {
        type: Number
    },
    jiraProjectName: {
        type: String
    },
    jiraIssueUrl: {
        type: String
    },
    isActive: {
        type: Number,
        default: 1,
        // 0 = deactive, 1 = active, 2 = Deleted
        enum: [0, 1, 2]
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    jiraTitle: {
        type: String,
        default: null
    },
    jiraDescription: {
        type: String,
        default: null
    },
    logStatus: {
        type: Number,
        default: 0,
        // 0 = pending, 1 = approve, 2=reject, 3= reopen
        enum: [0, 1, 2, 3]
    },
    lastActionPerformedBy: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'users'
    },
    lastActionPerformedTimestamp: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('log', schema);
