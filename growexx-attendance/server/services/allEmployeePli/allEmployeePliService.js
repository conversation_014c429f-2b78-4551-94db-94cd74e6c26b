/**
 * Class represents services for fetching all employee PLI data.
 * <AUTHOR>
 */
const User = require('../../models/user.model');
const PliRating = require('../../models/pliRating.model');
const moment = require('moment');

/**
 * Class represents services for All Employee PLI data
 */
class AllEmployeePliService {
    /**
     * Get all employees details along with their PLI ratings
     * @returns {object} Employee profile data with PLI ratings
     */
    static async getAllEmployeeProfile () {
        // Get all active users first
        const users = await User.find(
            { isActive: 1 },
            { _id: 1, firstName: 1, lastName: 1, businessUnit: 1, department: 1, employeeId: 1 }
        );

        // Map to store formatted user data
        const userMap = {};

        // Format the user data
        users.forEach(user => {
            userMap[user._id.toString()] = {
                menteeId: user._id,
                name: `${user.firstName} ${user.lastName}`,
                employeeId: user.employeeId,
                department: user.department || user.businessUnit || '',
                reporteeId: '',
                status: 'Not Started',
                finalScore: null,
                pliCycle: null
            };
        });
        // console.log('User Map:', userMap);

        // Get all PLI ratings
        const pliRatings = await PliRating.find(
            {},
            {
                menteeId: 1,
                month: 1,
                projectRatings: 1,
                status: 1,
                finalScore: 1
            }
        ).sort({ createdAt: -1 });


        // For each PLI rating, update the user data
        pliRatings.forEach(rating => {
            const menteeId = rating.menteeId.toString();
            userMap[menteeId].status = rating.status;
            userMap[menteeId].finalScore = rating.finalScore;
            userMap[menteeId].pliCycle = moment().month(rating.month - 1).format('MMMM'); // pliCycle = pliRatings.month;

        });

        // Convert map to array
        return Object.values(userMap);
    }
}

module.exports = AllEmployeePliService;
