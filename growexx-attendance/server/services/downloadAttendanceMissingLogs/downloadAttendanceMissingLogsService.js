const mongoose = require("mongoose");
const Logs = require("../../models/logs.model");
const User = require("../../models/user.model");
const Project = require("../../models/project.model");
const BusinessUnit = require("../../models/bu.model");
const Leave = require("../../models/leave.model");
const { parse } = require("json2csv");
const CONSTANTS = require("../../util/constants");
const MOMENT = require("moment");

/**
 * Class represents services for download user attendance with missing logs reporting.
 */
class DownloadAttendanceMissingLogsService {
  /**
   * @desc This function is being used to download user attendance with missing logs reporting
   * <AUTHOR>
   * @since 25/03/2021
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @param {Object} locale Locale passed from request
   * @return {Object} response Success response with CSV data and headers
   */
  static async downloadAttendanceMissingLogs(req, user) {
    const where = {};
    let buDetails = "";
    
    // Check if user is BU and get business unit details
    if (user.role === CONSTANTS.ROLE.BU) {
      buDetails = await BusinessUnit.findOne(
        { userId: user._id },
        { _id: 1 }
      ).lean();
    }

    // Check if user is manager
    const isUserManager = await Project.countDocuments({
      $or: [
        { pmUser: { $eq: user._id } },
        { reviewManager: { $eq: user._id } },
        { businessUnitId: { $eq: buDetails && buDetails._id ? buDetails._id : null } },
      ],
    });

    // Set date range
    const startDate = req.query.startDate
      ? MOMENT(req.query.startDate).startOf("day")._d
      : MOMENT().startOf("month")._d;
    const endDate = req.query.endDate
      ? MOMENT(req.query.endDate).endOf("day")._d
      : MOMENT().endOf("month")._d;

    // Get all active employees
    let employeeFilter = { isActive: CONSTANTS.STATUS.ACTIVE, isDelete: 0 };
    
    // If not admin or manager, restrict to current user
    if (user.role !== CONSTANTS.ROLE.ADMIN && !isUserManager) {
      employeeFilter._id = user._id;
    }
    
    // If specific userId is requested and user has permission
    if (req.query.userId && (user.role === CONSTANTS.ROLE.ADMIN || isUserManager)) {
      employeeFilter._id = mongoose.Types.ObjectId(req.query.userId);
    }

    const activeEmployees = await User.find(employeeFilter, {
      _id: 1,
      firstName: 1,
      lastName: 1,
      email: 1,
      employeeId: 1
    }).lean();

    // Get all logs for the date range
    const logWhere = {
      logDate: {
        $gte: startDate,
        $lte: endDate,
      },
      userId: { $in: activeEmployees.map(emp => emp._id) }
    };

    const aggregateParams = [
      {
        $match: logWhere,
      },
      {
        $group: {
          _id: {
            userId: "$userId",
            date: { $dateToString: { format: "%Y-%m-%d", date: "$logDate" } },
          },
          totalHours: {
            $sum: "$timeSpentHours",
          },
        },
      },
    ];

    const logs = await Logs.aggregate(aggregateParams);

    // Get all leaves for the date range
    const leaveWhere = {
      leaveDate: {
        $gte: startDate,
        $lte: endDate,
      },
      userId: { $in: activeEmployees.map(emp => emp._id) },
      isDelete: 0
    };

    const leaveAggregateParams = [
      {
        $match: leaveWhere,
      },
      {
        $group: {
          _id: {
            userId: "$userId",
            date: { $dateToString: { format: "%Y-%m-%d", date: "$leaveDate" } },
          },
          totalLeaveHours: {
            $sum: "$timeSpentHours",
          },
          leaveType: { $first: "$leaveType" },
        },
      },
    ];

    const leaves = await Leave.aggregate(leaveAggregateParams);

    // Create maps for quick lookup
    const logsMap = {};
    logs.forEach(log => {
      const key = `${log._id.userId}_${log._id.date}`;
      logsMap[key] = log.totalHours;
    });

    const leavesMap = {};
    leaves.forEach(leave => {
      const key = `${leave._id.userId}_${leave._id.date}`;
      leavesMap[key] = {
        hours: leave.totalLeaveHours,
        type: leave.leaveType
      };
    });

    // Generate date range array (excluding weekends)
    const dateRange = [];
    let currentDate = MOMENT(startDate);
    const endMoment = MOMENT(endDate);
    
    while (currentDate.isSameOrBefore(endMoment, 'day')) {
      // Skip weekends (Saturday = 6, Sunday = 0)
      if (currentDate.day() !== 0 && currentDate.day() !== 6) {
        dateRange.push(currentDate.format('YYYY-MM-DD'));
      }
      currentDate.add(1, 'day');
    }

    // Build the report data in pivot format
    const reportData = [];
    
    activeEmployees.forEach(employee => {
      // Properly format employee name to avoid CSV splitting issues
      const employeeName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim() || 'N/A';
      
      const employeeRow = {
        'Employee Name': employeeName
      };
      
      let totalHours = 0;
      let totalMissingHours = 0;
      
      // Add status for each date
      dateRange.forEach(date => {
        const key = `${employee._id}_${date}`;
        const dailyHours = logsMap[key] || 0;
        const leaveData = leavesMap[key];
        const leaveHours = leaveData ? leaveData.hours : 0;
        const leaveType = leaveData ? leaveData.type : null;
        
        let displayValue = '';
        
        if (leaveType) {
          // If there's a leave, show the leave type
          displayValue = leaveType;
        } else if (dailyHours === 0) {
          // No leave and no hours logged
          displayValue = 'Not Added';
          totalMissingHours += 8; // Full day missing
        } else {
          // Hours are logged
          displayValue = `${dailyHours}h`;
          totalHours += dailyHours;
          
          // If less than 8 hours and no leave to cover the difference
          if (dailyHours < 8) {
            totalMissingHours += (8 - dailyHours);
          }
        }
        
        // Use date as column header - format for better readability
        const formattedDate = MOMENT(date).format('DD-MMM-YYYY');
        employeeRow[formattedDate] = displayValue;
      });
      
      // Add the new columns with proper rounding
      const formattedTotalHours = totalHours % 1 === 0 ? `${totalHours}h` : `${totalHours.toFixed(2)}h`;
      const formattedMissingHours = totalMissingHours % 1 === 0 ? `${totalMissingHours}h` : `${totalMissingHours.toFixed(2)}h`;
      
      employeeRow['Total Hours'] = formattedTotalHours;
      employeeRow['Total Missing Hours'] = formattedMissingHours;
      
      reportData.push(employeeRow);
    });

    // Sort by employee name
    reportData.sort((a, b) => {
      return (a['Employee Name'] || '').localeCompare(b['Employee Name'] || '');
    });

    // Prepare CSV fields - Employee Name first, then all dates, then totals
    const formattedDateRange = dateRange.map(date => MOMENT(date).format('DD-MMM-YYYY'));
    const fields = ['Employee Name', ...formattedDateRange, 'Total Hours', 'Total Missing Hours'];
    
    // Use proper CSV options with quotes to prevent data splitting
    const opts = { 
      fields, 
      quote: '"',
      delimiter: ',',
      eol: '\n'
    };
    const csvData = parse(reportData, opts);

    return {
      headers: [
        {
          key: "Content-Type",
          value: "text/csv",
        },
        {
          key: "Content-Disposition",
          value: "attachment; filename=attendance-missing-logs-report.csv",
        },
      ],
      data: csvData,
    };
  }
}

module.exports = DownloadAttendanceMissingLogsService; 