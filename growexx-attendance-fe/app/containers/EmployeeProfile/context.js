import React, { createContext } from 'react';
import PropTypes from 'prop-types';

export const EmployeeProfileContext = createContext(null);

export const EmployeeProfileProvider = ({ children, value }) => (
  <EmployeeProfileContext.Provider value={value}>
    {children}
  </EmployeeProfileContext.Provider>
);

EmployeeProfileProvider.propTypes = {
  children: PropTypes.node.isRequired,
  value: PropTypes.any, // or specify the exact shape/type if you know it
};
