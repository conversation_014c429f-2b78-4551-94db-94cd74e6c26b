module.exports = {
    async up (db) {
        const userKras = await db.collection('userkras').find().toArray();
        for (const d of userKras) {
            const uniqueKras = [];
            const uniqueOutcome = [];
            for (const kra of d.kras) {
                if (!uniqueOutcome.includes(kra.outcome)) {
                    uniqueKras.push(kra);
                    uniqueOutcome.push(kra.outcome);
                }
            }
            if (uniqueKras.length !== d.kras.length) {
                await db.collection('userkras').updateOne(
                    { _id: d._id },
                    {
                        $set: {
                            kras: uniqueKras
                        }
                    }
                );
            }
        }
    }
};
