module.exports = {
    // Apply the migration
    async up(db) {
        const designationsCollection = db.collection("designations");

        const DESIGNATION = [
            'Asst. Manager - HR',
            'Asst. Manager - Talent Acquisition',
            'Business Analyst',
            'Business Advisor',
            'Business Development Executive',
            'Business Development Manager',
            'Business Unit Head - Web & Mobile',
            'Business Unit Head',
            'Co-Founder',
            'Data Analyst',
            'Data Engineer',
            'Database Administrator',
            'Data Scientist',
            'Database Administrator',
            'Digital Marketing Head',
            'Director',
            'Engineer - IT & DevOps',
            'Executive - Business Development',
            'Executive - Digital Marketing',
            'Executive - HR',
            'Executive - Lead Generation',
            'Executive - Talent Acquisition',
            'Executive - Web Research',
            'Executive Assistant',
            'Head - HR & Ops',
            'Jr. Data Analyst',
            'Jr. Database Administrator',
            'Jr. Data Engineer',
            'Jr. Database Administrator',
            'Jr. Software Engineer',
            'Jr. Software Engineer - DevOps',
            'Lead - Data Engineering',
            'Lead Engineer - QA',
            'Lead - UX',
            'Management Trainee',
            'Management Trainee - HR',
            'Manager - Business Development',
            'Manager - Data Engineering',
            'Manager - HR',
            'Manager Solutions',
            'Office Boy',
            'Practice Head - Data Science and Analytics',
            'Product Manager',
            'Product Owner',
            'Project Manager',
            'Regional Head - Sales',
            'Technical Consultant',
            'Scrum Master',
            'SEO Executive',
            'Software Engineer',
            'Software Engineer - DevOps',
            'Software Engineer - QA',
            'Sr. Business Analyst',
            'Sr. Data Analyst',
            'Sr. Database Administrator',
            'Sr. Data Engineer',
            'Sr. Data Scientist',
            'Sr. Database Administrator',
            'Sr. Executive - Business Development',
            'Sr. Executive - Digital Marketing',
            'Sr. Executive - HR',
            'Sr. Executive - Lead Generation',
            'Sr. Executive - Talent Acquisition',
            'Sr. Executive - Web Research',
            'Sr. Product Owner',
            'Sr. Software Engineer',
            'Sr. Software Engineer - DevOps',
            'Sr. Software Engineer - QA',
            'Sr. Technical Lead',
            'Sr. Technical Project Manager',
            'Sr. UI/UX Designer',
            'Technical Lead',
            'Sr. Web Analytics Engineer',
            'Technical Lead - DevOps',
            'Technical Project Manager',
            'UI/UX Designer',
            'Web Research Executive',
            'Service Delivery Manager',
            'Jr. Consultant',
            'Sr. Consultant',
        ];

        console.log("Adding designations to designations collection...");

        const designationDocs = DESIGNATION.map((name) => ({ name }));
        await designationsCollection.insertMany(designationDocs);

        console.log("Designations added to designations collection.");
    },

    // Rollback the migration
    async down(db) {
        const designationsCollection = db.collection("designations");

        console.log("Removing all designations from designations collection...");

        await designationsCollection.deleteMany({});

        console.log("All designations removed from designations collection.");
    },
};
