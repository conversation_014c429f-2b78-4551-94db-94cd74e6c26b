const router = require('express').Router();

const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const RagController = require('../services/getRag/ragController');

router.get('/', AuthMiddleWare, ACLMiddleWare, RagController.getRagReport);
router.get('/sprint-metrics', AuthMiddleWare, ACLMiddleWare, RagController.getSprintMetrics);
router.patch('/sprint-metrics', AuthMiddleWare, ACLMiddleWare, RagController.changeSprintMetrics);
router.get('/download-report', AuthMiddleWare, ACLMiddleWare, RagController.downloadRagReport);
router.get('/trigger-cron', AuthMiddle<PERSON>are, ACLMiddleWare, RagController.triggerCron);
router.patch('/', <PERSON>th<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>iddle<PERSON>are, RagController.changeRagDetails);
router.post('/freeze-report', Auth<PERSON>iddle<PERSON><PERSON>, ACLMiddleWare, RagController.freezeRagDetails);

module.exports = router;
