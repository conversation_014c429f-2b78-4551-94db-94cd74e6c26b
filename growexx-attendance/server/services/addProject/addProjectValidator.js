const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
/**
 * Class represents validations for Jira portal add.
 */
class AddProjectValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate add a user
     * <AUTHOR>
     * @since 06/04/2021
     */
    validationAddProject () {
        const { projectName, pmUser, startDate, endDate, businessUnitId } = this.body;
        super.projectName(projectName, 'Project name');
        super.projectReportingManger(pmUser, 'Reporting Manager');
        super.checkMongoId(businessUnitId, 'businessUnitId');
        if (endDate) {
            super.projectStartEndDate(startDate, endDate, 'Start date & End Date');
        } else {
            this.date(startDate);
        }
    }

    date (date) {

        const dateFormat = MOMENT(date, 'YYYY-MM-DD', true);
        if (!date) {
            throw new GeneralError(this.__(REQUIRED, 'Start date'), 400);
        }

        if (!dateFormat.isValid()) {
            throw new GeneralError(this.__(INVALID, 'Start date'), 400);
        }
    }


}
module.exports = AddProjectValidator;
