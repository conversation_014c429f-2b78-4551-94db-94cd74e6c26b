const PLIRating = require("../../models/pliRating.model");
const User = require("../../models/user.model");
const GeneralError = require("../../util/GeneralError");
const FreezePliRatingValidator = require("./freezePliRatingValidator");
const sendFrozenPliNotification = require("../notificationMentee/sendFrozenPliNotification");
const mongoose = require("mongoose");
const logger = require("../../util/logger");

/**
 * Class represents services for freezing PLI Rating
 */
class FreezePliRatingService {
  /**
   * @desc This function is being used to freeze a PLI rating
   * <AUTHOR>
   * @since 27/05/2025
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Updated PLI rating
   */
  static async freezePliRating(req, locale) {
    try {
      // const Validator = new FreezePliRatingValidator(req.body, locale);
      // await Validator.validateFreezePliRating();

      const {
        pliRatingId,
        mentorId,
        menteeId,
        superAdminOverride,
        month,
        year,
      } = req.body;

      // Log the request body for debugging
      logger.info(`FreezePliRating request: ${JSON.stringify(req.body)}`);

      let pliRating;

      // For Super Admin, prioritize finding by menteeId and month
      if (superAdminOverride && menteeId && month) {
        const currentYear = year || new Date().getFullYear();
        logger.info(
          `Super Admin override: Finding PLI by menteeId=${menteeId}, month=${month}, year=${currentYear}`
        );

        pliRating = await PLIRating.findOne({
          menteeId,
          month: parseInt(month, 10),
          year: parseInt(currentYear, 10),
        });

        if (pliRating) {
          logger.info(
            `Found PLI rating by menteeId and month: ${pliRating._id}`
          );
        }
      }

      // If not found by menteeId and month or not Super Admin, try by pliRatingId
      if (!pliRating && pliRatingId) {
        pliRating = await PLIRating.findById(pliRatingId);
        if (pliRating) {
          logger.info(`Found PLI rating by ID: ${pliRating._id}`);
        }
      }

      // If still not found, throw error
      if (!pliRating) {
        throw new GeneralError(locale("PLI_RATING_NOT_FOUND"));
      }

      // Verify that the mentor is authorized to freeze this PLI rating
      // Skip this check if superAdminOverride is true
      if (!superAdminOverride && pliRating.mentorId.toString() !== mentorId) {
        throw new GeneralError(locale("UNAUTHORIZED_MENTOR"));
      }

      // Check if PLI is already frozen and not a Super Admin override
      if (pliRating.isFrozen && !superAdminOverride) {
        throw new GeneralError(locale("PLI_ALREADY_FROZEN"));
      }

      // Freeze the PLI rating
      pliRating.isFrozen = true;
      pliRating.status = superAdminOverride ? "Finalized" : "Submitted";
      pliRating.updatedAt = Date.now();

      // Set superAdminOverride flag if applicable
      if (superAdminOverride) {
        pliRating.superAdminOverride = true;
      }

      // Save the updated PLI rating
      await pliRating.save();

      // Get mentee details for notification
      const mentee = await User.findById(pliRating.menteeId);
      const mentor = await User.findById(mentorId);

      if (mentee && mentor) {
        // Prepare payload for notification
        const notificationPayload = {
          menteeName: `${mentee.firstName} ${mentee.lastName}`,
          mentorName: `${mentor.firstName} ${mentor.lastName}`,
          month: pliRating.month,
          year: pliRating.year,
          pliRatingId: pliRating._id.toString(),
        };

        // Send notification to mentee
        try {
          await sendFrozenPliNotification(
            pliRating.menteeId,
            notificationPayload
          );
        } catch (emailError) {
          logger.error(
            `Failed to send frozen PLI notification: ${emailError.message}`
          );
          // Don't fail the entire operation if email fails
        }
      }

      return {
        status: "success",
        data: pliRating,
      };
    } catch (error) {
      logger.error(`Error in freezePliRating service: ${error.message}`);
      throw error;
    }
  }

  /**
   * @desc This function is being used to accept a frozen PLI rating by mentee
   * <AUTHOR>
   * @since 27/05/2025
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Updated PLI rating
   */
  static async acceptPliRating(req, locale) {
    try {
      // const Validator = new FreezePliRatingValidator(req.body, locale);
      // await Validator.validateAcceptPliRating();

      const { pliRatingId, menteeId } = req.body;

      // Check if PLI rating exists
      const pliRating = await PLIRating.findById(pliRatingId);
      if (!pliRating) {
        throw new GeneralError(locale("PLI_RATING_NOT_FOUND"));
      }

      // Verify that the mentee is authorized to accept this PLI rating
      if (pliRating.menteeId.toString() !== menteeId) {
        throw new GeneralError(locale("UNAUTHORIZED_MENTEE"));
      }

      // Check if PLI is frozen
      if (!pliRating.isFrozen) {
        throw new GeneralError(locale("PLI_NOT_FROZEN"));
      }

      // Check if PLI is already accepted
      if (pliRating.status === "Accepted") {
        throw new GeneralError(locale("PLI_ALREADY_ACCEPTED"));
      }

      // Accept the PLI rating
      pliRating.status = "Accepted";
      pliRating.updatedAt = Date.now();

      // Save the updated PLI rating
      await pliRating.save();

      return {
        status: "success",
        data: pliRating,
      };
    } catch (error) {
      logger.error(`Error in acceptPliRating service: ${error.message}`);
      throw error;
    }
  }

  // /**
  //  * Helper method to check if all PLI parameters are filled
  //  * @param {Object} pliRating PLI rating object
  //  * @returns {boolean} True if all parameters are filled, false otherwise
  //  */
  // static areAllParametersFilled (pliRating) {
  //     // Check if projectRatings exist and have scores
  //     if (!pliRating.projectRatings || pliRating.projectRatings.length === 0) {
  //         return false;
  //     }

  //     // Check if all project ratings have scores
  //     for (const projectRating of pliRating.projectRatings) {
  //         if (projectRating.score === undefined || projectRating.score === null) {
  //             return false;
  //         }
  //     }

  //     // All parameters are filled
  //     return true;
  // }
}

module.exports = FreezePliRatingService;
