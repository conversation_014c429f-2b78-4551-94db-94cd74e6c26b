module.exports = {
    async up (db) {
        const team = [];
        const projects = await db.collection('projects').find().toArray();
        for (const d of projects) {
            for (const user of d.users) {
                const isExist = await db.collection('projectteams').find({ userId: user.empId, projectId: d._id, isDev: 1 }).count();
                if (!isExist) {
                    team.push({
                        userId: user.empId,
                        projectId: d._id,
                        isDev: 1,
                        startDate: user.startDate,
                        endDate: user.endDate
                    });
                }
            }
        }
        if (team.length) {
            await db.collection('projectteams').insertMany(team);
        }
    }
};
