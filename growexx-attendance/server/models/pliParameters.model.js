/**
* @name PLI Parameters New model
* <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    roleParameters: [{
        applicableRole: {
            type: String,
            required: true,
            trim: true
        },
        parameters: [{
            parentParameter: {
                type: String,
                required: true,
                trim: true
            },
            parentWeightage: {
                type: Number,
                required: true,
                min: 0
            },
            projectType: {
                type: String,
                enum: ['Dedicated', 'Fixed'],
                required: true
            },
            childParameters: [{
                name: {
                    type: String,
                    required: true,
                    trim: true
                },
                weightage: {
                    type: Number,
                    required: true,
                    min: 0
                },
                description: {
                    type: String,
                    trim: true
                }
            }]
        }]
    }],
    isActive: {
        type: Number,
        default: 1,
        enum: [0, 1]
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('pliParameters', schema);
