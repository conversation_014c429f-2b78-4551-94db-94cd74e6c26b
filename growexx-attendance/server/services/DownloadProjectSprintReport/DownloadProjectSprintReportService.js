const SprintReportValidator = require('../sprintReport/SprintReportValidator');
const SprintReportService = require('../sprintReport/SprintReportService');
const Sprint = require('../../models/sprint.model');
const ProjectTrackerService = require('../getProjectTracker/ProjectTrackerService');
const Epic = require('../../models/epic.model');

/**
 * Class represents services for Get Sprint report.
 */
class DownloadProjectSprintReportService {

    /**
     * @desc This function is being used to Get the Project and Sprint report for download.
     * <AUTHOR>
     * @since 28/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async downlodProjectSprintReport (req, locale) {
        const boardId = req.query.boardId;
        const Validator = new SprintReportValidator(req.query, locale);
        Validator.validate(boardId);

        const sprintReportAggregation = SprintReportService.getSprintAgrgateParams({ boardId });
        const sprintReport = await Sprint.aggregate(sprintReportAggregation);

        const projectTrackerAggregation = ProjectTrackerService.getProjectTrackerAggregate({ boardId });
        const projectTracker = await Epic.aggregate(projectTrackerAggregation);

        return {
            sprintReport,
            projectTracker
        };
    }
}


module.exports = DownloadProjectSprintReportService;
