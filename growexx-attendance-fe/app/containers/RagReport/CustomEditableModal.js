import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Input, Modal } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheck,
  faTimes /* faEdit */,
} from '@fortawesome/free-solid-svg-icons';
import request from 'utils/request';
import { API_ENDPOINTS } from '../constants';

function CustomEditableModal({
  data,
  title,
  isVisible,
  id,
  param,
  changeRowData,
  onClose,
}) {
  const [inputValue, setInputValue] = useState(data);

  const handleOnChange = e => {
    setInputValue(e.target.value);
  };

  const onSave = () => {
    request(API_ENDPOINTS.RAG_REPORT, {
      method: 'PATCH',
      body: {
        field: param,
        value: inputValue,
        id,
      },
    });
    changeRowData(id, param, inputValue);
    onClose();
  };

  const decideInputTypeFromParam = () => (
    <Input.TextArea
      value={inputValue}
      onChange={handleOnChange}
      className="input-area"
      data-testid="input-area"
      maxLength={500}
      showCount
    />
  );

  useEffect(() => {
    if (inputValue !== data) {
      setInputValue(data);
    }
  }, [data]);

  return (
    <Modal
      forceRender
      title={title}
      visible={isVisible}
      onCancel={onClose}
      footer={null}
      width="50vw"
    >
      {decideInputTypeFromParam()}
      <div>
        <span>
          <Button type="link" className="pad-left-0" data-testid="cancel-btn">
            <FontAwesomeIcon icon={faTimes} onClick={onClose} />
          </Button>
          <Button type="link" data-testid="save-btn" onClick={onSave}>
            <FontAwesomeIcon icon={faCheck} />
          </Button>
        </span>
      </div>
    </Modal>
  );
}

CustomEditableModal.propTypes = {
  data: PropTypes.any,
  title: PropTypes.string,
  isVisible: PropTypes.bool,
  // isEdit: PropTypes.bool,
  id: PropTypes.string,
  param: PropTypes.oneOf([
    '',
    'processAudit',
    'techAudit',
    'comments',
    'mitigationPlan',
    'risksIdentified',
    'clientEscalations',
  ]),
  onClose: PropTypes.func,
  changeRowData: PropTypes.func,
};

export default CustomEditableModal;
