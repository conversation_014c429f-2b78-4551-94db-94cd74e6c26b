const { expect } = require('chai');
const sinon = require('sinon');
const axios = require('axios');
const Portal = require('../../server/models/portal.model');
const Story = require('../../server/models/story.model');
const Sprint = require('../../server/models/sprint.model');
const Epic = require('../../server/models/epic.model');
const ProjectBoard = require('../../server/models/projectBoard.model');
const Project = require('../../server/models/project.model');
const fetchDailyProjectTracker = require('../getDailyProjectTracker');

describe('Jira Data Fetching', () => {
    it('should fetch Jira data successfully', async () => {
        const mockResponseBoardList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'total': 1,
                'isLast': true,
                'values': [
                    {
                        'id': 85,
                        'name': 'ABC board',
                        'type': 'scrum'
                    }
                ]
            }
        };

        const mockProjects = [
            {
                _id: '6560393833638f422b0c4205',
                jiraProjectId: 'jira_project_1',
                projectName: 'Project 1',
                portalId: '6560393833638f422b0c4204',
                createdAt: '11-10-2023'
            }
        ];

        const mockPortal = {
            _id: '6560393833638f422b0c4204',
            url: 'test.atlassian.net',
            name: 'Test Jira',
            email: '<EMAIL>',
            token: 'test-token'
        };

        const mockResponseEpicList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'isLast': true,
                'values': [
                    {
                        'id': 47435,
                        'key': 'VAAL-69',
                        'name': 'Parent',
                        'summary': 'Parent'
                    }
                ]
            }
        };

        const mockResponseSpecificEpicList = {
            data: {
                'id': '12323',
                'key': 'PAR-427',
                'fields': {
                    'customfield_10020': [],
                    'status': {
                        'self': 'https://growexx.atlassian.net/rest/api/2/status/10001',
                        'description': '',
                        'iconUrl': 'https://growexx.atlassian.net/',
                        'name': 'Done',
                        'id': '10001',
                        'statusCategory': {
                            'self': 'https://growexx.atlassian.net/rest/api/2/statuscategory/3',
                            'id': 3,
                            'key': 'done',
                            'colorName': 'green',
                            'name': 'Done'
                        }
                    },
                    'project': {
                        'key': 'PAR'
                    },
                    'description': null,
                    'timetracking': {
                        'originalEstimate': '1w 1d',
                        'remainingEstimate': '1w 1d',
                        'originalEstimateSeconds': 172800,
                        'remainingEstimateSeconds': 172800
                    },
                    'summary': 'Developer'
                }
            }
        };

        const mockResponseStoryList = {
            data: {
                'startAt': 1,
                'maxResults': 50,
                'total': 50,
                'issues': [
                    {
                        'id': '12323',
                        'key': 'PAR-427',
                        'fields': {
                            'timespent': null,
                            'customfield_10020': null,
                            'aggregatetimespent': 172800,
                            'status': {
                                'name': 'Done'
                            },
                            'issuetype': {
                                'name': 'Story'
                            },
                            'project': {
                                'key': 'PAR'
                            },
                            'description': 'abc',
                            'timetracking': {
                                'originalEstimate': '1w 1d',
                                'remainingEstimate': '1w 1d',
                                'originalEstimateSeconds': 172800,
                                'remainingEstimateSeconds': 172800
                            },
                            'summary': 'TPM',
                            'duedate': null
                        }
                    },
                    {
                        'id': '12323',
                        'key': 'PAR-427',
                        'fields': {
                            'timespent': null,
                            'customfield_10020': [{
                                id: '123',
                                name: 'Sprint 1',
                                state: 'active',
                                goal: 'Test goal'
                            }],
                            'aggregatetimespent': 172800,
                            'status': {
                                'name': 'Done'
                            },
                            'issuetype': {
                                'name': 'Story'
                            },
                            'project': {
                                'key': 'PAR'
                            },
                            'description': 'abc',
                            'timetracking': {
                                'originalEstimate': '1w 1d',
                                'remainingEstimate': '1w 1d',
                                'remainingEstimateSeconds': 172800
                            },
                            'summary': 'Devops',
                            'duedate': '2023-11-10',
                            'customfield_10015': '2023-10-10'
                        }
                    },
                    {
                        'id': '12323',
                        'key': 'PAR-427',
                        'fields': {
                            'timespent': null,
                            'customfield_10020': [{
                                id: '123',
                                name: 'Sprint 1',
                                startDate: '2022-10-10',
                                endDate: '2022-10-20',
                                state: 'active',
                                goal: 'Test goal'
                            }],
                            'aggregatetimespent': 172800,
                            'status': {
                                'name': 'Done'
                            },
                            'issuetype': {
                                'name': 'Story'
                            },
                            'project': {
                                'key': 'PAR'
                            },
                            'description': 'abc',
                            'timetracking': {
                                'originalEstimate': '1w 1d',
                                'remainingEstimate': '1w 1d',
                                'remainingEstimateSeconds': 172800
                            },
                            'summary': 'scrum rituals',
                            'duedate': '2023-11-10',
                            'customfield_10015': '2023-10-10'
                        }
                    },
                    {
                        'id': '12323',
                        'key': 'PAR-427',
                        'fields': {
                            'timespent': null,
                            'customfield_10020': [{
                                id: '123',
                                name: 'Sprint 1',
                                startDate: '2022-10-10',
                                endDate: '2022-10-20',
                                state: 'active',
                                goal: 'Test goal'
                            }],
                            'aggregatetimespent': 172800,
                            'status': {
                                'name': 'Done'
                            },
                            'issuetype': {
                                'name': 'Story'
                            },
                            'project': {
                                'key': 'PAR'
                            },
                            'description': 'abc',
                            'timetracking': {
                                'originalEstimate': '1w 1d',
                                'remainingEstimate': '1w 1d',
                                'remainingEstimateSeconds': 172800
                            },
                            'summary': 'PO',
                            'duedate': '2023-11-10',
                            'customfield_10015': '2023-10-10'
                        }
                    }
                ]
            }
        };

        const mockBoardData = [
            {
                _id: '658cf0bb1414a2e83189f5e6',
                jiraBoardId: '29',
                projectId: '6560393833638f422b0c4205',
                jiraBoardName: 'CWM board',
                portalId: '6560393833638f422b0c4204'
            }
        ];

        const projectFindStub = sinon.stub(Project, 'find');
        projectFindStub.returns({
            sort: sinon.stub().resolves(mockProjects)
        });
        const portalFindOneStub = sinon.stub(Portal, 'findOne');
        portalFindOneStub.resolves(mockPortal);
        const projectBoardStub = sinon.stub(ProjectBoard, 'findOneAndUpdate');
        projectBoardStub.resolves({ _id: '6560393833638f422b0c4204' });
        const epicFindStub = sinon.stub(Epic, 'findOneAndUpdate');
        epicFindStub.resolves({ _id: '6560393833638f422b0c4204' });
        const storyStub = sinon.stub(Story, 'findOneAndUpdate');
        storyStub.resolves();
        const sprintStub = sinon.stub(Sprint, 'findOneAndUpdate');
        sprintStub.resolves({ _id: '6560393833638f422b0c4204' });

        const jiraStub = sinon.stub(axios, 'get');
        jiraStub.onCall(0).resolves(mockResponseBoardList);
        jiraStub.onCall(1).resolves(mockResponseEpicList);
        jiraStub.onCall(2).resolves(mockResponseSpecificEpicList);
        jiraStub.onCall(3).resolves(mockResponseStoryList);

        const projectBoardFindStub = sinon.stub(ProjectBoard, 'find');
        projectBoardFindStub.resolves(mockBoardData);

        const storyDeleteManyStub = sinon.stub(Story, 'deleteMany');
        storyDeleteManyStub.resolves({ acknowledged: true, deletedCount: 1 });
        const epicDeleteManyStub = sinon.stub(Epic, 'deleteMany');
        epicDeleteManyStub.resolves({ acknowledged: true, deletedCount: 1 });
        const sprintDeleteManyStub = sinon.stub(Sprint, 'deleteMany');
        sprintDeleteManyStub.resolves({ acknowledged: true, deletedCount: 1 });
        const projectBoardDeleteManyStub = sinon.stub(ProjectBoard, 'deleteMany');
        projectBoardDeleteManyStub.resolves({ acknowledged: true, deletedCount: 1 });

        await fetchDailyProjectTracker();

        expect(jiraStub.callCount).to.be.equals(4);
        expect(projectFindStub.calledOnce).to.be.true;
        expect(portalFindOneStub.calledOnce).to.be.true;
        expect(projectBoardStub.calledOnce).to.be.true;
        expect(epicFindStub.calledOnce).to.be.true;
        expect(storyStub.callCount).to.be.equals(4);
        expect(sprintStub.callCount).to.be.equals(3);
        jiraStub.restore();
        projectFindStub.restore();
        portalFindOneStub.restore();
        projectBoardStub.restore();
        epicFindStub.restore();
        storyStub.restore();
        sprintStub.restore();
        storyDeleteManyStub.restore();
        epicDeleteManyStub.restore();
        sprintDeleteManyStub.restore();
        projectBoardDeleteManyStub.restore();
        projectBoardFindStub.restore();
    });

    it('Failure in fetching board data', async () => {

        const mockProjects = [
            {
                _id: '6560393833638f422b0c4205',
                jiraProjectId: 'jira_project_1',
                projectName: 'Project 1',
                portalId: '6560393833638f422b0c4204',
                createdAt: '11-10-2023'
            }
        ];

        const mockPortal = {
            _id: '6560393833638f422b0c4204',
            url: 'test.atlassian.net',
            name: 'Test Jira',
            email: '<EMAIL>',
            token: 'test-token'
        };

        const projectFindStub = sinon.stub(Project, 'find');
        projectFindStub.returns({
            sort: sinon.stub().resolves(mockProjects)
        });
        const portalFindOneStub = sinon.stub(Portal, 'findOne');
        portalFindOneStub.resolves(mockPortal);
        const projectBoardStub = sinon.stub(ProjectBoard, 'findOneAndUpdate');
        projectBoardStub.resolves({ _id: '6560393833638f422b0c4204' });
        const epicFindStub = sinon.stub(Epic, 'findOneAndUpdate');
        epicFindStub.resolves({ _id: '6560393833638f422b0c4204' });
        const storyStub = sinon.stub(Story, 'findOneAndUpdate');
        storyStub.resolves({ _id: '6560393833638f422b0c4204' });
        const sprintStub = sinon.stub(Sprint, 'findOneAndUpdate');
        sprintStub.resolves({ _id: '6560393833638f422b0c4204' });

        const jiraStub = sinon.stub(axios, 'get');
        jiraStub.onCall(0).rejects(new Error('Board not found'));

        await fetchDailyProjectTracker();

        expect(jiraStub.callCount).to.be.equals(1);
        jiraStub.restore();
        projectFindStub.restore();
        portalFindOneStub.restore();
        projectBoardStub.restore();
        epicFindStub.restore();
        storyStub.restore();
        sprintStub.restore();
    });

    it('Failure in fetching epic list data', async () => {
        const mockResponseBoardList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'total': 1,
                'isLast': true,
                'values': [
                    {
                        'id': 85,
                        'name': 'ABC board',
                        'type': 'scrum'
                    }
                ]
            }
        };

        const mockProjects = [
            {
                _id: '6560393833638f422b0c4205',
                jiraProjectId: 'jira_project_1',
                projectName: 'Project 1',
                portalId: '6560393833638f422b0c4204',
                createdAt: '11-10-2023'
            }
        ];

        const mockPortal = {
            _id: '6560393833638f422b0c4204',
            url: 'test.atlassian.net',
            name: 'Test Jira',
            email: '<EMAIL>',
            token: 'test-token'
        };

        const projectFindStub = sinon.stub(Project, 'find');
        projectFindStub.returns({
            sort: sinon.stub().resolves(mockProjects)
        });
        const portalFindOneStub = sinon.stub(Portal, 'findOne');
        portalFindOneStub.resolves(mockPortal);
        const projectBoardStub = sinon.stub(ProjectBoard, 'findOneAndUpdate');
        projectBoardStub.resolves({ _id: '6560393833638f422b0c4204' });
        const epicFindStub = sinon.stub(Epic, 'findOneAndUpdate');
        epicFindStub.resolves({ _id: '6560393833638f422b0c4204' });
        const storyStub = sinon.stub(Story, 'findOneAndUpdate');
        storyStub.resolves({ _id: '6560393833638f422b0c4204' });
        const sprintStub = sinon.stub(Sprint, 'findOneAndUpdate');
        sprintStub.resolves({ _id: '6560393833638f422b0c4204' });

        const jiraStub = sinon.stub(axios, 'get');
        jiraStub.onCall(0).resolves(mockResponseBoardList);
        jiraStub.onCall(1).rejects(new Error('Epic not found'));

        await fetchDailyProjectTracker();

        expect(jiraStub.callCount).to.be.equals(2);
        jiraStub.restore();
        projectFindStub.restore();
        portalFindOneStub.restore();
        projectBoardStub.restore();
        epicFindStub.restore();
        storyStub.restore();
        sprintStub.restore();
    });

    it('Failure in fetching epic data', async () => {
        const mockResponseBoardList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'total': 1,
                'isLast': true,
                'values': [
                    {
                        'id': 85,
                        'name': 'ABC board',
                        'type': 'scrum'
                    }
                ]
            }
        };

        const mockProjects = [
            {
                _id: '6560393833638f422b0c4205',
                jiraProjectId: 'jira_project_1',
                projectName: 'Project 1',
                portalId: '6560393833638f422b0c4204',
                createdAt: '11-10-2023'
            }
        ];

        const mockPortal = {
            _id: '6560393833638f422b0c4204',
            url: 'test.atlassian.net',
            name: 'Test Jira',
            email: '<EMAIL>',
            token: 'test-token'
        };

        const mockResponseEpicList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'isLast': true,
                'values': [
                    {
                        'id': 47435,
                        'key': 'VAAL-69',
                        'name': 'Parent',
                        'summary': 'Parent'
                    }
                ]
            }
        };

        const projectFindStub = sinon.stub(Project, 'find');
        projectFindStub.returns({
            sort: sinon.stub().resolves(mockProjects)
        });
        const portalFindOneStub = sinon.stub(Portal, 'findOne');
        portalFindOneStub.resolves(mockPortal);
        const projectBoardStub = sinon.stub(ProjectBoard, 'findOneAndUpdate');
        projectBoardStub.resolves({ _id: '6560393833638f422b0c4204' });
        const epicFindStub = sinon.stub(Epic, 'findOneAndUpdate');
        epicFindStub.resolves({ _id: '6560393833638f422b0c4204' });
        const storyStub = sinon.stub(Story, 'findOneAndUpdate');
        storyStub.resolves({ _id: '6560393833638f422b0c4204' });
        const sprintStub = sinon.stub(Sprint, 'findOneAndUpdate');
        sprintStub.resolves({ _id: '6560393833638f422b0c4204' });

        const jiraStub = sinon.stub(axios, 'get');
        jiraStub.onCall(0).resolves(mockResponseBoardList);
        jiraStub.onCall(1).resolves(mockResponseEpicList);
        jiraStub.onCall(2).rejects(new Error('Epic not found'));

        await fetchDailyProjectTracker();

        expect(jiraStub.callCount).to.be.equals(3);
        jiraStub.restore();
        projectFindStub.restore();
        portalFindOneStub.restore();
        projectBoardStub.restore();
        epicFindStub.restore();
        storyStub.restore();
        sprintStub.restore();
    });

    it('Failure in fetching epic data', async () => {
        const mockResponseBoardList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'total': 1,
                'isLast': true,
                'values': [
                    {
                        'id': 85,
                        'name': 'ABC board',
                        'type': 'scrum'
                    }
                ]
            }
        };

        const mockProjects = [
            {
                _id: '6560393833638f422b0c4205',
                jiraProjectId: 'jira_project_1',
                projectName: 'Project 1',
                portalId: '6560393833638f422b0c4204',
                createdAt: '11-10-2023'
            }
        ];

        const mockPortal = {
            _id: '6560393833638f422b0c4204',
            url: 'test.atlassian.net',
            name: 'Test Jira',
            email: '<EMAIL>',
            token: 'test-token'
        };

        const mockResponseEpicList = {
            data: {
                'maxResults': 50,
                'startAt': 0,
                'isLast': true,
                'values': [
                    {
                        'id': 47435,
                        'key': 'VAAL-69',
                        'name': 'Parent',
                        'summary': 'Parent'
                    }
                ]
            }
        };

        const mockResponseSpecificEpicList = {
            data: {
                'id': '12323',
                'key': 'PAR-427',
                'fields': {
                    'customfield_10020': [],
                    'status': {
                        'self': 'https://growexx.atlassian.net/rest/api/2/status/10001',
                        'description': '',
                        'iconUrl': 'https://growexx.atlassian.net/',
                        'name': 'Done',
                        'id': '10001',
                        'statusCategory': {
                            'self': 'https://growexx.atlassian.net/rest/api/2/statuscategory/3',
                            'id': 3,
                            'key': 'done',
                            'colorName': 'green',
                            'name': 'Done'
                        }
                    },
                    'project': {
                        'key': 'PAR'
                    },
                    'description': null,
                    'timetracking': {
                        'originalEstimate': '1w 1d',
                        'remainingEstimate': '1w 1d',
                        'originalEstimateSeconds': 172800,
                        'remainingEstimateSeconds': 172800
                    },
                    'summary': 'PO'
                }
            }
        };

        const projectFindStub = sinon.stub(Project, 'find');
        projectFindStub.returns({
            sort: sinon.stub().resolves(mockProjects)
        });
        const portalFindOneStub = sinon.stub(Portal, 'findOne');
        portalFindOneStub.resolves(mockPortal);
        const projectBoardStub = sinon.stub(ProjectBoard, 'findOneAndUpdate');
        projectBoardStub.resolves({ _id: '6560393833638f422b0c4204' });
        const epicFindStub = sinon.stub(Epic, 'findOneAndUpdate');
        epicFindStub.resolves({ _id: '6560393833638f422b0c4204' });
        const storyStub = sinon.stub(Story, 'findOneAndUpdate');
        storyStub.resolves({ _id: '6560393833638f422b0c4204' });
        const sprintStub = sinon.stub(Sprint, 'findOneAndUpdate');
        sprintStub.resolves({ _id: '6560393833638f422b0c4204' });

        const jiraStub = sinon.stub(axios, 'get');
        jiraStub.onCall(0).resolves(mockResponseBoardList);
        jiraStub.onCall(1).resolves(mockResponseEpicList);
        jiraStub.onCall(2).resolves(mockResponseSpecificEpicList);
        jiraStub.onCall(3).rejects(new Error('Story not found'));

        await fetchDailyProjectTracker();

        expect(jiraStub.callCount).to.be.equals(4);
        jiraStub.restore();
        projectFindStub.restore();
        portalFindOneStub.restore();
        projectBoardStub.restore();
        epicFindStub.restore();
        storyStub.restore();
        sprintStub.restore();
    });
});

