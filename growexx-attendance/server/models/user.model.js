/**
 * @name user model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const mongoose = require('mongoose');
const { BUSINESS_UNIT } = require('../util/constants');

const schema = new appMongoose.Schema({
    employeeId: {
        type: Number
    },
    email: {
        type: String,
        unique: true
    },
    password: {
        type: String
    },
    firstName: {
        type: String,
        min: 2,
        max: 30
    },
    lastName: {
        type: String,
        min: 2,
        max: 30
    },
    countryCode: {
        type: String
    },
    phoneNumber: {
        type: String
    },
    isActive: {
        type: Number,
        default: 1,
        // 0 = deactive, 1 = active, 2 = suspended
        enum: [0, 1, 2]
    },
    otp: {
        type: Number,
        default: 0
    },
    phoneOtp: {
        type: Number,
        default: 0
    },
    doj: {
        type: Date
    },
    profilePicture: {
        type: String
    },
    resetToken: {
        type: String
    },
    resetExpiryTime: {
        type: Date
    },
    isDelete: {
        type: Number,
        default: 0,
        enum: [0, 1]
    },
    role: {
        type: Number,
        // 1 = User, 4 = admin
        enum: [1, 2, 3, 4, 5]
    },
    level: {
        type: String
    },
    requestedCountryCode: {
        type: String
    },
    requestedPhoneNumber: {
        type: String
    },
    label: {
        type: [String]
    },
    githubUsernames: {
        type: [String]
    },
    designation: {
        type: String
    },
    businessUnit: {
        type: String,
        enum: BUSINESS_UNIT
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    mentorId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        default: null
    },
    accessRights: {
        pli: {
            type: Boolean,
            default: false
        },
        pliAdmin: {
            type: Boolean,
            default: false
        },
        pliSuperAdmin: {
            type: Boolean,
            default: false
        }
    }
});

schema.path('email').required(true, 'User email cannot be blank');
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
schema.statics.findEmployeeMap = async function (employeeIds) {
    const users = await this.find({
        employeeId: {
            $in: employeeIds
        }
    }).select('employeeId');
    return users.reduce((map, obj) => {
        map[obj.employeeId] = obj._id;
        return map;
    }, {});
};

schema.statics.findActiveEmployeeById = async function (employeeId) {
    return await this.findOne({
        $and: [
            { _id: mongoose.Types.ObjectId(employeeId) },
            { isActive: CONSTANTS.STATUS.ACTIVE }
        ]
    }, { _id: 1 });
};
module.exports = appMongoose.model('user', schema);
