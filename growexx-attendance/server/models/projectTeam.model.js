/**
 * @name projectTeam model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({

    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user'
    },
    projectId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'project'
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        default: null
    },
    isDev: {
        type: Number,
        default: 1,
        enum: [0, 1]
    }
}, {
    timestamps: true
});
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
schema.statics.addEmployeeIfNotEsists = async function ( projectId, userId, startDate, endDate, isDev ) {
    const checkEmployeeExists = await this.countDocuments({
        userId,
        projectId,
        endDate: { $eq: null }
    }).select('_id');
    if (!checkEmployeeExists) {
        const projectTeamData = {
            userId,
            projectId,
            startDate,
            endDate: endDate ? endDate : '',
            isDev: isDev ? 1 : 0
        };
        await this.create(projectTeamData);
    }
};
module.exports = appMongoose.model('projectTeam', schema);
