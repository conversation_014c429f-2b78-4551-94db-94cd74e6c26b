const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const admin = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestAdminPayload = {
    token: jwt.sign(admin, process.env.JWT_SECRET, tokenOptionalInfo)
};


describe('Download project tracker and sprint report', () => {
    try {
        it('As a TPM, I should able to download project tracker and sprint report without board Id', async () => {
            const res = await request(process.env.BASE_URL)
                .get('/project-tracker/download-report')
                .set({ Authorization: requestAdminPayload.token });
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 400);
        });

        it('As a TPM, I should able download project tracker and sprint report with board Id', async () => {
            const data = {
                boardId: '656060f26373241a63a8d764'
            };
            const res = await request(process.env.BASE_URL)
                .get('/project-tracker/download-report')
                .set({ Authorization: requestAdminPayload.token })
                .query(data);
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, 200);
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});
