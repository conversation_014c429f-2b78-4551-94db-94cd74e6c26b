/**
 * @name kra model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const { KRA_CATAGORY } = require('../util/constants');

const schema = new appMongoose.Schema({
    category: {
        type: String,
        required: true,
        enum: KRA_CATAGORY
    },
    outcome: {
        type: String,
        required: true,
        min: 1,
        max: 500
    },
    measure: {
        type: String,
        required: true,
        min: 1,
        max: 500
    },
    target: {
        type: String,
        required: true,
        min: 1,
        max: 500
    },
    designation: {
        type: String,
        required: true,
    },
    quarter: {
        type: String,
        required: true
    },
    year: {
        type: Number,
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('kra', schema);

