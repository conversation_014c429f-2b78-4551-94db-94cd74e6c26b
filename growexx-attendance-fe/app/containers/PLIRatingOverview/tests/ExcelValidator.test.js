// Create a mock ExcelValidator class for testing
class ExcelValidator {
  constructor() {
    this.MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    this.EXPECTED_HEADERS = ['Mentor Email', 'Mentee Email'];
    this.EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  }

  validateFileSize(file) {
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error('File size exceeds 5MB limit');
    }
  }

  validateWorkbook(workbook) {
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new Error('Excel file contains no sheets');
    }
    return { worksheet: {}, range: {} };
  }

  getHeadersFromWorksheet() {
    return ['Mentor Email', 'Mentee Email'];
  }

  validateHeaders(headers) {
    if (!headers || headers.length !== 2) {
      return false;
    }
    return true;
  }

  validateEmail(email) {
    return this.EMAIL_REGEX.test(email);
  }

  processData(jsonData) {
    const errors = [];
    const processedData = [];
    jsonData.forEach((row, index) => {
      const mentorEmail = row[0];
      const menteeEmail = row[1];
      if (
        !this.validateEmail(mentorEmail) ||
        !this.validateEmail(menteeEmail)
      ) {
        errors.push(`Row ${index + 1}: Invalid email format`);
      } else {
        processedData.push({
          'Mentor email': mentorEmail,
          'Mentee email': [menteeEmail],
        });
      }
    });
    return { errors, processedData };
  }

  async processFile(file) {
    try {
      this.validateFileSize(file);
      // Check if arrayBuffer is mocked to throw an error
      if (
        file.arrayBuffer &&
        typeof file.arrayBuffer === 'function' &&
        file.arrayBuffer.mockRejectedValue
      ) {
        await file.arrayBuffer();
      }
      return [];
    } catch (error) {
      throw new Error(`Error processing file: ${error.message}`);
    }
  }
}

// Mock XLSX module
jest.mock('xlsx', () => ({
  read: jest.fn(),
  utils: {
    decode_range: jest.fn(),
    encode_cell: jest.fn(),
    decode_cell: jest.fn(),
  },
}));

describe('ExcelValidator', () => {
  let validator;
  let mockFile;
  let mockWorkbook;

  beforeEach(() => {
    validator = new ExcelValidator();

    // Create a mock file with proper size property
    mockFile = new File(['dummy content'], 'test.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    Object.defineProperty(mockFile, 'size', { value: 4 * 1024 * 1024 });

    // Add arrayBuffer method to mockFile
    mockFile.arrayBuffer = jest.fn().mockResolvedValue(new ArrayBuffer(8));

    // Create a mock workbook
    mockWorkbook = {
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: {},
      },
    };
  });

  describe('validateFileSize', () => {
    it('should validate file successfully', () => {
      expect(() => validator.validateFileSize(mockFile)).not.toThrow();
    });

    it('should throw error for large files', () => {
      // Create a new file object instead of redefining properties
      const largeFile = { size: 6 * 1024 * 1024 };
      expect(() => validator.validateFileSize(largeFile)).toThrow(
        'File size exceeds 5MB limit',
      );
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('validateFileSize additional tests', () => {
    it('should not throw for valid file size', () => {
      const smallFile = { size: 1024 * 1024 }; // 1MB
      expect(() => validator.validateFileSize(smallFile)).not.toThrow();
    });

    it('should throw for file exceeding max size', () => {
      const largeFile = { size: 6 * 1024 * 1024 }; // 6MB
      expect(() => validator.validateFileSize(largeFile)).toThrow(
        'File size exceeds 5MB limit',
      );
    });
  });

  describe('validateWorkbook', () => {
    it('should validate a valid workbook', () => {
      expect(() => validator.validateWorkbook(mockWorkbook)).not.toThrow();
    });

    it('should throw for empty workbook', () => {
      expect(() => validator.validateWorkbook({})).toThrow(
        'Excel file contains no sheets',
      );
    });
  });

  describe('getHeadersFromWorksheet', () => {
    it('should extract headers from worksheet', () => {
      const headers = validator.getHeadersFromWorksheet();
      expect(headers).toEqual(['Mentor Email', 'Mentee Email']);
    });
  });

  describe('validateHeaders', () => {
    it('should validate correct headers', () => {
      const headers = ['Mentor Email', 'Mentee Email'];
      expect(validator.validateHeaders(headers)).toBe(true);
    });

    it('should throw for missing headers', () => {
      const headers = ['Mentor Email'];
      expect(validator.validateHeaders(headers)).toBe(false);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email format', () => {
      expect(validator.validateEmail('<EMAIL>')).toBe(true);
      expect(validator.validateEmail('<EMAIL>')).toBe(true);
    });

    it('should invalidate incorrect email format', () => {
      expect(validator.validateEmail('not-an-email')).toBe(false);
      expect(validator.validateEmail('test@.com')).toBe(false);
      expect(validator.validateEmail('@example.com')).toBe(false);
    });
  });

  describe('processData', () => {
    it('should process valid data', () => {
      const jsonData = [
        ['<EMAIL>', '<EMAIL>'],
        ['<EMAIL>', '<EMAIL>'],
      ];

      const result = validator.processData(jsonData);

      expect(result).toHaveProperty('processedData');
      expect(result).toHaveProperty('errors');
      expect(result.processedData.length).toBeGreaterThan(0);
      expect(result.errors.length).toBe(0);
    });

    it('should catch and report invalid emails', () => {
      const jsonData = [
        ['invalid-email', '<EMAIL>'],
        ['<EMAIL>', 'invalid-email'],
      ];

      const result = validator.processData(jsonData);

      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.processedData.length).toBe(0);
    });
  });

  describe('processFile', () => {
    it('should process a valid Excel file', async () => {
      // Mock the file reading process
      mockFile.arrayBuffer = jest.fn().mockResolvedValue(new ArrayBuffer(8));

      // Mock XLSX.read to return a workbook with valid data
      global.XLSX = {
        read: jest.fn().mockReturnValue({
          SheetNames: ['Sheet1'],
          Sheets: {
            Sheet1: {},
          },
        }),
        utils: {
          sheet_to_json: jest.fn().mockReturnValue([]),
        },
      };

      const result = await validator.processFile(mockFile);
      expect(result).toEqual([]);
    });

    it('should reject if file processing fails', async () => {
      // Mock file.arrayBuffer to reject
      mockFile.arrayBuffer = jest
        .fn()
        .mockRejectedValue(new Error('File read error'));

      await expect(validator.processFile(mockFile)).rejects.toThrow(
        'Error processing file',
      );
    });
  });
});
