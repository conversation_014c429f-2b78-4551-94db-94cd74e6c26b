/**
 * @name Portal model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    name: {
        type: String
    },
    url: {
        type: String,
        unique: true
    },
    email: {
        type: String
    },
    token: {
        type: String
    },
    isActive: {
        type: Number,
        default: 1,
        // 0 = deactive, 1 = active, 2 = Deleted
        enum: [0, 1, 2]
    },
    lastFetch: {
        type: Date,
        default: Date.now
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('portal', schema);
