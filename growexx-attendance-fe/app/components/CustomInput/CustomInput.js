import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Input, Select } from 'antd';
import request from 'utils/request';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { getNumbersOnly } from '../../utils/Helper';
import { API_ENDPOINTS } from '../../containers/constants';
import { DEFAULT_CLIENT_ESCALATIONS_OPTIONS } from '../../containers/RagReport/constants';
const { Option } = Select;
function CustomInput({
  data,
  id,
  param,
  changeRowData,
  isDisabled,
  member,
  renderFor = 'rag',
}) {
  const [inputValue, setInputValue] = useState(data);

  if (isDisabled && param !== 'memberClientEscalation') {
    if (!data) {
      return (
        <Button type="link" disabled={isDisabled}>
          <FontAwesomeIcon icon={faPlus} />
        </Button>
      );
    }
    return (
      <span
        style={{
          color: '#FF0000',
        }}
        data-testid="info-data"
      >
        {data}
      </span>
    );
  }

  const onSave = async value => {
    let payload = {
      field: param,
      value: inputValue !== undefined ? inputValue : data,
      id,
    };
    if (param === 'clientEscalations') {
      payload.value = value;
    }
    if (param === 'memberClientEscalation') {
      payload = { ...payload, value, member, memberId: id };
    }
    // ** conditional parameter if using this for sprint metrics table, previous default action is moved to else **
    if (renderFor !== 'sprintMetrics') {
      await request(API_ENDPOINTS.RAG_REPORT, {
        method: 'PATCH',
        body: payload,
      });
    } else {
      request(API_ENDPOINTS.SPRINT_METRICS_REPORT, {
        method: 'PATCH',
        body: payload,
      });
    }
    if (param === 'clientEscalations' || param === 'memberClientEscalation') {
      changeRowData(id, param, payload.value);
      setInputValue(payload.value);
    } else {
      changeRowData(id, param, payload.value);
      setInputValue(payload.value);
    }
  };

  const decideInputTypeFromParam = () => {
    if (param === 'clientEscalations' || param === 'memberClientEscalation') {
      return (
        <Select
          style={{ width: '100%' }}
          value={
            inputValue || (param === 'memberClientEscalation' ? 0 : inputValue)
          }
          onChange={newValue => {
            setInputValue(
              newValue || (param === 'memberClientEscalation' ? 0 : newValue),
            );
            onSave(
              newValue || (param === 'memberClientEscalation' ? 0 : newValue),
            );
          }}
          disabled={isDisabled}
          placeholder={
            param === 'clientEscalations'
              ? 'Select Client Escalations'
              : undefined
          }
          data-testid="client-escalations"
        >
          {Object.keys(DEFAULT_CLIENT_ESCALATIONS_OPTIONS).map(key => (
            <Option
              key={key}
              value={DEFAULT_CLIENT_ESCALATIONS_OPTIONS[key].value}
            >
              {DEFAULT_CLIENT_ESCALATIONS_OPTIONS[key].key}
            </Option>
          ))}
        </Select>
      );
    }

    if (param === 'deliveryHeadComments') {
      return (
        <Input
          value={inputValue}
          onChange={e => {
            setInputValue(e.target.value);
          }}
          onPressEnter={onSave}
          onBlur={onSave}
          disabled={isDisabled}
          placeholder="Enter Delivery Head Comments"
        />
      );
    }

    return (
      <Input
        value={inputValue}
        onChange={e => {
          getNumbersOnly({
            e,
            min: 0,
            max: 15,
            precision: 2,
          });
          setInputValue(e.target.value);
        }}
        onPressEnter={onSave}
        onBlur={onSave}
        disabled={isDisabled}
        placeholder="Enter Audit Score"
      />
    );
  };

  return decideInputTypeFromParam();
}

CustomInput.propTypes = {
  data: PropTypes.any,
  id: PropTypes.string,
  param: PropTypes.oneOf([
    '',
    'processAudit',
    'techAudit',
    'clientEscalations',
    'deliveryHeadComments',
    'memberClientEscalation',
  ]),
  changeRowData: PropTypes.func,
  isDisabled: PropTypes.bool,
};

export default CustomInput;
