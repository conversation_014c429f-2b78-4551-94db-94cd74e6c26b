const CategoryWeightage = require('../../models/categoryWeightage.model');
const Userkra = require('../../models/userKra.model');
const GeneralError = require('../../util/GeneralError');
const AddCategoryWeightageValidator = require('./addCategoryWeightageValidator');
/**
 * Class represents services for add category weightage.
 */
class AddCategoryWeightageService {

    /**
     * @desc This function is being used to add a category weightage
     * <AUTHOR>
     * @since 24/08/2022
     * @param {Object} req Request
     */
    static async addCategoryWeightage (req, user, locale) {
        const { categoryWeightages } = req.body;
        const Validator = new AddCategoryWeightageValidator(req.body, locale);
        Validator.validationAddCategoryWeightage();
        for (const index in categoryWeightages) {
            if (categoryWeightages[index]) {
                await CategoryWeightage.updateOne(
                    { designation: categoryWeightages[index].designation, category: categoryWeightages[index].category,
                        quarter: categoryWeightages[index].quarter, year: categoryWeightages[index].year, isWeightageAssignedToKra: 0 },
                    { weightage: categoryWeightages[index].weightage }, { upsert: true });
            }
        }
        return categoryWeightages;
    }

}

module.exports = AddCategoryWeightageService;
