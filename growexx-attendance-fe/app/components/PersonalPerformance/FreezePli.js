import React, { useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Button, notification, Spin } from 'antd';
import {
  Container,
  Label,
  Value,
  ButtonContainer,
  StyledButton,
} from './Styles/StyledFreezePli';
import request from '../../utils/request';
import { API_ENDPOINTS } from '../../containers/EmployeeProfile/constants';
import { EmployeeProfileContext } from '../../containers/EmployeeProfile/context';

const FreezePLI = ({
  ids,
  mentorId,
  menteeId,
  calculatedPLIScore,
  existingPliRatingId,
  isPLIFrozen,
  disabled,
  onPLISubmitted,
}) => {
  const { selectedMonth } = useContext(EmployeeProfileContext) || {};

  const [freezing, setFreezing] = useState(false);
  const [isSuperAdminFrozen, setIsSuperAdminFrozen] = useState(false);

  // Create a ref to track mount status
  const isMounted = React.useRef(true);

  useEffect(() => {
    const checkSuperAdminFreeze = async () => {
      if (existingPliRatingId) {
        try {
          const response = await request(
            `${
              API_ENDPOINTS.PLI_RATING_BY_ID
            }?pliRatingId=${existingPliRatingId}&superAdminOverride=${
              ids && ids.isSuperAdminUser ? 'true' : 'false'
            }`,
            {
              method: 'GET',
            },
          );

          if (response && response.status === 1 && response.data) {
            // Only update state if the component is still mounted
            if (isMounted.current) {
              setIsSuperAdminFrozen(
                response.data.superAdminOverride && response.data.isFrozen,
              );
            }
          }
        } catch (error) {
          notification.error({
            message: 'Error',
            description: 'Failed to check Super Admin freeze status.',
          });
        }
      }
    };

    checkSuperAdminFreeze();

    // Cleanup function to set isMounted to false when the component unmounts
    return () => {
      isMounted.current = false;
    };
  }, [existingPliRatingId, ids]);

  // Only show the "finalized by Super Admin" message if it's frozen by Super Admin AND current user is not a Super Admin
  if (isSuperAdminFrozen && !ids?.isSuperAdminUser) {
    return (
      <Container>
        <Row align="middle">
          <Col span={24}>
            <div style={{ textAlign: 'center', color: '#e74c3c' }}>
              <b>
                This PLI has been finalized by a Super Admin and cannot be
                modified further.
              </b>
            </div>
          </Col>
        </Row>
      </Container>
    );
  }

  // Only show the "already frozen" message if it's frozen AND current user is not a Super Admin
  if (isPLIFrozen && !ids?.isSuperAdminUser) {
    return (
      <Container>
        <Row align="middle">
          <Col span={24}>
            <div style={{ textAlign: 'center', color: '#1890ff' }}>
              <b>This PLI has already been frozen.</b>
            </div>
          </Col>
        </Row>
      </Container>
    );
  }

  const handleFreezePLI = async () => {
    try {
      setFreezing(true);

      // Ensure we have a valid pliRatingId - prioritize existingPliRatingId if available
      const pliRatingId = existingPliRatingId || (ids && ids.pliRatingId);

      const isSuperAdmin = ids && ids.isSuperAdminUser === true;
      const effectiveMenteeId = menteeId || (ids && ids.menteeId);

      // For regular users, we need pliRatingId
      // For Super Admin, we need at least menteeId
      if (!pliRatingId && !isSuperAdmin) {
        notification.error({
          message: 'Error',
          description: 'PLI Rating ID is required to finalize PLI score',
        });
        setFreezing(false);
        return;
      }

      if (!effectiveMenteeId) {
        notification.error({
          message: 'Error',
          description: 'Mentee ID is required to freeze PLI',
        });
        setFreezing(false);
        return;
      }

      const months = {
        january: 1,
        february: 2,
        march: 3,
        april: 4,
        may: 5,
        june: 6,
        july: 7,
        august: 8,
        september: 9,
        october: 10,
        november: 11,
        december: 12,
      };

      const month =
        selectedMonth && months[selectedMonth.toLowerCase()]
          ? months[selectedMonth.toLowerCase()]
          : new Date().getMonth() + 1;

      const year = new Date().getFullYear();

      // For Super Admin, we don't need to fetch pliRatingId first
      // The backend will handle finding by menteeId and month
      const formattedData = {
        mentorId:
          mentorId || (ids && ids.mentorId)
            ? String(mentorId || ids.mentorId)
            : '',
        menteeId: effectiveMenteeId ? String(effectiveMenteeId) : '',
        superAdminOverride: isSuperAdmin,
        month,
        year,
      };

      // Only include pliRatingId if it exists
      if (pliRatingId) {
        formattedData.pliRatingId = pliRatingId;
      }

      await completeFreezeOperation(formattedData);

      // Reset freezing state after successful operation
      setFreezing(false);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to freeze PLI score',
      });
      setFreezing(false);
    }
  };

  // Helper function to complete the freeze operation
  const completeFreezeOperation = async formattedData => {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (ids && ids.isSuperAdminUser) {
      headers['X-Super-Admin-Override'] = 'true';
    }

    const response = await request(API_ENDPOINTS.FREEZE_PLI, {
      method: 'POST',
      body: JSON.stringify(formattedData),
      headers,
    });

    if (response && response.status === 1) {
      notification.success({
        message: 'Success',
        description:
          ids && ids.isSuperAdminUser
            ? 'PLI Score finalized by Super Admin successfully'
            : 'PLI Score frozen successfully',
      });
      // Call the parent's callback function
      if (onPLISubmitted) {
        onPLISubmitted();
      }
    } else {
      throw new Error(
        response && response.message
          ? response.message
          : 'Failed to freeze PLI score',
      );
    }
  };

  // Helper function to determine button text based on user role
  const getButtonText = () => {
    if (ids && ids.isSuperAdminUser) {
      return 'Finalize PLI Score';
    }
    return 'Freeze PLI Score';
  };

  return (
    <Container>
      <Row align="middle">
        <Col span={6}>
          <Label>Calculated PLI Score</Label>
        </Col>
        <Col span={4}>
          <Value>{calculatedPLIScore || 'N/A'}</Value>
        </Col>
        <Col span={5}>
          <Button
            type="primary"
            style={{ marginLeft: '16px' }}
            onClick={handleFreezePLI}
            disabled={freezing || disabled}
            data-testid="freeze-pli-button"
          >
            {freezing ? <Spin size="small" /> : getButtonText()}
          </Button>
        </Col>
      </Row>

      <ButtonContainer>
        <StyledButton type="primary">Save as Draft</StyledButton>
        <StyledButton type="default">Cancel</StyledButton>
      </ButtonContainer>
    </Container>
  );
};

FreezePLI.propTypes = {
  ids: PropTypes.shape({
    menteeId: PropTypes.string,
    mentorId: PropTypes.string,
    parameterIds: PropTypes.object,
    pliRatingId: PropTypes.string,
    isSuperAdminUser: PropTypes.bool,
  }),
  mentorId: PropTypes.string,
  menteeId: PropTypes.string,
  calculatedPLIScore: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  existingPliRatingId: PropTypes.string,
  isPLIFrozen: PropTypes.bool,
  disabled: PropTypes.bool,
  onPLISubmitted: PropTypes.func,
};

FreezePLI.defaultProps = {
  ids: null,
  menteeId: null,
  mentorId: null,
  calculatedPLIScore: null,
  existingPliRatingId: null,
  isPLIFrozen: false,
  disabled: false,
  onPLISubmitted: null,
};

export default FreezePLI;
