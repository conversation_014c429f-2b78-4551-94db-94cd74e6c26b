/* eslint-disable max-len */
const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const AuthMiddleWareManager = require('../middleware/managerAuth');
const ACLMiddleWare = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const AddKraController = require('../services/addKra/addKraController');
const UploadKraController = require('../services/uploadKRA/uploadKRAController');
const ListKraController = require('../services/listKra/listKraController');
const AssignKraController = require('../services/assignKra/assignKraController');
const AssessKraController = require('../services/assessKra/assessKraController');
const PublishKraController = require('../services/publishKra/publishKraController');
const GetKraController = require('../services/getKra/getKraController');
const GetKraForManagerController = require('../services/getKraForManager/getKraForManagerController');
const RateKraController = require('../services/rateKra/rateKraController');
const GetUserKraController = require('../services/getUserKra/getUserKRAController');
const SelfKraForUserController = require('../services/selfKra/selfKraForUserController');
const RateSelfKraController = require('../services/rateSelfKra/rateSelfKraController');
const GetAssignedKraListForAdminController = require('../services/listAssignedKraForAdmin/listAssignedKraForAdminController');
const DownloadKraAttachmentController = require('../services/downloadKraAttachment/downloadKraAttachmentController');
const DeleteKraController = require('../services/deleteKra/deleteKraController');
const ListAssignViewKraController = require('../services/listAssignViewKra/listAssignViewKraContoller');
const ListKraGroupController = require('../services/listKraGroup/listKraGroupController');
const ReleaseRatingController = require('../services/releaseRatingKra/releaseRatingKraController');
const DownloadViewRatingController = require('../services/downloadViewRating/downloadViewRatingController');
const UnfreezeReviewerController = require('../services/unfreezeReviewer/unfreezeReviewerController');
const UploadKRAAttachmentController = require('../services/uploadKRAAttachment/uploadKRAAttachmentController');
const AddCategoryWeightageController = require('../services/addCategoryWeightage/addCategoryWeightageController');
const GetCategoryWeightageContoller = require('../services/getCategoryWeightage/getCategoryWeightageContoller');
const NotificationController = require('../services/notification/notificationController');
const FeedbackMeetingController = require('../services/feedbackMeeting/feedbackMeetingController');
const ListCategoryWeightageController = require('../services/listCategoryWeightage/listCategoryWeightageController');
const DownloadUserKraDataController = require('../services/downloadUserKraData/downloadUserKraDataController');
const StartFeedbackMeetingController = require('../services/startFeedbackMeeting/startFeedbackMeetingController');
const UpdateKraController = require('../services/updateKra/updateKraController');
const DownloadPendingKraAssignReportController = require('../services/downloadPendingKraAssignReport/downloadPendingKraAssignReportController');
const DownloadKraStatusReportController = require('../services/downloadKraStatusReport/downloadKraStatusReportController');

router.post('/import', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, UploadMiddleWare.single('file'), UploadKraController.uploadKRA);
router.post('/', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, AddKraController.addKra);
router.put('/', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, UpdateKraController.updateKra);
router.post('/assign', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, AssignKraController.assignKra);
router.post('/bulk-assign', AuthMiddleWare, ACLMiddleWare, AssignKraController.bulkAssign);
router.get('/all', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, ListKraController.listKras);
router.get('/list/group-by-designation', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, ListKraGroupController.listKraGroup);
router.put('/assessment', AuthMiddleWare, ACLMiddleWare, AssessKraController.startKRAAssessment);
router.patch('/assessment/feedback-meeting', AuthMiddleWare, ACLMiddleWare, StartFeedbackMeetingController.startFeedbackMeeting);
router.post('/publish', AuthMiddleWare, ACLMiddleWare, PublishKraController.publishKra);
router.get('/', AuthMiddleWare, ACLMiddleWare, GetKraController.getKra);
router.delete('/', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, DeleteKraController.deleteKra);
router.patch('/release-ratings', AuthMiddleWare, ACLMiddleWare, ReleaseRatingController.releaseRating);
router.post('/download-ratings', AuthMiddleWare, ACLMiddleWare, DownloadViewRatingController.downloadViewRating);

// router /kra/assesment
router.post('/upload', AuthMiddleWare, UploadMiddleWare.single('file'), UploadKRAAttachmentController.uploadAttachment);
router.get('/download/:userKraId/:kraId/:file', AuthMiddleWare, DownloadKraAttachmentController.downloadAttachment);
router.get('/assessment/manager', AuthMiddleWare, ACLMiddleWare, GetKraForManagerController.listKraForManager);
router.get('/assessment/user-kra', AuthMiddleWare, ACLMiddleWare, GetUserKraController.getUserKRA);
router.put('/rate/reporting/save', AuthMiddleWare, ACLMiddleWare, RateKraController.rateByReportingSave);
router.put('/rate/reviewer/save', AuthMiddleWare, ACLMiddleWare, RateKraController.rateByReviewerSave);
router.put('/rate/reporting/freeze', AuthMiddleWare, ACLMiddleWare, RateKraController.rateByReportingFreeze);
router.put('/bu-head-approved', AuthMiddleWare, ACLMiddleWare, RateKraController.buHeadApproved);
router.put('/rate/reviewer/freeze', AuthMiddleWare, ACLMiddleWare, RateKraController.rateByReviewerFreeze);
router.put('/rate/self/save', AuthMiddleWare, ACLMiddleWare, RateSelfKraController.rateSelfSave);
router.put('/rate/self/freeze', AuthMiddleWare, ACLMiddleWare, RateSelfKraController.rateSelfFreeze);
router.patch('/user-kra/freezed-by-review-manager', AuthMiddleWare, ACLMiddleWare, UploadMiddleWare.array('attachments'), UnfreezeReviewerController.unfreezeReviewer);
router.patch('/user-kra/feedback-meeting-date', AuthMiddleWare, ACLMiddleWare, FeedbackMeetingController.feedbackMeeting);
router.patch('/user-kra/feedback-meeting-done', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, FeedbackMeetingController.feedbackMeetingDone);

router.get('/self/list', AuthMiddleWare, ACLMiddleWare, SelfKraForUserController.listSelfKra);
router.get('/assigned-to-users/list', AuthMiddleWare, ACLMiddleWare, GetAssignedKraListForAdminController.listAssignedKraForAdmin);
router.get('/list/view/assign', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, ListAssignViewKraController.listAssignViewKra);
router.post('/category-weightage', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, AddCategoryWeightageController.addCategoryWeightage);
router.get('/category-weightage', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, GetCategoryWeightageContoller.getCategoryWeightage);
router.get('/notification', AuthMiddleWare, ACLMiddleWare, NotificationController.notification);
router.get('/list/category-weightage', AuthMiddleWare, AuthMiddleWareManager, ACLMiddleWare, ListCategoryWeightageController.listCategoryWeightage);
router.get('/download-kra-data', AuthMiddleWare, ACLMiddleWare, DownloadUserKraDataController.downloadUserKraData);
router.get('/download/pending-assign-kra', AuthMiddleWare, ACLMiddleWare, DownloadPendingKraAssignReportController.downloadPendingReport);
router.get('/download/kra-status', AuthMiddleWare, ACLMiddleWare, DownloadKraStatusReportController.downloadStatusReport);

module.exports = router;
