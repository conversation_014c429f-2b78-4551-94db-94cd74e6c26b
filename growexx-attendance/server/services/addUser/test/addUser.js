module.exports = {
    addUser: [{
        it: 'As a admin I should validate if employeeId is not pass',
        options: {
            fisrtName: 'Growexx',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if employeeId is pass as blank',
        options: {
            employeeId: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if employeeId is pass as invalid',
        options: {
            employeeId: 'A',
            url: 'A',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if email is not pass',
        options: {
            employeeId: 100,
            name: 'Growexx',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if email is pass as blank',
        options: {
            employeeId: 100,
            email: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if email is pass as invalid',
        options: {
            employeeId: 100,
            email: 'test',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if password is not pass',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if password is pass but empty',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if password is pass as invalid',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: 'test',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if first name is not pass',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if first name is pass but empty',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if last name is not pass',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if last name is pass but empty',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if label is not passed',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if label is passed as empty',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if label is passed as invalid array',
        options: {
            employeeId: 1,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: 'newemployee',
            dateOfJoining: '2021-09-04',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a Admin, I should not able to add already added employeeid',
        options: {
            employeeId: 1,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['newemployee'],
            dateOfJoining: '2021-09-04',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a Admin, I should not able to add already added email',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['newemployee'],
            dateOfJoining: '2021-09-04',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a Admin, I should not able to add already added label',
        options: {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['user'],
            dateOfJoining: '2021-09-04',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if date of joining is not pass',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new2.label'],
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if date of joining is pass as blank',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new2.label'],
            dateOfJoining: '',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is not pass',
        options: {
            firstName: 'Test',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if designation is pass as blank',
        options: {
            designation: '',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if date of joining is pass as invalid',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new2.label'],
            dateOfJoining: 'asf',
            designation: 'Software Engineer',
            level: 'L1'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if level is not passed',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            designation: 'Sr. Product Owner',
            dateOfJoining: '2021-09-04'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if level is invalid',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            designation: 'Sr. Product Owner',
            dateOfJoining: '2021-09-04',
            level: 'level'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if business Unit is not passed',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            designation: 'Sr. Product Owner',
            dateOfJoining: '2021-09-04',
            level: 'L0'
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if business Unit is passed as blank',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            designation: 'Sr. Product Owner',
            dateOfJoining: '2021-09-04',
            level: 'L0',
            businessUnit: ''
        },
        status: 0
    },
    {
        it: 'As a admin I should validate if business Unit is not valid',
        options: {
            employeeId: 200,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            designation: 'Sr. Product Owner',
            dateOfJoining: '2021-09-04',
            level: 'L0',
            businessUnit: 'L8'
        },
        status: 0
    }]
};
