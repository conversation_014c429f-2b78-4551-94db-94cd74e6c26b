
const AddProjectService = require('./addProjectService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for add a new project
 */
class AddProjectController {

    /**
     * @desc This function is being used to add a new project
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    static async addProject (req, res) {
        try {
            const data = await AddProjectService.addProject(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = AddProjectController;
