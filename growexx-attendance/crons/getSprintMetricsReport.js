const dotenv = require('dotenv');
const { createLogger, format, transports } = require('winston');
const getSprintMetrics = require('./sprintMetricsReportHelper');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

const { combine, timestamp, printf } = format;
// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');
const loggerFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} => ${level}: ${message}`;
});

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [
    new transports.Console({})
];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME })
    );
}
const logger = createLogger({
    format: combine(
        timestamp(),
        loggerFormat
    ),
    transports: appTransports
});
let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}
const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};


const connectDB = async () => {
    try {
        return process.env.NODE_ENV !== 'testing' && await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        logger.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchSprintMetricsReport = async () => {
    try {
        logger.info(`Fetch Sprint Metrics Report Cron Started at ${new Date}`);
        await connectDB();
        const syncAllData = process.argv[process.argv.length - 1];
        await getSprintMetrics(syncAllData, null);
    } catch (err) {
        logger.error('Fetch Sprint Metrics Report error', err);
    } finally {
        logger.info(`Fetch Sprint Metrics Report Cron Ended at ${new Date}`);
        process.env.NODE_ENV !== 'testing' && process.exit();
    }
};

fetchSprintMetricsReport();

module.exports = fetchSprintMetricsReport;
