const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.paths['/user/leave'] = {
        post: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'User'
            ],
            description: 'Add user\'s leave',
            summary: 'Add user\'s leave',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Add user\'s leave',
                required: true,
                schema: {
                    $ref: '#/definitions/addUserLeave'
                }
            }],
            responses: {
                200: {
                    description: 'Add user leave',
                    schema: {
                        $ref: '#/definitions/successAddUserLeave'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    swaggerJson.definitions.addUserLeave = {
        type: 'object',
        properties: {
            leaveType: {
                type: 'string',
                example: 'Sick or Casual Leave'
            },
            startDate: {
                type: 'string',
                example: 'YYYY-MM-DD'
            },
            endDate: {
                type: 'string',
                example: 'YYYY-MM-DD'
            },
            startDateDuration: {
                type: 'string',
                example: 'first/second/full'
            },
            endDateDuration: {
                type: 'string',
                example: 'second/full'
            },
            employeeId: {
                type: 'string',
                example: 'Employee Id'
            }
        }
    };

    swaggerJson.definitions.successAddUserLeave = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {}
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    return swaggerJson;
};

