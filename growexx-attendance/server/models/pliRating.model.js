/**
 * @name PLI Rating model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    menteeId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    mentorId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    month: {
        type: Number,
        required: true,
        min: 1,
        max: 12
    },
    year: {
        type: Number,
        required: true
    },
    projectRatings: [{
        projectId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'project',
            required: true
        },
        projectWeightage: {
            type: Number,
            required: true,
            min: 0,
            max: 100
        },
        parameterScores: [{
            parameterId: {
                type: appMongoose.Schema.Types.ObjectId,
                ref: 'pliParameters'
            },
            projectType: {
                type: String,
                enum: ['Dedicated', 'Fixed'],
                required: true
            },
            parentParameter: {
                type: String,
                required: true,
                trim: true
            },
            autoFilled: {
                type: Boolean,
                default: true
            },
            score: {
                type: Number
            },
            comments: {
                type: String,
                trim: true
            },
            childScores: [{
                childParameterId: String,
                sprintScores: [{
                    sprintNumber: {
                        type: String,
                        required: true
                    },
                    score: {
                        type: Number
                    },
                    comment: {
                        type: String,
                        trim: true
                    }
                }]
            }]
        }]
    }],
    proactivenessScores: [{
        criteria: {
            type: String,
            enum: ['Meeting Deadlines', 'Task Prioritization', 'Time Tracking',
                'Initiative', 'Issue Identification', 'Solution Proposals',
                'Stakeholder Communication', 'Adaptability'],
            required: true
        },
        score: {
            type: Number,
            required: true
        }
    }],
    techRoadmapRating: {
        type: Number
    },
    techRoadmapComments: {
        type: String,
        trim: true
    },
    techRoadmapDocumentLink: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        enum: ['Draft', 'Submitted', 'Accepted', 'QueryRaised', 'Finalized'],
        default: 'Draft'
    },
    menteeQuery: {
        type: String,
        trim: true
    },
    mentorResponse: {
        type: String,
        trim: true
    },
    superAdminOverride: {
        type: Boolean,
        default: false
    },
    superAdminComments: {
        type: String,
        trim: true
    },
    finalScore: {
        type: Number
    },
    isFrozen: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('pliRating', schema);
