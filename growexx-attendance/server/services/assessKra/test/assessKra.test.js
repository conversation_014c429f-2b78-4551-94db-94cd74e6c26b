const TestCase = require('./assessKra');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin <PERSON>ken
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Assess Kra', () => {

    TestCase.assessKra.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .put('/kra/assessment')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.statusCode, data.status);
        });
    });
});
