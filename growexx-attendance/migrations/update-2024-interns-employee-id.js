module.exports = {
    async up(db) {
        const data = [
            { employeeId: 356, email: '<EMAIL>' },
            { employeeId: 357, email: '<EMAIL>' },
            { employeeId: 358, email: '<EMAIL>' },
            { employeeId: 359, email: '<EMAIL>' },
            { employeeId: 360, email: '<EMAIL>' },
            { employeeId: 361, email: '<EMAIL>' },
            { employeeId: 362, email: '<EMAIL>' },
            { employeeId: 363, email: '<EMAIL>' },
            { employeeId: 364, email: '<EMAIL>' },
            { employeeId: 365, email: '<EMAIL>' },
            { employeeId: 366, email: '<EMAIL>' },
            { employeeId: 367, email: '<EMAIL>' },
            { employeeId: 368, email: '<EMAIL>' },
            { employeeId: 369, email: '<EMAIL>' },
            { employeeId: 370, email: '<EMAIL>' },
            { employeeId: 376, email: '<EMAIL>' },
            { employeeId: 377, email: '<EMAIL>' },
        ];

        data.map(async obj => {
            return await db.collection('users').updateOne({ email: obj.email }, {
                $set: { employeeId: obj.employeeId }
            });
        });
    }
};
