/**
 * Migration for pliRating collection
 * Up: Create collection if it doesn't exist
 * Down: Drop collection
 */
module.exports = {
  async up(db, client) {
    const collections = await db.listCollections({ name: 'pliRating' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('pliRating');
    }
  },

  async down(db, client) {
    const collections = await db.listCollections({ name: 'pliRating' }).toArray();
    if (collections.length > 0) {
      await db.collection('pliRating').drop();
    }
  }
};