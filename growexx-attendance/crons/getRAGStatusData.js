/* eslint-disable no-unused-vars */
/* eslint-disable no-console */
/* eslint-disable max-len */
const { transports } = require('winston');
const getRagStatus = require('./ragStatusHelper');
const dotenv = require('dotenv');

const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });

global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.MOMENT = require('moment');
global._ = require('lodash');

const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [new transports.Console({})];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME }));
}

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(
        process.env.DB_PASSWORD
    )}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: false,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        console.info('MongoDB connection error.', err);
        return false;
    }
};


const main = async () => {
    await connectDB();
    const syncAllData = process.argv[process.argv.length - 1];
    await getRagStatus(syncAllData, false, null);
    process.exit();
};

main();

module.exports = main;
