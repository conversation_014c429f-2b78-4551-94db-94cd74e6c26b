const message = require('../../locales/en.json');

module.exports = swaggerJson => {
    swaggerJson.paths['/kra'] = {
        post: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Kra'
            ],
            description: 'Add kra details',
            summary: 'Add kra details',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'Add kra details',
                required: true,
                schema: {
                    $ref: '#/definitions/addKra'
                }
            }],
            responses: {
                200: {
                    description: 'Add Kra details',
                    schema: {
                        $ref: '#/definitions/successAddKra'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        },
        put: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Kra'
            ],
            description: 'edit kra details',
            summary: 'edit kra details',
            parameters: [{
                in: 'body',
                name: 'body',
                description: 'edit kra details',
                required: true,
                schema: {
                    $ref: '#/definitions/editKra'
                }
            }],
            responses: {
                200: {
                    description: 'edit Kra details',
                    schema: {
                        $ref: '#/definitions/successeditKra'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.editKra = {
        type: 'object',
        properties: {
            kras: {
                type: 'array',
                example: `[{
                    "_id": "62e7b1cae7353ac179da3b0f",
                    "category": "Business Result",
                    "outcome": "Planning",
                    "measure": "Estimation vs spent",
                    "target": "Inputs from:     - PM     - DM     - Customer"
                  }]`,
                required: true
            },
            deletedKras: {
                type: 'array',
                example: `[{
                    "62e7b1cae7353ac179da3b0f"]`,
                required: true
            }
        }
    };

    swaggerJson.definitions.successeditKra = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    'n': 1,
                    'nModified': 1,
                    'ok': 1
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.addKra = {
        type: 'object',
        properties: {
            category: {
                type: 'string',
                example: 'Business Result',
                required: true
            },
            outcome: {
                type: 'string',
                example: 'Developing high quality user story',
                required: true
            },
            measure: {
                type: 'string',
                example: 'Estimation vs spent',
                required: true
            },
            target: {
                type: 'string',
                example: 'This data can be obtained from Sprint report',
                required: true
            },
            designation: {
                type: 'string',
                example: 'Software Engineer',
                required: true
            },
            quarter:  {
                type: 'string',
                example:  'Q3',
                required: true
            },
            year:  {
                type: 'number',
                example:  2022,
                required: true
            }
        }
    };


    swaggerJson.definitions.successAddKra = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    '_id': '62cd28107fbfabd4ce8daad4',
                    'category': 'Business Result',
                    'outcome': 'Developing high quality user story',
                    'measure': 'Estimation vs spent',
                    'target': 'This data can be obtained from Sprint report',
                    'designation': 'Software Engineer',
                    'quarter': 'Q3',
                    'year': 2022,
                    'createdAt': '2022-07-12T07:51:44.941Z',
                    'updatedAt': '2022-07-12T07:51:44.941Z',
                    '__v': 0
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    return swaggerJson;

};
