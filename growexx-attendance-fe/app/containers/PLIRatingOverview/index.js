/**
 * PLIRating
 *
 * This component is the PLI Ratings Overview page for Superadmins
 */

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import {
  PageHeader,
  Input,
  Table,
  Button,
  Select,
  Row,
  Col,
  Modal,
  Tooltip,
  Spin,
  notification,
  Typography,
  Upload,
  message,
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import * as XLSX from 'xlsx';
import injectSaga from 'utils/injectSaga';
import injectReducer from 'utils/injectReducer';
import {
  makeSelectProcessingResults,
  makeSelectProcessingError,
  makeSelectResultsModalVisible,
  makeSelectMentorMenteeData,
  makeSelectMentorMenteeLoading,
} from './selectors';
import {
  processBulkAssignments,
  hideResultsModal,
  fetchMentorMenteeData,
  showModal,
  hideModal,
  overridePLIRating,
  finalizePLIRating,
} from './actions';
import reducer from './reducer';
import saga from './saga';

/**
 * RatingDetailSection component for displaying override score and comments
 */
const RatingDetailSection = ({ ratingDetail }) => {
  if (!ratingDetail) return null;

  const showOverrideScore =
    ratingDetail.overrideScore !== undefined &&
    ratingDetail.overrideScore !== null &&
    ratingDetail.status === 'Finalized';

  const showComments =
    ratingDetail.comments && ratingDetail.status === 'Finalized';

  if (!showOverrideScore && !showComments) return null;

  return (
    <>
      {showOverrideScore && (
        <Row gutter={16} style={{ marginBottom: '20px' }}>
          <Col span={8}>
            <div className="detail-label">Override Score:</div>
            <div className="detail-value">
              {`${(ratingDetail.overrideScore * 20).toFixed(1)}%`}
            </div>
          </Col>
        </Row>
      )}
      {showComments && (
        <>
          <div className="detail-label">Comments:</div>
          <div className="detail-value">{ratingDetail.comments}</div>
        </>
      )}
    </>
  );
};

RatingDetailSection.propTypes = {
  ratingDetail: PropTypes.object,
};

/**
 * Presentational component for displaying skipped emails list
 * @param {Object} props - Component props
 * @param {Array} props.skippedEmails - Array of skipped email strings
 * @returns {JSX.Element|null} Component JSX or null
 */
const SkippedEmailsList = ({ skippedEmails }) => {
  if (!skippedEmails || skippedEmails.length === 0) return null;

  return (
    <div style={{ marginBottom: '16px' }}>
      <Typography.Title level={5}>Skipped Emails:</Typography.Title>
      <ul>
        {skippedEmails.map(email => (
          <li key={email}>{email}</li>
        ))}
      </ul>
    </div>
  );
};

SkippedEmailsList.propTypes = {
  skippedEmails: PropTypes.array,
};

/**
 * Presentational component for displaying processing errors list
 * @param {Object} props - Component props
 * @param {Array} props.errors - Array of error message strings
 * @returns {JSX.Element|null} Component JSX or null
 */
const ProcessingErrorsList = ({ errors }) => {
  if (!errors || errors.length === 0) return null;

  return (
    <div style={{ marginBottom: '16px' }}>
      <Typography.Title level={5} style={{ color: 'red' }}>
        Errors:
      </Typography.Title>
      <ul>
        {errors.map(error => (
          <li key={error.email + error.reason} style={{ color: 'red' }}>{`${
            error.email
          }: ${error.reason}`}</li>
        ))}
      </ul>
    </div>
  );
};

ProcessingErrorsList.propTypes = {
  errors: PropTypes.array,
};

/**
 * ProcessingResultSection component for displaying success count
 */
const SuccessCountSection = ({ count }) => (
  <div style={{ marginBottom: '16px' }}>
    <Typography.Title level={5} style={{ color: '#52c41a' }}>
      Successfully Processed
    </Typography.Title>
    <Typography.Paragraph>
      {count} assignments were successfully processed
    </Typography.Paragraph>
  </div>
);

SuccessCountSection.propTypes = {
  count: PropTypes.number,
};

/**
 * ProcessingResultSection component for displaying skipped emails
 */
const SkippedEmailsSection = ({ emails }) => (
  <div style={{ marginBottom: '16px' }}>
    <Typography.Title level={5} style={{ color: '#faad14' }}>
      Skipped Emails
    </Typography.Title>
    <Typography.Paragraph>
      The following emails were skipped as they are not registered in the
      system:
    </Typography.Paragraph>
    <ul
      style={{
        maxHeight: '150px',
        overflowY: 'auto',
        backgroundColor: '#fffbe6',
        padding: '8px 16px',
        borderRadius: '4px',
        border: '1px solid #ffe58f',
      }}
    >
      {emails.map(email => (
        <li key={email}>{email}</li>
      ))}
    </ul>
  </div>
);

SkippedEmailsSection.propTypes = {
  emails: PropTypes.array.isRequired,
};

/**
 * SummarySection component for displaying summary
 */
const SummarySection = ({ results }) => (
  <div
    style={{
      marginTop: '16px',
      padding: '12px',
      backgroundColor: '#f5f5f5',
      borderRadius: '4px',
    }}
  >
    <Typography.Paragraph>
      <strong>Summary:</strong>
    </Typography.Paragraph>
    {results && results.summary ? (
      <ul>
        {results.summary.split('. ').map((line, index) => {
          const trimmedLine = line.trim();
          // eslint-disable-next-line react/no-array-index-key
          return trimmedLine ? <li key={index}>{trimmedLine}</li> : null;
        })}
      </ul>
    ) : (
      <ul>
        <li>
          Total assignments processed:{' '}
          {results && results.totalCount !== undefined ? results.totalCount : 0}
        </li>
        <li>
          Successful assignments:{' '}
          {results && results.successfulAssignments !== undefined
            ? results.successfulAssignments
            : 0}
        </li>
        <li>
          Skipped emails:{' '}
          {(results && results.skippedEmails && results.skippedEmails.length) ||
            0}
        </li>
        <li>
          Errors encountered:{' '}
          {(results && results.errors && results.errors.length) || 0}
        </li>
      </ul>
    )}
  </div>
);

SummarySection.propTypes = {
  results: PropTypes.object.isRequired,
};

/**
 * ModalTitle component for displaying mentor details in modal header
 */
const MentorModalTitle = ({ mentor }) => (
  <div>
    <div>Mentor Name: {mentor && mentor.mentor}</div>
    {mentor && mentor.employeeId && (
      <div style={{ fontSize: '14px', color: '#666' }}>
        Employee ID: {mentor.employeeId}
      </div>
    )}
  </div>
);

MentorModalTitle.propTypes = {
  mentor: PropTypes.object,
};

/**
 * MenteeList component for displaying list of mentees
 */
const MenteeList = ({ mentees, onDelete }) => (
  <div
    style={{
      maxHeight: '400px',
      overflowY: 'auto',
      marginBottom: '16px',
    }}
  >
    {mentees &&
      mentees.map(mentee => (
        <div
          key={mentee.id}
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <div>
            <div>{mentee.name || mentee.email}</div>
            {mentee.name && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                {mentee.email}
              </div>
            )}
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type="link"
              onClick={() => {
                window.location.href = `/pli/employee-profile/${mentee.empId}`;
              }}
            >
              View
            </Button>
            <Button type="link" danger onClick={() => onDelete(mentee.id)}>
              Delete
            </Button>
          </div>
        </div>
      ))}
  </div>
);

MenteeList.propTypes = {
  mentees: PropTypes.array,
  onDelete: PropTypes.func.isRequired,
};

/**
 * ProcessingResultsContent component for displaying processing results
 */
const ProcessingResultsContent = ({ results }) => (
  <div>
    <Typography.Title level={5}>Processing Results</Typography.Title>

    {results.successCount !== undefined && (
      <SuccessCountSection count={results.successCount} />
    )}

    {results.skippedEmails && results.skippedEmails.length > 0 && (
      <SkippedEmailsSection emails={results.skippedEmails} />
    )}

    {results.errors && results.errors.length > 0 && (
      <ProcessingErrorsSection errors={results.errors} />
    )}

    <SummarySection results={results} />
  </div>
);

ProcessingResultsContent.propTypes = {
  results: PropTypes.object,
};

/**
 * ProcessingErrorsSection component for displaying processing errors
 */
const ProcessingErrorsSection = ({ errors }) => (
  <div style={{ marginBottom: '16px' }}>
    <Typography.Title level={5} style={{ color: '#ff4d4f' }}>
      Processing Errors
    </Typography.Title>
    <Typography.Paragraph>
      The following errors occurred during processing:
    </Typography.Paragraph>
    <ul
      style={{
        maxHeight: '150px',
        overflowY: 'auto',
        backgroundColor: '#fff1f0',
        padding: '8px 16px',
        borderRadius: '4px',
        border: '1px solid #ffa39e',
      }}
    >
      {errors.map(error => (
        <li key={error.email + error.reason} style={{ color: '#ff4d4f' }}>
          <strong>{error.email}:</strong> {error.reason}
        </li>
      ))}
    </ul>
  </div>
);

ProcessingErrorsSection.propTypes = {
  errors: PropTypes.array.isRequired,
};

/**
 * ExcelValidator class for handling Excel file validation and processing
 */
export class ExcelValidator {
  constructor() {
    this.MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    this.MAX_ROWS = 1000;
    this.CHUNK_SIZE = 100;
    this.EXPECTED_HEADERS = ['Mentor Email', 'Mentee Email'];
    this.EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  }

  /**
   * Validate file size
   * @param {File} file - The file to validate
   * @throws {Error} If file size exceeds limit
   */
  validateFileSize(file) {
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(
        `File size exceeds ${this.MAX_FILE_SIZE / (1024 * 1024)}MB limit`,
      );
    }
  }

  /**
   * Validate workbook structure
   * @param {Object} workbook - The XLSX workbook object
   * @throws {Error} If workbook is invalid
   */
  validateWorkbook(workbook) {
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new Error('Excel file contains no sheets');
    }

    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    if (!worksheet || Object.keys(worksheet).length === 0) {
      throw new Error('Excel sheet is empty');
    }

    // Get the actual range of data in the worksheet
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

    // Count actual rows with data
    let actualRowCount = 0;
    for (let row = range.s.r; row <= range.e.r; row += 1) {
      const rowHasData = Object.keys(worksheet).some(key => {
        const cell = XLSX.utils.decode_cell(key);
        return (
          cell.r === row &&
          worksheet[key].v !== undefined &&
          worksheet[key].v !== null
        );
      });
      if (rowHasData) {
        actualRowCount += 1;
      }
    }

    // Use actual row count instead of range.e.r
    if (actualRowCount > this.MAX_ROWS) {
      throw new Error(
        `Excel file contains too many rows (maximum ${
          this.MAX_ROWS
        } rows allowed). Found ${actualRowCount} rows.`,
      );
    }

    return { worksheet, range, actualRowCount };
  }

  /**
   * Get headers from worksheet
   * @param {Object} worksheet - The XLSX worksheet object
   * @returns {Array} Array of header strings
   */
  getHeadersFromWorksheet(worksheet) {
    const headers = [];
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

    // Read only the first row
    for (let col = range.s.c; col <= range.e.c; col += 1) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
      const cell = worksheet[cellRef];
      if (cell && cell.v !== undefined && cell.v !== null) {
        headers.push(cell.v);
      }
    }

    return headers;
  }

  /**
   * Validate headers
   * @param {Array} headers - The headers to validate
   * @throws {Error} If headers are invalid
   */
  validateHeaders(headers) {
    if (!headers || headers.length !== this.EXPECTED_HEADERS.length) {
      throw new Error(
        'Invalid Excel file format. Please use the template provided in "Download Sample Format"',
      );
    }

    if (
      !headers.every((header, index) => header === this.EXPECTED_HEADERS[index])
    ) {
      throw new Error(
        'Invalid Excel file format. Please use the template provided in "Download Sample Format"',
      );
    }
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} Whether email is valid
   */
  validateEmail(email) {
    return this.EMAIL_REGEX.test(email);
  }

  /**
   * Process worksheet in chunks
   * @param {Object} worksheet - The XLSX worksheet object
   * @param {Object} range - The worksheet range
   * @returns {Array} Processed data
   */
  processWorksheet(worksheet, range) {
    const jsonData = [];
    let headers = null;

    // Process only rows that have data
    for (let row = range.s.r; row <= range.e.r; row += 1) {
      const rowHasData = Object.keys(worksheet).some(key => {
        const cell = XLSX.utils.decode_cell(key);
        return (
          cell.r === row &&
          worksheet[key].v !== undefined &&
          worksheet[key].v !== null
        );
      });

      if (rowHasData) {
        const rowData = [];
        for (let col = range.s.c; col <= range.e.c; col += 1) {
          const cellRef = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellRef];
          rowData.push(cell ? cell.v : null);
        }

        if (rowData.length > 0) {
          if (!headers) {
            headers = rowData;
            this.validateHeaders(headers);
          } else {
            jsonData.push(rowData);
          }
        }
      }
    }

    return jsonData;
  }

  /**
   * Process and validate data rows
   * @param {Array} jsonData - The data to process
   * @returns {Object} Processed data and errors
   */
  processData(jsonData) {
    const errors = [];
    const processedData = [];

    jsonData.forEach((row, index) => {
      const mentorEmail = row[0];
      const menteeEmails = row[1];

      // Skip empty rows
      if (!mentorEmail && !menteeEmails) return;

      // Validate mentor email
      if (!mentorEmail) {
        errors.push(`Row ${index + 2}: Mentor Email is required`);
        return;
      }

      if (!this.validateEmail(mentorEmail)) {
        errors.push(
          `Row ${index + 2}: Invalid mentor email format: ${mentorEmail}`,
        );
        return;
      }

      // Validate mentee emails
      if (!menteeEmails) {
        errors.push(`Row ${index + 2}: Mentee Email is required`);
        return;
      }

      const menteeEmailList = menteeEmails
        .split(',')
        .map(email => email.trim())
        .filter(email => email !== '');

      if (menteeEmailList.length === 0) {
        errors.push(`Row ${index + 2}: No valid mentee emails found`);
        return;
      }

      const invalidMenteeEmails = menteeEmailList.filter(
        email => !this.validateEmail(email),
      );

      if (invalidMenteeEmails.length > 0) {
        errors.push(
          `Row ${index +
            2}: Invalid mentee email(s): ${invalidMenteeEmails.join(', ')}`,
        );
        return;
      }

      // Add valid row to processed data
      processedData.push({
        'Mentor email': mentorEmail,
        'Mentee email': menteeEmailList,
      });
    });

    return { errors, processedData };
  }

  /**
   * Process Excel file with retry logic
   * @param {File} file - The file to process
   * @returns {Promise<Object>} Processed data
   */
  async processFile(file) {
    try {
      this.validateFileSize(file);
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data, { type: 'array' });

      // Quick check for headers first
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      if (!worksheet) {
        throw new Error(
          'Invalid Excel file format: No worksheet found in the file',
        );
      }

      // Get headers directly from worksheet
      const headers = this.getHeadersFromWorksheet(worksheet);
      if (headers.length !== this.EXPECTED_HEADERS.length) {
        throw new Error(
          `Invalid Excel file format: Expected ${
            this.EXPECTED_HEADERS.length
          } columns (${this.EXPECTED_HEADERS.join(', ')}), but found ${
            headers.length
          } columns (${headers.join(', ')})`,
        );
      }

      // Check headers match
      const mismatchedHeaders = headers.filter(
        (header, index) => header !== this.EXPECTED_HEADERS[index],
      );
      if (mismatchedHeaders.length > 0) {
        throw new Error(
          `Invalid Excel file format: Column headers do not match. Expected "${this.EXPECTED_HEADERS.join(
            ', ',
          )}", but found "${headers.join(', ')}"`,
        );
      }

      // If headers are valid, proceed with full validation
      const { worksheet: validatedWorksheet, range } = this.validateWorkbook(
        workbook,
      );
      const jsonData = this.processWorksheet(validatedWorksheet, range);
      const { errors, processedData } = this.processData(jsonData);

      if (errors.length > 0) {
        // Throw error with all validation errors
        throw new Error(errors.join('\n'));
      }

      if (processedData.length === 0) {
        throw new Error(
          'No valid data found in the file. Please ensure the file contains at least one valid row of data.',
        );
      }

      return processedData;
    } catch (error) {
      // If it's a validation error, throw it directly
      if (
        error.message.includes('Row') ||
        error.message.includes('Invalid Excel file format')
      ) {
        throw error;
      }
      // For other errors, wrap them in a generic message
      throw new Error(`Error processing file: ${error.message}`);
    }
  }
}

/**
 * PLIRating class component
 */
class PLIRating extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      filters: {
        department: 'All',
        status: 'All',
        cycleMonth: 'All',
        search: '',
      },
      selectedRowKeys: [],
      form: {
        score: 0,
        comments: '',
      },
      downloadModalVisible: false,
      uploadModalVisible: false,
      isProcessing: false,
      errorModalVisible: false,
      validationErrors: [],
      editModalVisible: false,
      selectedMentor: null,
      filteredTableData: null,
    };
    this.searchTimer = null;
    this.mounted = false;
  }

  componentDidMount() {
    this.mounted = true;
    this.props.fetchMentorMenteeData();
  }

  componentWillUnmount() {
    this.mounted = false;
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }

  /**
   * Fetch ratings with current filters
   */
  fetchRatings = () => {
    if (!this.mounted) return;

    const { filters } = this.state;
    const filteredData = this.props.mentorMenteeData.filter(item => {
      // Department filter
      if (
        filters.department !== 'All' &&
        item.department !== filters.department
      ) {
        return false;
      }

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const empIdStr = String(item.empId || '').toLowerCase();
        const mentorStr = String(item.mentor || '').toLowerCase();
        const deptStr = String(item.department || '').toLowerCase();

        return (
          empIdStr.includes(searchLower) ||
          mentorStr.includes(searchLower) ||
          deptStr.includes(searchLower)
        );
      }

      return true;
    });

    // Only update state if component is still mounted
    if (this.mounted) {
      this.setState({ filteredTableData: filteredData });
    }
  };

  /**
   * Handle search input change
   * @param {Object} e - Event object
   */
  handleSearchChange = e => {
    const searchValue = e.target.value;

    // Clear any pending search timer
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    // Update state with the new search value
    this.setState(
      prevState => ({
        filters: {
          ...prevState.filters,
          search: searchValue,
        },
      }),
      () => {
        // Debounce the search with a 300ms delay
        this.searchTimer = setTimeout(() => {
          this.fetchRatings();
        }, 300);
      },
    );
  };

  /**
   * Handle department filter change
   * @param {string} value - Selected department
   */
  handleDepartmentChange = value => {
    this.setState(
      prevState => ({
        filters: {
          ...prevState.filters,
          department: value,
        },
      }),
      () => {
        this.fetchRatings();
      },
    );
  };

  /**
   * Handle status filter change
   * @param {string} value - Selected status
   */
  handleStatusChange = value => {
    this.setState(
      prevState => ({
        filters: {
          ...prevState.filters,
          status: value,
        },
      }),
      () => {
        this.fetchRatings();
      },
    );
  };

  /**
   * Handle cycle month filter change
   * @param {string} value - Selected cycle month
   */
  handleCycleMonthChange = value => {
    this.setState(
      prevState => ({
        filters: {
          ...prevState.filters,
          cycleMonth: value,
        },
      }),
      () => {
        this.fetchRatings();
      },
    );
  };

  /**
   * Handle view rating details
   * @param {Object} record - Rating record
   */
  handleViewRating = record => {
    this.props.fetchMentorMenteeData({
      ...this.state.filters,
      search: record.menteeId,
    });
    this.props.showModal(record);
  };

  /**
   * Handle override score form change
   * @param {string} field - Form field
   * @param {any} value - New value
   */
  handleFormChange = (field, value) => {
    this.setState(prevState => ({
      form: {
        ...prevState.form,
        [field]: value,
      },
    }));
  };

  /**
   * Submit override
   */
  handleOverride = () => {
    const { form } = this.state;
    const { currentRating } = this.props;

    if (!form.score) {
      notification.error({
        message: 'Error',
        description: 'Please enter a valid score',
      });
      return;
    }

    this.props.overridePLIRating(currentRating.menteeId, {
      score: form.score,
      comments: form.comments,
    });
    this.props.hideModal();
  };

  /**
   * Finalize rating
   * @param {Object} record - Rating record
   */
  handleFinalize = record => {
    this.props.finalizePLIRating(record.menteeId);
  };

  /**
   * Handle modal close
   */
  handleModalClose = () => {
    this.props.hideModal();
    this.setState({
      form: {
        score: 0,
        comments: '',
      },
    });
  };

  /**
   * Handle row selection change
   * @param {Array} selectedRowKeys - Selected row keys
   */
  handleSelectChange = selectedRowKeys => {
    this.setState({ selectedRowKeys });
  };

  /**
   * Handle approve all
   */
  handleApproveAll = () => {
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length === 0) {
      notification.warning({
        message: 'Warning',
        description: 'Please select at least one rating to approve',
      });
      return;
    }

    // In a real implementation, we would make an API call to approve all selected ratings
    notification.info({
      message: 'Info',
      description: 'This feature would approve all selected ratings',
    });
  };

  /**
   * Get color for status tag
   * @param {string} status - Status string
   * @returns {string} Color for tag
   */
  getStatusColor = status => {
    switch (status) {
      case 'Finalized':
        return 'green';
      case '  Started':
        return 'default';
      case 'Draft':
        return 'default';
      case 'Submitted':
        return 'processing';
      case 'Accepted':
        return 'success';
      case 'QueryRaised':
        return 'warning';
      default:
        return 'blue';
    }
  };

  /**
   * Reset all filters to default values
   */
  resetFilters = () => {
    this.setState(
      {
        filters: {
          department: 'All',
          search: '',
        },
      },
      () => {
        this.fetchRatings();
      },
    );
  };

  /**
   * Handle download sample format click
   */
  handleDownloadSampleClick = () => {
    this.setState({ downloadModalVisible: true });
  };

  /**
   * Handle download sample format
   */
  handleDownloadSample = () => {
    // Create sample data
    const data = [
      {
        'Mentor Email': '<EMAIL>',
        'Mentee Email':
          '<EMAIL>, <EMAIL>',
      },
    ];

    // Create worksheet
    const ws = XLSX.utils.json_to_sheet(data);

    // Create workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Mentor-Mentee Assignment');

    // Generate Excel file
    XLSX.writeFile(wb, 'mentor_mentee_assignment_template.xlsx');

    // Close modal
    this.setState({ downloadModalVisible: false });
  };

  /**
   * Handle download modal close
   */
  handleDownloadModalClose = () => {
    this.setState({ downloadModalVisible: false });
  };

  /**
   * Handle upload modal open
   */
  handleUploadClick = () => {
    this.setState({ uploadModalVisible: true });
  };

  /**
   * Handle upload modal close
   */
  handleUploadModalClose = () => {
    this.setState({
      uploadModalVisible: false,
      isProcessing: false,
      errorModalVisible: false,
      validationErrors: [],
    });
  };

  /**
   * Handle error modal close
   */
  handleErrorModalClose = () => {
    this.setState({
      errorModalVisible: false,
      validationErrors: [],
    });
  };

  /**
   * Process uploaded Excel file
   * @param {File} file - Uploaded file
   * @returns {Promise} Promise resolving to processed data
   */
  processExcelFile = async file => {
    try {
      const validator = new ExcelValidator();
      return await validator.processFile(file);
    } catch (error) {
      // Set validation errors in state
      this.setState({
        validationErrors: error.message.split('\n'),
        errorModalVisible: true,
        uploadModalVisible: false,
      });
      throw error;
    }
  };

  /**
   * Custom request handler for file upload
   * @param {Object} options - Upload options
   */
  customRequest = ({ file }) => {
    try {
      // Validate file type
      const isExcel =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';

      if (!isExcel) {
        throw new Error('Please upload only Excel files (.xlsx or .xls)');
      }

      // Process the file
      this.processExcelFile(file)
        .then(processedData => {
          if (processedData && processedData.length > 0) {
            // Dispatch the action to send data to the backend
            this.props.processBulkAssignments(processedData);
            this.setState({ uploadModalVisible: false });
          } else {
            this.setState({
              validationErrors: ['No valid data found in the file'],
              errorModalVisible: true,
              uploadModalVisible: false,
            });
          }
        })
        .catch(error => {
          // Don't show error message if it's a validation error (already shown in modal)
          if (error.message !== 'Validation failed') {
            message.error(error.message);
          }
        })
        .finally(() => {
          this.setState({ isProcessing: false });
        });
    } catch (error) {
      message.error(error.message);
      this.setState({ isProcessing: false });
    }
  };

  handleEditMentor = record => {
    this.setState({
      editModalVisible: true,
      selectedMentor: record,
    });
  };

  handleEditModalClose = () => {
    this.setState({
      editModalVisible: false,
      selectedMentor: null,
    });
  };

  // handleDeleteMentee = menteeId => {
  //   // TODO: Implement delete mentee functionality
  // };
  handleDeleteMentee = () => {
    // TODO: Implement delete mentee functionality
  };

  handleAddNewMentee = () => {
    // TODO: Implement add new mentee functionality
  };

  /**
   * Render method
   * @returns {JSX.Element} Component JSX
   */
  render() {
    const { mentorMenteeData, mentorMenteeLoading } = this.props;
    const { filteredTableData } = this.state;

    // Transform data for table display
    const tableData = (filteredTableData || mentorMenteeData).map(item => ({
      key: item.id,
      employeeId: item.empId || 'N/A',
      mentor: item.mentor || 'N/A',
      department: item.department || 'N/A',
      mentees: item.mentees || [],
    }));

    // Table columns
    const columns = [
      {
        title: 'EmpID',
        dataIndex: 'employeeId',
        key: 'empId',
        render: text => text || 'N/A',
      },
      {
        title: 'Mentor',
        dataIndex: 'mentor',
        key: 'mentor',
        render: text => text || 'N/A',
      },
      {
        title: 'Department',
        dataIndex: 'department',
        key: 'department',
        render: text => text || 'N/A',
      },
      {
        title: 'Mentees',
        dataIndex: 'mentees',
        key: 'mentees',
        render: (text, record) => (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {record.mentees.map(mentee => (
              <div
                key={mentee.id}
                style={{
                  backgroundColor: '#f9f0ff',
                  padding: '2px 8px',
                  borderRadius: '2px',
                  display: 'inline-block',
                  marginBottom: '4px',
                  fontSize: '12px',
                  color: '#722ed1',
                  border: '1px solid #d3adf7',
                  whiteSpace: 'nowrap',
                  maxWidth: 'fit-content',
                }}
              >
                {mentee.name || mentee.email}
              </div>
            ))}
          </div>
        ),
      },
      {
        title: 'Actions',
        key: 'actions',
        render: (_, record) => (
          <span>
            <Tooltip title="View Mentee Details">
              <EyeOutlined
                onClick={() => this.handleEditMentor(record)}
                style={{ cursor: 'pointer' }}
              />
            </Tooltip>
          </span>
        ),
      },
    ];

    return (
      <div style={{ padding: '20px' }}>
        <PageHeader title="Mentor-Mentee Assignments" />

        <div
          style={{
            marginBottom: '20px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <Input.Search
              placeholder="Search by EmpID, Mentor name, or Department"
              value={this.state.filters.search}
              onChange={this.handleSearchChange}
              onSearch={value => {
                this.setState(
                  prevState => ({
                    filters: {
                      ...prevState.filters,
                      search: value,
                    },
                  }),
                  () => {
                    this.fetchRatings();
                  },
                );
              }}
              style={{ width: 300 }}
              prefix={<SearchOutlined />}
              allowClear
              enterButton
            />

            <Select
              value={this.state.filters.department}
              onChange={this.handleDepartmentChange}
              style={{ width: 200 }}
              dropdownMatchSelectWidth={false}
              placeholder="Filter by Department"
            >
              <Select.Option value="All">All Departments</Select.Option>
              {Array.from(
                new Set(
                  this.props.mentorMenteeData.map(item => item.department),
                ),
              )
                .filter(Boolean)
                .sort()
                .map(dept => (
                  <Select.Option key={dept} value={dept}>
                    {dept}
                  </Select.Option>
                ))}
            </Select>

            <Button onClick={this.resetFilters}>Reset Filters</Button>
          </div>

          <div style={{ display: 'flex', gap: '10px' }}>
            <Tooltip title="Download the sample Excel format for bulk upload">
              <Button
                icon={<DownloadOutlined />}
                onClick={this.handleDownloadSampleClick}
              >
                Download Sample Format
              </Button>
            </Tooltip>
            <Tooltip title="Upload Excel file for bulk mentor-mentee assignment">
              <Button
                icon={<UploadOutlined />}
                type="primary"
                onClick={this.handleUploadClick}
              >
                Bulk Upload
              </Button>
            </Tooltip>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={tableData}
          loading={mentorMenteeLoading}
          rowKey="key"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
          }}
        />

        {/* Download Sample Format Modal */}
        <Modal
          title="Download Sample Format"
          open={this.state.downloadModalVisible}
          onCancel={this.handleDownloadModalClose}
          footer={[
            <Button key="close" onClick={this.handleDownloadModalClose}>
              Close
            </Button>,
            <Button
              key="download"
              type="primary"
              icon={<DownloadOutlined />}
              onClick={this.handleDownloadSample}
            >
              Download Template
            </Button>,
          ]}
        >
          <Typography.Title level={5}>Important Guidelines</Typography.Title>
          <ul style={{ paddingLeft: '20px' }}>
            <li>
              Please remove the dummy entries from the first row and start
              adding entries from there itself.
            </li>
            <li>
              The first column &quot;Mentor Email&quot; takes in only one value
              which consists of a valid email under the organization
            </li>
            <li>
              The second column &quot;Mentee Email&quot; takes in multiple
              comma-separated values consisting of valid emails under the
              organization
            </li>
            <li>Please maintain the format and occupy all rows sequentially</li>
          </ul>
        </Modal>

        {/* Upload Modal */}
        <Modal
          title="Bulk Upload Mentor-Mentee Assignment"
          open={this.state.uploadModalVisible}
          onCancel={this.handleUploadModalClose}
          footer={[
            <Button key="close" onClick={this.handleUploadModalClose}>
              Close
            </Button>,
          ]}
        >
          <Typography.Paragraph>
            Click on the &quot;Download Sample Format&quot; button to go through
            all the required format rules for the excel upload and download the
            sample excel.
          </Typography.Paragraph>

          <Upload.Dragger
            name="file"
            accept=".xlsx,.xls"
            showUploadList={false}
            customRequest={this.customRequest}
            onChange={this.handleUpload}
            beforeUpload={file => {
              const isExcel =
                file.type ===
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
              if (!isExcel) {
                message.error('You can only upload Excel files!');
                return Upload.LIST_IGNORE;
              }
              this.setState({ isProcessing: true });
              return true;
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag file to this area to upload
            </p>
            <p className="ant-upload-hint">
              Support for a single Excel file upload. Strictly prohibited from
              uploading company data or other banned files.
            </p>
          </Upload.Dragger>

          {this.state.isProcessing && (
            <div style={{ textAlign: 'center', marginTop: '20px' }}>
              <Spin tip="Processing file..." />
            </div>
          )}
        </Modal>

        {/* Error Modal */}
        <Modal
          title="Validation Errors"
          open={this.state.errorModalVisible}
          onCancel={this.handleErrorModalClose}
          footer={[
            <Button key="close" onClick={this.handleErrorModalClose}>
              Close
            </Button>,
          ]}
        >
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            <Typography.Paragraph>
              Please fix the following errors in your Excel file:
            </Typography.Paragraph>
            <ul>
              {this.state.validationErrors.map(error => (
                <li key={error} style={{ color: 'red', marginBottom: '8px' }}>
                  {error}
                </li>
              ))}
            </ul>
          </div>
        </Modal>

        {/* Edit Mentor Modal */}
        <Modal
          title={<MentorModalTitle mentor={this.state.selectedMentor} />}
          open={this.state.editModalVisible}
          onCancel={this.handleEditModalClose}
          footer={[
            <Button key="close" onClick={this.handleEditModalClose}>
              Close
            </Button>,
            <Button key="add" type="primary" onClick={this.handleAddNewMentee}>
              Add New Mentee
            </Button>,
          ]}
          width={600}
        >
          <MenteeList
            mentees={
              this.state.selectedMentor && this.state.selectedMentor.mentees
            }
            onDelete={this.handleDeleteMentee}
          />
        </Modal>

        {/* Results Modal */}
        <Modal
          title="Bulk Upload Results"
          open={this.props.resultsModalVisible}
          onCancel={this.props.hideResultsModal}
          footer={[
            <Button key="close" onClick={this.props.hideResultsModal}>
              Close
            </Button>,
          ]}
          width={600}
        >
          {this.props.processingError ? (
            <div style={{ color: 'red' }}>
              <Typography.Title level={5}>Error</Typography.Title>
              <Typography.Paragraph>
                {this.props.processingError}
              </Typography.Paragraph>
            </div>
          ) : (
            this.props.processingResults && (
              <ProcessingResultsContent
                results={this.props.processingResults}
              />
            )
          )}
        </Modal>

        <style jsx global>{`
          .detail-label {
            font-weight: bold;
          }

          .detail-value {
            margin-top: 5px;
          }
        `}</style>
      </div>
    );
  }
}

PLIRating.propTypes = {
  fetchMentorMenteeData: PropTypes.func.isRequired,
  mentorMenteeData: PropTypes.array,
  mentorMenteeLoading: PropTypes.bool,
  processBulkAssignments: PropTypes.func.isRequired,
  processingResults: PropTypes.object,
  processingError: PropTypes.string,
  resultsModalVisible: PropTypes.bool,
  hideResultsModal: PropTypes.func.isRequired,
  showModal: PropTypes.func,
  currentRating: PropTypes.object,
  overridePLIRating: PropTypes.func,
  hideModal: PropTypes.func,
  finalizePLIRating: PropTypes.func,
};

PLIRating.defaultProps = {
  mentorMenteeData: [],
  mentorMenteeLoading: false,
  processingResults: null,
  processingError: null,
  resultsModalVisible: false,
  showModal: () => {},
  currentRating: null,
  overridePLIRating: () => {},
  hideModal: () => {},
  finalizePLIRating: () => {},
};

/**
 * Map state to props
 */
const mapStateToProps = createStructuredSelector({
  mentorMenteeData: makeSelectMentorMenteeData(),
  mentorMenteeLoading: makeSelectMentorMenteeLoading(),
  processingResults: makeSelectProcessingResults(),
  processingError: makeSelectProcessingError(),
  resultsModalVisible: makeSelectResultsModalVisible(),
});

/**
 * Map dispatch to props
 */
function mapDispatchToProps(dispatch) {
  return {
    fetchMentorMenteeData: () => dispatch(fetchMentorMenteeData()),
    processBulkAssignments: data => dispatch(processBulkAssignments(data)),
    hideResultsModal: () => dispatch(hideResultsModal()),
    showModal: record => dispatch(showModal(record)),
    hideModal: () => dispatch(hideModal()),
    overridePLIRating: (menteeId, data) =>
      dispatch(overridePLIRating(menteeId, data)),
    finalizePLIRating: menteeId => dispatch(finalizePLIRating(menteeId)),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: 'pliRating', reducer });
const withSaga = injectSaga({ key: 'pliRating', saga });

export default compose(
  withReducer,
  withSaga,
  withConnect,
)(PLIRating);
