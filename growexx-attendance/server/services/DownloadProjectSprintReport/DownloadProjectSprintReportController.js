const Utils = require('../../util/utilFunctions');
const DownloadProjectSprintReportService = require('./DownloadProjectSprintReportService');

/**
 * Class represents controller for Get Project Tracker.
 */
class DownloadProjectSprintReportController {
    /**
   * @desc This function is being used to Get the Project and Sprint report for download.
   * <AUTHOR>
   * @since 28/11/2023
   * @param {Object} req Request
   * @param {Object} req.query Request query.
   * @param {function} res Response
   */
    static async downlodProjectSprintReport (req, res) {
        try {
            const data = await DownloadProjectSprintReportService.downlodProjectSprintReport(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = DownloadProjectSprintReportController;
