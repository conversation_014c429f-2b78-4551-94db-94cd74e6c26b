const data = require('../userEmailGithubMapping.json');

module.exports = {
    async up (db) {
        Object.keys(data).map(async email => {
            return db.collection('users').updateOne({ email }, {
                $set: { githubUsernames: data[email] }
            });
        });
    },

    async down (db) {
        await db.collection('users').updateMany({}, { $unset: { githubUsernames: [] } });
    }
};
