const Project = require('../../models/project.model');
const ProjectTeam = require('../../models/projectTeam.model');
const User = require('../../models/user.model');
const AddProjectValidator = require('./addProjectValidator');
const GeneralError = require('../../util/GeneralError');
/**
 * Class represents services for add a new project.
 */
class AddProjectService {

    /**
     * @desc This function is being used to add a project user
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addProject (req, user, locale) {
        const Validator = new AddProjectValidator(req.body, locale);
        Validator.validationAddProject();
        const { projectName, startDate, endDate } = req.body;
        const checkProjectCount = await Project.countDocuments( {
            projectName
        });

        if (checkProjectCount !== 0) {
            throw new GeneralError(locale('PROJECT_EXISTS'), 400);
        }

        const { addData, allProjectUser } = await this.getPreparedData(req, locale);

        const projectId = await Project.create(addData);
        if (allProjectUser.length > 0) {
            allProjectUser.forEach(async (user) => {
                if (user.empId) {
                    await ProjectTeam.addEmployeeIfNotEsists(projectId._id, user.empId, user.startDate, user.endDate, true);
                } else {
                    await ProjectTeam.addEmployeeIfNotEsists(projectId._id, user._id, startDate, endDate, false);
                }
            });
        }
        return projectId;
    }
    static async getPreparedData (req, locale) {
        const { projectName, reviewManager, startDate, endDate, pmUser, hasReviewManager, businessUnitId } = req.body;
        const addData = {
            projectName,
            startDate,
            pmUser,
            endDate,
            businessUnitId,
            users: []
        };
        if (hasReviewManager === true) {
            addData.reviewManager = reviewManager;
        }
        const allProjectUser = [];
        // PM User
        if (pmUser) {
            const projectManagerData = await User.findActiveEmployeeById(pmUser);
            if (!projectManagerData) {
                throw new GeneralError(locale('INVALID_REQUEST'), 400);
            }
            allProjectUser.push(projectManagerData);
            addData.pmUser = projectManagerData._id;
        }
        if (reviewManager) {
            const reviewManagerData = await User.findActiveEmployeeById(reviewManager);
            if (!reviewManagerData) {
                throw new GeneralError(locale('INVALID_REQUEST'), 400);
            }
            allProjectUser.push(reviewManagerData);
            addData.reviewManager = reviewManagerData._id;
        }

        return { addData, allProjectUser };
    }
}

module.exports = AddProjectService;
