import {
  fetchPLIRatings,
  fetchPLIRatingsSuccess,
  fetchPLIRatingsError,
  fetchPLIRatingDetail,
  fetchPLIRatingDetailSuccess,
  fetchPLIRatingDetailError,
  overridePLIRating,
  overridePLIRatingSuccess,
  overridePLIRatingError,
  finalizePLIRating,
  finalizePLIRatingSuccess,
  finalizePLIRatingError,
  showModal,
  hideModal,
  defaultAction,
  processBulkAssignments,
  processBulkAssignmentsSuccess,
  processBulkAssignmentsError,
  showResultsModal,
  hideResultsModal,
  fetchMentorMenteeData,
  fetchMentorMenteeDataSuccess,
  fetchMentorMenteeDataError,
} from '../actions';

import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATINGS_SUCCESS,
  FETCH_PLI_RATINGS_ERROR,
  FETCH_PLI_RATING_DETAIL,
  FETCH_PLI_RATING_DETAIL_SUCCESS,
  FETCH_PLI_RATING_DETAIL_ERROR,
  OVERRIDE_PLI_RATING,
  OVERRIDE_PLI_RATING_SUCCESS,
  OVERRIDE_PLI_RATING_ERROR,
  FINALIZE_PLI_RATING,
  FINALIZE_PLI_RATING_SUCCESS,
  FINALIZE_PLI_RATING_ERROR,
  SHOW_MODAL,
  HIDE_MODAL,
  DEFAULT_ACTION,
  PROCESS_BULK_ASSIGNMENTS,
  PROCESS_BULK_ASSIGNMENTS_SUCCESS,
  PROCESS_BULK_ASSIGNMENTS_ERROR,
  SHOW_RESULTS_MODAL,
  HIDE_RESULTS_MODAL,
  FETCH_MENTOR_MENTEE_DATA,
  FETCH_MENTOR_MENTEE_DATA_SUCCESS,
  FETCH_MENTOR_MENTEE_DATA_ERROR,
} from '../constants';

describe('PLI Rating Actions', () => {
  describe('fetchPLIRatings', () => {
    it('should return the correct type and params', () => {
      const params = { department: 'Engineering' };
      const expected = {
        type: FETCH_PLI_RATINGS,
        params,
      };
      expect(fetchPLIRatings(params)).toEqual(expected);
    });
  });

  describe('fetchPLIRatingsSuccess', () => {
    it('should return the correct type and ratings', () => {
      const ratings = [{ id: 1, name: 'Test Rating' }];
      const expected = {
        type: FETCH_PLI_RATINGS_SUCCESS,
        ratings,
      };
      expect(fetchPLIRatingsSuccess(ratings)).toEqual(expected);
    });
  });

  describe('fetchPLIRatingsError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to fetch ratings');
      const expected = {
        type: FETCH_PLI_RATINGS_ERROR,
        error,
      };
      expect(fetchPLIRatingsError(error)).toEqual(expected);
    });
  });

  describe('fetchPLIRatingDetail', () => {
    it('should return the correct type and id', () => {
      const id = '123';
      const expected = {
        type: FETCH_PLI_RATING_DETAIL,
        id,
      };
      expect(fetchPLIRatingDetail(id)).toEqual(expected);
    });
  });

  describe('fetchPLIRatingDetailSuccess', () => {
    it('should return the correct type and rating detail', () => {
      const ratingDetail = { id: '123', name: 'Test Rating Detail' };
      const expected = {
        type: FETCH_PLI_RATING_DETAIL_SUCCESS,
        ratingDetail,
      };
      expect(fetchPLIRatingDetailSuccess(ratingDetail)).toEqual(expected);
    });
  });

  describe('fetchPLIRatingDetailError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to fetch rating detail');
      const expected = {
        type: FETCH_PLI_RATING_DETAIL_ERROR,
        error,
      };
      expect(fetchPLIRatingDetailError(error)).toEqual(expected);
    });
  });

  describe('overridePLIRating', () => {
    it('should return the correct type, id, and data', () => {
      const id = '123';
      const data = { score: 4, comments: 'Good' };
      const expected = {
        type: OVERRIDE_PLI_RATING,
        id,
        data,
      };
      expect(overridePLIRating(id, data)).toEqual(expected);
    });
  });

  describe('overridePLIRatingSuccess', () => {
    it('should return the correct type and result', () => {
      const result = { id: '123', score: 4, comments: 'Good' };
      const expected = {
        type: OVERRIDE_PLI_RATING_SUCCESS,
        result,
      };
      expect(overridePLIRatingSuccess(result)).toEqual(expected);
    });
  });

  describe('overridePLIRatingError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to override rating');
      const expected = {
        type: OVERRIDE_PLI_RATING_ERROR,
        error,
      };
      expect(overridePLIRatingError(error)).toEqual(expected);
    });
  });

  describe('finalizePLIRating', () => {
    it('should return the correct type and id', () => {
      const id = '123';
      const expected = {
        type: FINALIZE_PLI_RATING,
        id,
      };
      expect(finalizePLIRating(id)).toEqual(expected);
    });
  });

  describe('finalizePLIRatingSuccess', () => {
    it('should return the correct type and result', () => {
      const result = { id: '123', status: 'finalized' };
      const expected = {
        type: FINALIZE_PLI_RATING_SUCCESS,
        result,
      };
      expect(finalizePLIRatingSuccess(result)).toEqual(expected);
    });
  });

  describe('finalizePLIRatingError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to finalize rating');
      const expected = {
        type: FINALIZE_PLI_RATING_ERROR,
        error,
      };
      expect(finalizePLIRatingError(error)).toEqual(expected);
    });
  });

  describe('showModal', () => {
    it('should return the correct type and rating', () => {
      const rating = { id: 1, name: 'Test Rating' };
      const expected = {
        type: SHOW_MODAL,
        rating,
      };
      expect(showModal(rating)).toEqual(expected);
    });
  });

  describe('hideModal', () => {
    it('should return the correct type', () => {
      const expected = {
        type: HIDE_MODAL,
      };
      expect(hideModal()).toEqual(expected);
    });
  });

  describe('defaultAction', () => {
    it('should return the correct type', () => {
      const expected = {
        type: DEFAULT_ACTION,
      };
      expect(defaultAction()).toEqual(expected);
    });
  });

  describe('processBulkAssignments', () => {
    it('should return the correct type and data', () => {
      const data = [
        { mentorEmail: '<EMAIL>', menteeEmail: '<EMAIL>' },
      ];
      const expected = {
        type: PROCESS_BULK_ASSIGNMENTS,
        data,
      };
      expect(processBulkAssignments(data)).toEqual(expected);
    });
  });

  describe('processBulkAssignmentsSuccess', () => {
    it('should return the correct type and results', () => {
      const results = { success: true, processed: 5 };
      const expected = {
        type: PROCESS_BULK_ASSIGNMENTS_SUCCESS,
        results,
      };
      expect(processBulkAssignmentsSuccess(results)).toEqual(expected);
    });
  });

  describe('processBulkAssignmentsError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to process bulk assignments');
      const expected = {
        type: PROCESS_BULK_ASSIGNMENTS_ERROR,
        error,
      };
      expect(processBulkAssignmentsError(error)).toEqual(expected);
    });
  });

  describe('showResultsModal', () => {
    it('should return the correct type', () => {
      const expected = {
        type: SHOW_RESULTS_MODAL,
      };
      expect(showResultsModal()).toEqual(expected);
    });
  });

  describe('hideResultsModal', () => {
    it('should return the correct type', () => {
      const expected = {
        type: HIDE_RESULTS_MODAL,
      };
      expect(hideResultsModal()).toEqual(expected);
    });
  });

  describe('fetchMentorMenteeData', () => {
    it('should return the correct type', () => {
      const expected = {
        type: FETCH_MENTOR_MENTEE_DATA,
      };
      expect(fetchMentorMenteeData()).toEqual(expected);
    });
  });

  describe('fetchMentorMenteeDataSuccess', () => {
    it('should return the correct type and data', () => {
      const data = { mentors: [], mentees: [] };
      const expected = {
        type: FETCH_MENTOR_MENTEE_DATA_SUCCESS,
        data,
      };
      expect(fetchMentorMenteeDataSuccess(data)).toEqual(expected);
    });
  });

  describe('fetchMentorMenteeDataError', () => {
    it('should return the correct type and error', () => {
      const error = new Error('Failed to fetch mentor-mentee data');
      const expected = {
        type: FETCH_MENTOR_MENTEE_DATA_ERROR,
        error,
      };
      expect(fetchMentorMenteeDataError(error)).toEqual(expected);
    });
  });
});
