/**
 * @name sprint model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    boardId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'projectBoard'
    },
    jiraSprintId: {
        type: String
    },
    name: {
        type: String
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    completeDate: {
        type: Date
    },
    state: {
        type: String
        // active, future and past
    },
    sprintGoal: {
        type: String
    },
    sprintDesc: {
        type: String
    },
    sprintUrl: {
        type: String
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
module.exports = appMongoose.model('sprint', schema);
