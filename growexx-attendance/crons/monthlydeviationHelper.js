const dotenv = require('dotenv');
const axios = require('axios');
const { createLogger, format, transports } = require('winston');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });
const Portal = require('../server/models/portal.model');
const Story = require('../server/models/story.model');
const Sprint = require('../server/models/sprint.model');
const Epic = require('../server/models/epic.model');
const ProjectBoard = require('../server/models/projectBoard.model');
const Project = require('../server/models/project.model');
const PtCronStatus = require('../server/models/ptCronStatus.model');
const { ISSUE_TYPE } = require('../server/util/constants');
const { combine, timestamp, printf } = format;
const ProjectTrackerHealthCardService = require('../server/services/projectTrackerHealthCard/projectTrackerHealthCardService');
const ProjectTrackerMetrics = require('../server/models/projectTrackerMetrics.model');
const mongoose = require('mongoose');
const loggerFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} => ${level}: ${message}`;
});


const JIRA_URLS = {
    PROJECT_BOARD: '/rest/agile/1.0/board?projectKeyOrId=',
    EPIC_LIST: '/rest/agile/1.0/board/',
    PARTICULAR_EPIC: '/rest/api/2/issue/',
    STORY_LIST: '/rest/agile/1.0/epic/'
};
const LOGGER_FILE_NAME = 'projectTracker.log';
const appTransports = [
    new transports.Console({})
];
if (process.env.NODE_ENV !== 'testing') {
    appTransports.push(new transports.File({ filename: LOGGER_FILE_NAME })
    );
}
const logger = createLogger({
    format: combine(
        timestamp(),
        loggerFormat
    ),
    transports: appTransports
});
/**
* @desc This function is being used to fetch the jira data using api
* <AUTHOR>
* @since 27/11/2023
*/
const fetchJiraData = async (jiraUrl, portal, isPaginate = true) => {
    const responseData = [];
    let isLast = false;
    let startAt = 0;
    try {
        do {
            const url = isPaginate ? `${jiraUrl}&startAt=${startAt}` : jiraUrl;
            const response = await axios.get(url, {
                headers: {
                    Authorization: `Basic ${Buffer.from(
                        `${portal.email}:${portal.token}`
                    ).toString('base64')}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            if (!isPaginate) {
                return response.data;
            }
            responseData.push(...response.data.values);
            isLast = response.data.isLast;
            startAt += response.data.maxResults;
        } while (!isLast);
        return responseData;
    } catch (error) {
        logger.info(error.message);
        throw error;
    }
};

/**
* @desc This function is being used to fetch the epic's information
* <AUTHOR>
* @since 27/11/2023
*/
const fetchEpicEstimationData = async (portal, epicId) => {
    const url = `https://${portal.url}${JIRA_URLS.PARTICULAR_EPIC}${epicId}`;
    return await fetchJiraData(url, portal, false);
};

/**
* @desc This function is being used to fetch the story information
* <AUTHOR>
* @since 27/11/2023
*/
const fetchStoryData = async (portal, epicId, epicDbId, board) => {
    const stories = [];
    const bId = board.jiraBoardId;
    let startAt = 0;
    let total = 0;
    try {
        do {
            const url = `https://${portal.url}${JIRA_URLS.STORY_LIST}${epicId}/issue?startAt=${startAt}`;
            const response = await axios.get(url, {
                headers: {
                    Authorization: `Basic ${Buffer.from(
                        `${portal.email}:${portal.token}`
                    ).toString('base64')}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            stories.push(...response.data.issues);
            startAt += response.data.maxResults;
            total = response.data.total;
        } while (total > startAt);

        for (const story of stories) {
            logger.info(`                     Current Story: KEY= ${story.key} & ID=${story.id}`);
            try {
                const sprintData = await manageSprintData(story.fields.customfield_10020, board._id, story.fields.project, portal, bId);
                const issueType = story.fields.issuetype.name;
                const updatedStory = await Story.findOneAndUpdate(
                    {
                        $and: [
                            { jiraStoryId: story.id },
                            { boardId: board._id }
                        ]
                    },
                    {
                        epicId: epicDbId,
                        boardId: board._id,
                        jiraStoryId: story.id,
                        sprintId: sprintData._id,
                        title: story.fields.summary,
                        description: story.fields.description ?? '',
                        jiraStoryNo: story.key,
                        jiraStoryUrl: `https://${portal.url}/browse/${story.key}`,
                        originalEstimate: story.fields.timetracking?.originalEstimateSeconds ?? 0,
                        loggedEffort: story.fields?.aggregatetimespent ?? 0,
                        startDate: story.fields.customfield_10015 ? MOMENT(story.fields.customfield_10015) : null,
                        endDate: story.fields.duedate ? MOMENT(story.fields.duedate) : null,
                        status: story.fields.status.name,
                        issueType: story.fields.issuetype.name,
                        belongsTo: getAuthorOfStory(story.fields.summary)
                    },
                    {
                        upsert: true,
                        new: true,
                        returnNewDocument: true
                    }
                );
                if (issueType === ISSUE_TYPE.STORY) {
                    await fetchAndSaveLinkedBugs(portal, updatedStory, board._id);
                } else {
                    logger.info(`No linked bugs found for Story: KEY= ${story.key}`);
                }
            } catch (error) {
                logger.error('Error in fetching story data: ' + error.message);
            }
        }
    } catch (error) {
        logger.error(error.message);
        throw error;
    }
};

/**
* @desc This function is being used to fetch and save bugs which are linked to stories
* <AUTHOR>
* @since 16/09/2024
*/
const fetchAndSaveLinkedBugs = async (portal, story, boardId) => {
    try {
        const url = `https://${portal.url}/rest/api/3/search?jql=issue in linkedIssues(${story.jiraStoryNo}) AND issuetype = ${ISSUE_TYPE.BUG}`;
        const response = await axios.get(url, {
            headers: {
                Authorization: `Basic ${Buffer.from(
                    `${portal.email}:${portal.token}`
                ).toString('base64')}`,
                Accept: 'application/json',
                'Content-Type': 'application/json'
            }
        });

        const linkedBugs = response.data.issues;

        for (const bug of linkedBugs) {
            try {
                await Story.updateOne(
                    {
                        jiraStoryId: bug.id,
                        boardId: boardId
                    },
                    {
                        $set: {
                            epicId: story.epicId,
                            boardId: boardId,
                            jiraStoryId: bug.id,
                            sprintId: story.sprintId,
                            title: bug.fields.summary,
                            linkedStoryId: story._id,
                            jiraStoryNo: bug.key,
                            jiraStoryUrl: `https://${portal.url}/browse/${bug.key}`,
                            originalEstimate: bug.fields.timetracking?.originalEstimateSeconds ?? 0,
                            loggedEffort: bug.fields?.aggregatetimespent ?? 0,
                            startDate: bug.fields.customfield_10015 ? MOMENT(bug.fields.customfield_10015) : null,
                            endDate: bug.fields.duedate ? MOMENT(bug.fields.duedate) : null,
                            status: bug.fields.status.name,
                            issueType: bug.fields.issuetype.name,
                            belongsTo: getAuthorOfStory(bug.fields.summary)
                        }
                    },
                    {
                        upsert: true,
                        new: true
                    }
                );
                logger.info(`Linked Bug: KEY= ${bug.key} linked with Story ID=${story._id}`);
            } catch (error) {
                logger.error('Error in saving linked bug data: ' + error.message);
            }
        }
        return linkedBugs;
    } catch (error) {
        logger.error('Error fetching linked bugs: ' + error.message);
    }
};

/**
* @desc This utility function is being used to identify the author of issue
* <AUTHOR>
* @since 27/11/2023
*/
const getAuthorOfStory = (summary) => {
    const summaryLower = summary.toLowerCase();

    const tpmTlRegex = /\b(tpm|tl)\b/;
    const poRegex = /\bpo\b/;
    const scrumMasterRegex = /\bscrum\smaster\b/;
    const devOpsRegex = /\bdevops\b/;
    const scrumRitualsRegex = /\bscrum\srituals\b/;

    if (tpmTlRegex.test(summaryLower)) {
        return CONSTANTS.PROJECT_TRACKER.USER_TYPE.TPM_TL;
    } else if (poRegex.test(summaryLower) || scrumMasterRegex.test(summaryLower)) {
        return CONSTANTS.PROJECT_TRACKER.USER_TYPE.PO;
    } else if (devOpsRegex.test(summaryLower)) {
        return CONSTANTS.PROJECT_TRACKER.USER_TYPE.DEVOPS;
    } else if (scrumRitualsRegex.test(summaryLower)) {
        return CONSTANTS.PROJECT_TRACKER.USER_TYPE.SCRUM_RITUALS;
    } else {
        return CONSTANTS.PROJECT_TRACKER.USER_TYPE.DEV;
    }
};

/**
* @desc This function is being used to manage sprint data fetched from jira
* <AUTHOR>
* @since 27/11/2023
*/
const manageSprintData = async (sprintData, boardId, projectData, portal, jiraBoardId) => {
    if (!sprintData) {
        return { _id: null };
    }
    sprintData = sprintData[0];
    const key = projectData.key;
    const id = sprintData.id;
    let sprintUrl;
    if (key && id) {
        sprintUrl = `https://${portal.url}/jira/software/c/projects/${key}/boards/${jiraBoardId}/reports/sprint-retrospective?sprint=${id}`;
    } else {
        sprintUrl = 'NA';
    }
    const sprintInfo = await Sprint.findOneAndUpdate(
        {
            $and: [
                { jiraSprintId: sprintData.id },
                { boardId }
            ]
        },
        {
            boardId,
            jiraSprintId: sprintData.id,
            name: sprintData.name,
            startDate: sprintData.startDate ? MOMENT(sprintData.startDate) : null,
            endDate: sprintData.endDate ? MOMENT(sprintData.endDate) : null,
            state: sprintData.state,
            sprintGoal: sprintData.goal,
            sprintUrl
        },
        {
            upsert: true,
            new: true
        });
    return { _id: sprintInfo._id };
};

/**
* @desc This function is being used to fetch the jira epic data
* <AUTHOR>
* @since 27/11/2023
*/
const fetchAllEpics = async (portal, boardId, board) => {
    const url = `https://${portal.url}${JIRA_URLS.EPIC_LIST}/${boardId}/epic?`;
    const epicData = await fetchJiraData(url, portal);
    const bId = board.jiraBoardId;

    for (const epic of epicData) {
        logger.info(`              Current Epic No : ${epic.id}`);

        try {
            const epicEstimationData = await fetchEpicEstimationData(portal, epic.id);
            const projectData = epicEstimationData.fields.project;
            const sprintData = await manageSprintData(epicEstimationData.customfield_10020, board._id, projectData, portal, bId);
            const epicInfo = await Epic.findOneAndUpdate(
                {
                    $and: [
                        { jiraEpicId: epic.id },
                        { boardId: board._id }
                    ]
                },
                {
                    key: epic.key,
                    boardId: board.id,
                    jiraEpicId: epic.id,
                    jiraEpicUrl: portal.url && epic.key ? `https://${portal.url}/browse/${epic.key}` : 'NA',
                    status: epicEstimationData.fields.status?.name ?? 'NA',
                    name: epicEstimationData.fields.name || epic.name,
                    summary: epicEstimationData.fields.summary,
                    sprintId: sprintData._id,
                    description: epicEstimationData.fields.description,
                    salesEstimate: epicEstimationData.fields?.timetracking?.originalEstimateSeconds ?? 0,
                    belongsTo: getAuthorOfStory(epicEstimationData.fields.summary)
                },
                {
                    upsert: true,
                    new: true
                });
            await fetchStoryData(portal, epic.id, epicInfo._id, board);
        } catch (error) {
            logger.error('Error in fetching story-epic data:', error.message);
        }
    }
};

/**
* @desc This function is being used to delete old project tracker data
* <AUTHOR>
* @since 27/12/2023
*/
const deleteOlderData = async (boardData) => {
    for (const board of boardData) {
        logger.info(`Deleting data of Current Board : ${board.jiraBoardName}`);
        try {
            let result;
            result = await Story.deleteMany({ boardId: board._id });
            logger.info(`Deleting stories with boardid : ${board._id} and deleted count : ${JSON.stringify(result)}`);
            result = await Epic.deleteMany({ boardId: board._id });
            logger.info(`Deleting epics with boardid : ${board._id} and deleted count : ${JSON.stringify(result)}`);
            result = await Sprint.deleteMany({ boardId: board._id });
            logger.info(`Deleting sprints with boardid : ${board._id} and deleted count : ${JSON.stringify(result)}`);
        } catch (error) {
            logger.error('Error in deleting data of board :' + board.id + error.message);
        }
    }
};

/**
* @desc This function is being used to fetch the jira board information
* <AUTHOR>
* @since 27/11/2023
*/
const getBoardByProject = async (portal, jiraProjectId, projectId) => {
    const url = `https://${portal.url}${JIRA_URLS.PROJECT_BOARD}${jiraProjectId}`;
    const boardData = await fetchJiraData(url, portal);
    const boardDbData = await ProjectBoard.find({ projectId: projectId });

    logger.info(`No of boards : ${boardDbData.length} of projectId : ${projectId}`);
    await deleteOlderData(boardDbData);

    try {
        const result = await ProjectBoard.deleteMany({ projectId: projectId });
        logger.info(`Deleting board with projectid : ${projectId} and deleted count : ${JSON.stringify(result)}`);
    } catch (error) {
        logger.error('Error in deleting board data:' + error.message);
    }

    let lastBoardInfo;
    for (const board of boardData) {
        logger.info(`       Current Board No : ${board.id}`);

        try {
            const boardInfo = await ProjectBoard.findOneAndUpdate(
                {
                    $and: [
                        { jiraBoardId: board.id },
                        { projectId }
                    ]
                },
                {
                    projectId,
                    jiraBoardId: board.id,
                    portalId: portal._id,
                    jiraBoardName: board.name
                },
                {
                    upsert: true,
                    new: true
                });
            await fetchAllEpics(portal, board.id, boardInfo);
            lastBoardInfo = boardInfo; // Store the last processed board
        } catch (error) {
            logger.error('Error in fetching epics data:' + error.message);
        }
    }
    return lastBoardInfo; // Return the last processed board
};

/**
* @desc This function is being used to fetch the jira project data
* <AUTHOR>
* @since 27/11/2023
*/
const fetchAndStoreProjectData = async (projectId = null) => {
    // eslint-disable-next-line no-useless-catch
    try {
        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: true });
        // console.log(cronStatus.isRunning);
        const pId = '60704d1eb965020716a63ab0';
        // const projects = await Project.find({ jiraProjectId: { $exists: true }, portalId: { $ne: pId } }).sort({ createdAt: -1 });
        let projects;
        if (!projectId) {
            projects = await Project.find({ jiraProjectId: { $exists: true }, portalId: { $ne: pId } }).sort({ createdAt: -1 });
        }
        else {
            logger.info(`current projectId - ${projectId}`);
            const query = { jiraProjectId: { $exists: true }, portalId: { $ne: pId }, _id: projectId };
            projects = await Project.find(query).sort({ createdAt: -1 });
        }
        const totalProjects = projects.length;
        logger.info(`======= Total Projects : ${totalProjects} =======`);
        for (const [index, project] of projects.entries()) {
            const remProjects = totalProjects - index - 1; // Calculate remaining projects
            const description = 'Currently processing project at index';
            logger.info(`== ${description} ${index}: ${project.projectName}. ${remProjects} projects more in queue ==`);
            try {
                const portalData = await Portal.findOne({ _id: project.portalId });
                await getBoardByProject(portalData, project.jiraProjectId, project._id);
            } catch (error) {
                logger.info(`JIRA project tracker data fetching failed for ${project.projectName}`);
            }
        }
        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: false });
    } catch (error) {
        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: false });
        throw error;
    }

};
const monthlyDeviationData = async (projectIds) => {
    console.log('monthlyDeviationData');

    try {
        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: true });
        const pId = '60704d1eb965020716a63ab0';
        const processedProjects = [];

        // Get projects with specific IDs
        const projects = await Project.find({
            _id: { $in: projectIds.map(id => mongoose.Types.ObjectId(id)) },
            jiraProjectId: { $exists: true },
            portalId: { $ne: pId }
        });

        if (!projects || !projects.length) {
            logger.info('No projects found to process');
            await PtCronStatus.findOneAndUpdate({}, { isCronRunning: false });
            return {
                totalProjects: 0,
                processedProjects: [],
                timestamp: new Date()
            };
        }

        for (const project of projects) {
            logger.info(`Processing project: ${project.projectName}`);
            try {
                const portalData = await Portal.findOne({ _id: project.portalId });
                logger.info('Portal data fetched');

                const boardData = await getBoardByProject(portalData, project.jiraProjectId, project._id);
                logger.info('Board data fetched');

                if (!boardData) {
                    throw new Error('No board data returned from getBoardByProject');
                }

                // Fetch health card metrics
                const healthCardData = await ProjectTrackerHealthCardService.getProjectTrackerHealthCard(
                    { query: { boardId: boardData._id.toString() } },
                    { __: (msg) => msg }
                );
                logger.info('Health card data fetched');

                // Store metrics
                const metricsStored = await storeProjectMetrics(boardData._id, healthCardData, project._id, project.projectName);
                logger.info(`Metrics stored successfully: ${metricsStored}`);

                processedProjects.push({
                    projectId: project._id,
                    projectName: project.projectName,
                    boardId: boardData._id,
                    status: 'success'
                });
            } catch (error) {
                logger.info(`JIRA project tracker data fetching failed for ${project.projectName}`);
                processedProjects.push({
                    projectId: project._id,
                    projectName: project.projectName,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: false });
        return {
            totalProjects: projects.length,
            processedProjects,
            timestamp: new Date()
        };
    } catch (error) {
        await PtCronStatus.findOneAndUpdate({}, { isCronRunning: false });
        throw error;
    }
};




const storeProjectMetrics = async (boardId, metricsData, projectId, projectName) => {
    try {
        const { epicEffortsInformation, countInformation } = metricsData;
        console.log(epicEffortsInformation, 'epicEffortsInformation');
        console.log(countInformation, 'countInformation');
        console.log(projectName, 'projectName');
        


        // Initialize metrics object
        const metrics = {
            done: { salesEstimate: '0.00', spent: '0.00', toBeSpent: '0.00', difference: '0.00' },
            inprogress: { salesEstimate: '0.00', spent: '0.00', toBeSpent: '0.00', difference: '0.00' },
            todo: { salesEstimate: '0.00', spent: '0.00', toBeSpent: '0.00', difference: '0.00' }
        };

        // Map epic information
        epicEffortsInformation.forEach(epic => {
            // Convert status to match our metrics object keys
            const status = epic.status.toLowerCase().replace(/\s+/g, '');

            // Check if we have a matching status in our metrics object
            if (metrics.hasOwnProperty(status)) {
                metrics[status] = {
                    salesEstimate: epic.totalSalesEstimates,
                    spent: epic.totalLoggedEfforts,
                    toBeSpent: epic.toBeLoggedEfforts,
                    difference: epic.difference
                };
            }
        });

        const metricsDoc = {
            projectId: mongoose.Types.ObjectId(projectId),
            projectName: projectName,
            boardId: mongoose.Types.ObjectId(boardId),
            totalHours: {
                planned: countInformation.totalSalesEstimate,
                actual: countInformation.totalSpent,
                difference: countInformation.totalDifference
            },
            metrics: {
                done: metrics.done,
                inProgress: metrics.inprogress,
                toDo: metrics.todo
            },
            epicCount: countInformation.totalEpicCount,
            storyCount: countInformation.totalStoryCount,
            storiesWithoutEpic: countInformation.storiesWithoutEpics,
            epicsWithoutSalesEstimate: countInformation.epicWithoutSE,
            bugsWithoutLinkedStories: countInformation.bugsWithoutLinkedStories,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        console.log(metricsDoc, 'metricsDoc');
        const db = mongoose.connection;
        const result = await db.collection('projecttrackermetrics').insertOne(metricsDoc);

        logger.info(`Project metrics stored successfully for boardId: ${boardId}`);
        return result;

    } catch (error) {
        logger.error('Error storing project metrics:', error.message);
        throw error;
    }
};
const projectTrackerHelper = {
    fetchAndStoreProjectData,
    monthlyDeviationData,
    storeProjectMetrics
};
module.exports = projectTrackerHelper;
