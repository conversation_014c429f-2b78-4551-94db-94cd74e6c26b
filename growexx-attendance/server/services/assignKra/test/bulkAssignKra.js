module.exports = {
    bulkAssignKra: [
        {
            it: 'As a admin, I should validate Bulk kra should not save if userList is passed blank',
            options: {
                'projectId': '6089bafc940729317d5a5104',
                'userList': [],
                'quarter': 'Q1',
                'year': 2022
            },
            status: 400
        },
        {
            it: 'As a admin, I should validate Bulk kra saved properly',
            options: {
                'projectId': '6089bafc940729317d5a5104',
                'userList': [
                    '63ad69090c37bec5bfc96b8f',
                    '5f083c352a7908662c335551',
                    '62cd5fb5e123d78488b32a39',
                    '5f083c352a7908662c335554',
                    '5f083c352a7908662c335555',
                    '5f083c352a7908662c335559'
                ],
                'quarter': 'Q2',
                'year': 2022,
                'pmUser': '5f083c352a7908662c334532'
            },
            status: 200
        },
        {
            it: 'As a admin, I should validate Bulk kra saved properly for project without review manager',
            options: {
                'projectId': '63296da369029090ec09b3f8',
                'userList': [
                    '62cd5fb5e123d78488b32a39',
                    '5f083c352a7908662c335553',
                    '5f083c352a7908662c335555'
                ],
                'quarter': 'Q2',
                'year': 2022,
                'businessUnitId': '640c9ae585c55dd4f0fdfc56'
            },
            status: 200
        },
        {
            it: 'As a admin, I should validate Bulk kra saved properly.',
            options: {
                'projectId': '63296da369029090ec09b3f8',
                'userList': [
                    '62cd5fb5e123d78488b32a39',
                    '5f083c352a7908662c335553',
                    '5f083c352a7908662c335555',
                    '5f083c352a7908662c335558'
                ],
                'quarter': 'Q4',
                'year': 2023,
                'reviewManager': '5f083c352a7908662c33453d'
            },
            status: 200
        }
    ] };
