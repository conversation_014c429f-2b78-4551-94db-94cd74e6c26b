/**
 * Test for PLISection component
 */

// Mock modules before any imports
import React from 'react';
import { render } from 'react-testing-library';
import { Provider } from 'react-redux';
import { IntlProvider } from 'react-intl';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import configureStore from 'configureStore';
import { ROLES, ROUTES } from '../../constants';

// Import the component under test after mocks are set up
import PLISection from '../index';

jest.mock('../../UnauthorizedPage');
jest.mock('react-router-dom');

// Set up mock implementations after imports
const mockRedirect = jest.fn(({ to }) => (
  <div data-testid="redirect" data-to={to}>
    Redirecting to {to}
  </div>
));

const mockUnauthorized = jest.fn(() => (
  <div data-testid="unauthorized">Unauthorized</div>
));

// Apply mock implementations
jest.requireMock('react-router-dom').Redirect = mockRedirect;
jest.requireMock('../../UnauthorizedPage').default = mockUnauthorized;

describe('PLISection Component', () => {
  let store;
  let history;

  beforeEach(() => {
    history = createMemoryHistory();
    store = configureStore({}, history);
  });

  const renderComponent = (user = null) => {
    // Set the user in the global state
    store.getState().global = { user };

    return render(
      <Provider store={store}>
        <IntlProvider locale="en">
          <ConnectedRouter history={history}>
            <PLISection />
          </ConnectedRouter>
        </IntlProvider>
      </Provider>,
    );
  };

  it('should render without crashing', () => {
    const { container } = renderComponent();
    expect(container).toBeDefined();
  });

  it('should show Unauthorized component when user is null', () => {
    const { getByTestId } = renderComponent(null);
    expect(getByTestId('unauthorized')).toBeDefined();
  });

  it('should show Unauthorized component when user has invalid role', () => {
    const { getByTestId } = renderComponent({ role: 'INVALID_ROLE' });
    expect(getByTestId('unauthorized')).toBeDefined();
  });

  it('should redirect to PLI page when user has HR role', () => {
    const { getByTestId } = renderComponent({ role: ROLES.HR });
    expect(getByTestId('redirect')).toBeDefined();
    expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
  });

  it('should redirect to PLI page when user has BU role', () => {
    const { getByTestId } = renderComponent({ role: ROLES.BU });
    expect(getByTestId('redirect')).toBeDefined();
    expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
  });

  it('should redirect to PLI page when user has PM role', () => {
    const { getByTestId } = renderComponent({ role: ROLES.PM });
    expect(getByTestId('redirect')).toBeDefined();
    expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
  });

  it('should redirect to PLI page when user has RM role', () => {
    const { getByTestId } = renderComponent({ role: ROLES.RM });
    expect(getByTestId('redirect')).toBeDefined();
    expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
  });

  it('should redirect to PLI page when user has ADMIN role', () => {
    const { getByTestId } = renderComponent({ role: ROLES.ADMIN });
    expect(getByTestId('redirect')).toBeDefined();
    expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
  });

  describe('hasPliAccess function', () => {
    it('should return false when user is null', () => {
      // Create a component instance to test the hasPliAccess function directly
      const wrapper = renderComponent(null);
      expect(wrapper.getByTestId('unauthorized')).toBeDefined();
    });

    it('should return false when user has invalid role', () => {
      const wrapper = renderComponent({ role: 'INVALID_ROLE' });
      expect(wrapper.getByTestId('unauthorized')).toBeDefined();
    });

    it('should return true when user has HR role', () => {
      const wrapper = renderComponent({ role: ROLES.HR });
      expect(wrapper.getByTestId('redirect')).toBeDefined();
    });
  });

  describe('Redux connection', () => {
    it('should map state to props correctly', () => {
      // Set up a mock user in the global state
      const mockUser = { role: ROLES.HR };
      store.getState().global = { user: mockUser };

      const { getByTestId } = renderComponent(mockUser);
      expect(getByTestId('redirect')).toBeDefined();
      expect(getByTestId('redirect').getAttribute('data-to')).toBe(ROUTES.PLI);
    });
  });
});
