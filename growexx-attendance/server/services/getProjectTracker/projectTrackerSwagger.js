const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.paths['/project-tracker/project'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Project Tracker'
            ],
            description: 'Get project tracker sheet',
            summary: 'Get project tracker sheet',
            parameters: [{
                in: 'query',
                name: 'isPaginate',
                description: 'flag for pagination required.'
            },
            {
                in: 'query',
                name: 'search',
                description: 'search epic by epic name,story number and sprint name.'
            },
            {
                in: 'query',
                name: 'status',
                description: 'filter jira story by status.'
            },
            {
                in: 'query',
                name: 'boardId',
                description: 'Search by board id'
            },
            {
                in: 'query',
                name: 'page',
                description: 'Fetch the page'
            },
            {
                in: 'query',
                name: 'limit',
                description: 'Fetch the number of records'
            },
            {
                in: 'query',
                name: 'sort',
                description: 'Fetch data in sort order i.e latest fetched 1 for ASC and -1 DESC'
            },
            {
                in: 'query',
                name: 'sortBy',
                // eslint-disable-next-line max-len
                description: 'sort by following fields, [epicName, deviation,epicDeviation, storyNumber,orginalEstimation, startDate, endDate, spent]'
            }],
            responses: {
                200: {
                    description: 'Sucess',
                    schema: {
                        $ref: '#/definitions/successGetProjectTrackerSheet'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.paths['/project-tracker/epic-names'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Project Tracker'
            ],
            description: 'Get epic names for filtering',
            summary: 'Get epic names for filtering',
            parameters: [{
                in: 'query',
                name: 'boardId',
                description: 'Board ID to filter epic names',
                required: true
            },
            {
                in: 'query',
                name: 'search',
                description: 'Search term to filter epic names'
            }],
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        $ref: '#/definitions/successGetEpicNames'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.paths['/project-tracker/trigger-cron'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Project Tracker'
            ],
            description: 'Trigger cron for specific project',
            summary: 'Trigger cron for specific project',
            parameters: [
                {
                    in: 'query',
                    name: 'projectId',
                    description: 'To trigger cron for specific project'
                }
            ],
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        $ref: '#/definitions/successTriggerCron'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.paths['/project-tracker/cron-status'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'Project Tracker'
            ],
            description: 'To check the status of cron job',
            summary: 'To check the status of cron job',
            parameters: [],
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        $ref: '#/definitions/successGetCronStatus'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.successGetProjectTrackerSheet = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    docs: [{
                        '_id': '656060f36373241a63a8d766',
                        'createdAt': '2023-11-24T08:38:11.664Z',
                        'epicName': 'Test Epic 1',
                        'userStory': 'Story 3',
                        'functionalFlow': '',
                        'storyNumber': 'WA-52',
                        'storyLink': 'https://growexx.atlassian.net/browse/WA-52',
                        'salesEstimation': 55,
                        'orginalEstimation': 8,
                        'startDate': '2022-06-16T18:30:00.000Z',
                        'endDate': '2022-06-13T18:30:00.000Z',
                        'sprintName': '',
                        'currentStatus': 'Done',
                        'spent': 0,
                        'deviation': -8,
                        'epicDeviation': 55
                    }],
                    totalDocs: 1,
                    limit: 10,
                    totalPages: 1,
                    page: 1,
                    pagingCounter: 1,
                    hasPrevPage: false,
                    hasNextPage: false,
                    prevPage: null,
                    nextPage: 2
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successGetEpicNames = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'array',
                example: [
                    {
                        text: 'Epic Name 1',
                        value: 'Epic Name 1'
                    },
                    {
                        text: 'Epic Name 2',
                        value: 'Epic Name 2'
                    }
                ]
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successTriggerCron = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successGetCronStatus = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                example: {
                    _id: '65d87f2ced213fab459fa5bc',
                    isCronRunning: false,
                    isRagCronRunning: false,
                    createdAt: '2024-02-26T06:29:17.328Z',
                    updatedAt: '2024-02-26T06:29:17.328Z'
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.unexpextedError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ERROR_MSG
            }
        }
    };

    swaggerJson.definitions.validationError = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.INVALID_REQUEST
            }
        }
    };

    swaggerJson.definitions.unauthorisedAccess = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: message.ACCESS_DENIED
            }
        }
    };

    return swaggerJson;
};
