---
description: 
globs: 
alwaysApply: true
---

# Code Style and Conventions
## General Conventions
- Follow ESLint and Prettier configuration for code formatting
- Use JSDoc for documenting functions with parameter descriptions and return types
- Follow existing patterns when adding new code
## Frontend Conventions
### React Components
- Use the container/presentational pattern
  - Containers handle logic and connect to Redux
  - Presentational components focus only on rendering
- Use functional components with hooks when possible
- Follow the directory structure for components and containers
### Redux
Redux code is organized following a standard pattern:
- `actions.js`: Action creators and types
- `reducer.js`: State handling
- `saga.js`: Side effects using redux-saga
- `selectors.js`: State selectors
- `constants.js`: Action types and other constants
## Backend Conventions
### API Endpoints
- Use RESTful conventions for API endpoints
- Group related endpoints in the same router
- Validate request data before processing
### Testing
- Frontend: Jest tests in component/container test directories
- Backend: Mocha/Chai tests in service test directories
- Write unit tests for all new functionality