/**
 * @name ragReport model
 * <AUTHOR>
 */

const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');


const schema = new appMongoose.Schema({
    project: {
        type: String,
        required: true,
        trim: true
    },
    sprintReport: {
        type: String,
        required: true
    },
    deliveryHeadComments: {
        type: String,
        trim: true
    },
    sprintNumber: {
        type: String
    },
    team: [
        {
            member: {
                type: String,
                required: true,
                trim: true
            },
            b2dCount: {
                type: Number
            },
            sprintMetrics: {
                type: Number
            },
            solvedBugCount: {
                type: Number
            },
            effortVariance: {
                type: Number
            },
            escalationCount: {
                type: Number
            },
            spentHours:{
                type: Number
            },
            plannedEfforts:{
                type:Number
            }
        }
    ],
    openCloseRatio: {
        type: Number,
        required: true
    },
    effortVariance: {
        type: Number,
        required: true
    },
    unlabelledBugsCount: {
        type: Number,
        required: true
    },
    unlabelledBugsCountJQL: {
        type: String,
        required: true
    },
    totalBugs: {
        type: Number,
        required: true
    },
    bugsCreatedDuringSprint: {
        type: Number,
        required: true
    },
    spillOverBugs: {
        type: Number,
        required: true
    },
    resolvedBugs: {
        type: Number,
        required: true
    },
    clientEscalations: {
        type: String,
        required: false
    },
    risksIdentified: {
        type: String,
        required: false
    },
    mitigationPlan: {
        type: String,
        required: false
    },
    comments: {
        type: String,
        required: false
    },
    techAudit: {
        type: String,
        required: false
    },
    processAudit: {
        type: String,
        required: false
    },
    sprintStart: {
        type: Date,
        required: true
    },
    sprintEnd: {
        type: Date,
        required: true
    },
    boardId: {
        type: String,
        required: true
    },
    jiraSprintId: {
        type: String
    },
    boardKey: {
        type: String,
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    freeze: {
        type: Boolean,
        default: false
    },
    completedStoriesCount: {
        type: Number,
        required: true
    },
    totalPlannedStories: {
        type: Number,
        required: true
    },
    totalPlannedEfforts: {
        type: Number,
        required: true
    },
    totalSpentEfforts: {
        type: Number,
        required: true
    },
    apiCreated: {
        type: String,
        trim: true
    },
    doneIssues: {
        type: Array,
        default: []
    }
});
schema.path('project').required(true, 'Project name cannot be blank');
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
module.exports = appMongoose.model('rag', schema);
