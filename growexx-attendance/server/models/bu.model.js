/**
 * @name businessUnit model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    userId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user'
    },
    name: {
        type: String,
        min: 2,
        max: 30
    },
    designations: {
        type: Array
    },
    projectsList: {
        type: Array
    }
}, {
    timestamps: true
});
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('businessUnit', schema);
