const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for Jira portal add.
 */
class AddJiraPortalValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate Jira portal add
     * <AUTHOR>
     * @since 18/03/2021
     */
    validationAddJiraPortal () {
        this.name(this.body.name, 'Jira project name' );
        super.url(this.body.url, 'Jira url');
        this.email(this.body.email, 'Jira username' );
        this.token(this.body.token, 'Jira token');
    }

    /**
     * @desc This function is being used to validate Jira portal add name
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Number} name name
     */
    name (name, field) {
        if (!name) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate Jira portal add token
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Number} token token
     */
    token (token, field) {
        if (!token) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }
    }
}

module.exports = AddJiraPortalValidator;
