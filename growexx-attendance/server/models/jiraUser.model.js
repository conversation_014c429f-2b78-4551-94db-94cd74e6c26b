const { Schema, model } = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new Schema({
    jiraUser: {
        type: String,
        trim: true,
    },
    githubUser: {
        type: String,
        trim: true,
    },
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
module.exports = model('jiraUser', schema);
