const TestCase = require('./addLeave');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Add leaves', () => {

    TestCase.addLeave.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .post('/user/leave')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should able to add leave', async () => {
        const data = {
            employeeId: 100,
            email: '<EMAIL>',
            password: '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label'],
            leaveType: 'Paid Leave',
            startDate: '2021-02-01',
            endDate: '2021-02-01'
        };
        const res = await request(process.env.BASE_URL)
            .post('/user/leave')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });
});
