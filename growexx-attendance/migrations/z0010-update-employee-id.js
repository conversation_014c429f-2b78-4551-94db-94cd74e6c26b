module.exports = {
    async up (db) {
        const data =
            [
                { employeeId:129 , email: '<EMAIL>' },
                { employeeId:130 , email: '<EMAIL>' },
                { employeeId:132 , email: '<EMAIL>' },
                { employeeId:135 , email: '<EMAIL>' },
                { employeeId:136 , email: '<EMAIL>' },
                { employeeId:137 , email: '<EMAIL>' },
                { employeeId:138 , email: '<EMAIL>' },
                { employeeId:139 , email: '<EMAIL>' },
                { employeeId:140 , email: '<EMAIL>' },
                { employeeId:143 , email: '<EMAIL>' },
                { employeeId:284 , email: '<EMAIL>' },
                { employeeId:285 , email: '<EMAIL>' },
                { employeeId:286 , email: '<EMAIL>' },
                { employeeId:287 , email: '<EMAIL>' },
                { employeeId:288 , email: '<EMAIL>' },
                { employeeId:289 , email: '<EMAIL>' },
                { employeeId:290 , email: '<EMAIL>' },
                { employeeId:291 , email: '<EMAIL>' },
                { employeeId:292 , email: '<EMAIL>' },
                { employeeId:293 , email: '<EMAIL>' },
                { employeeId:294 , email: '<EMAIL>' },
                { employeeId:295 , email: '<EMAIL>' },
                { employeeId:296 , email: '<EMAIL>' },                
            ];

        data.map(async obj => {
            return await db.collection('users').updateOne({ email: obj.email }, {
                $set: { employeeId: obj.employeeId }
            });
        });
    }
};
