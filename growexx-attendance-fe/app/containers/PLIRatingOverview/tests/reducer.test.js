import {
  FETCH_PLI_RATINGS,
  FETCH_PLI_RATINGS_SUCCESS,
  FETCH_PLI_RATINGS_ERROR,
  FETCH_PLI_RATING_DETAIL,
  FETCH_PLI_RATING_DETAIL_SUCCESS,
  <PERSON>ETCH_PLI_RATING_DETAIL_ERROR,
  OVERRIDE_PLI_RATING,
  OVERRIDE_PLI_RATING_SUCCESS,
  OVERRIDE_PLI_RATING_ERROR,
  FINALIZE_PLI_RATING,
  FINALIZE_PLI_RATING_SUCCESS,
  FINALIZE_PLI_RATING_ERROR,
  SHOW_MODAL,
  HIDE_MODAL,
  PROCESS_BULK_ASSIGNMENTS,
  PROCESS_BULK_ASSIGNMENTS_SUCCESS,
  PROCESS_BULK_ASSIGNMENTS_ERROR,
  SHOW_RESULTS_MODAL,
  HIDE_RESULTS_MODAL,
  <PERSON>ET<PERSON>_MENTOR_MENTEE_DATA,
  FETCH_MENTOR_MENTEE_DATA_SUCCESS,
  FETCH_MENTOR_MENTEE_DATA_ERROR,
} from '../constants';
import reducer, { initialState } from '../reducer';

describe('pliRatingReducer', () => {
  let state;

  beforeEach(() => {
    state = {
      ...initialState,
      ratings: [],
      ratingDetail: null,
      loading: false,
      error: null,
    };
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  describe('FETCH_PLI_RATINGS', () => {
    it('should handle the fetchPLIRatings action correctly', () => {
      const expected = {
        ...state,
        loading: true,
        error: null,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATINGS,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_PLI_RATINGS_SUCCESS', () => {
    it('should handle the fetchPLIRatingsSuccess action correctly', () => {
      const mockRatings = [{ id: 1, name: 'Test Rating' }];
      const expected = {
        ...state,
        loading: false,
        ratings: mockRatings,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATINGS_SUCCESS,
          ratings: mockRatings,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_PLI_RATINGS_ERROR', () => {
    it('should handle the fetchPLIRatingsError action correctly', () => {
      const error = new Error('Failed to fetch ratings');
      const expected = {
        ...state,
        loading: false,
        error,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATINGS_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_PLI_RATING_DETAIL', () => {
    it('should handle the fetchPLIRatingDetail action correctly', () => {
      const expected = {
        ...state,
        detailLoading: true,
        detailError: null,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATING_DETAIL,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_PLI_RATING_DETAIL_SUCCESS', () => {
    it('should handle the fetchPLIRatingDetailSuccess action correctly', () => {
      const mockRatingDetail = { id: 1, name: 'Test Rating Detail' };
      const expected = {
        ...state,
        detailLoading: false,
        ratingDetail: mockRatingDetail,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATING_DETAIL_SUCCESS,
          ratingDetail: mockRatingDetail,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_PLI_RATING_DETAIL_ERROR', () => {
    it('should handle the fetchPLIRatingDetailError action correctly', () => {
      const error = new Error('Failed to fetch rating detail');
      const expected = {
        ...state,
        detailLoading: false,
        detailError: error,
      };

      expect(
        reducer(state, {
          type: FETCH_PLI_RATING_DETAIL_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });

  describe('OVERRIDE_PLI_RATING', () => {
    it('should handle the overridePLIRating action correctly', () => {
      const expected = {
        ...state,
        overrideLoading: true,
        overrideError: null,
      };

      expect(
        reducer(state, {
          type: OVERRIDE_PLI_RATING,
        }),
      ).toEqual(expected);
    });
  });

  describe('OVERRIDE_PLI_RATING_SUCCESS', () => {
    it('should handle the overridePLIRatingSuccess action correctly', () => {
      const stateWithRatings = {
        ...state,
        ratings: [{ _id: 1, name: 'Old Name' }],
      };
      const mockResult = { _id: 1, name: 'Updated Name' };

      const result = reducer(stateWithRatings, {
        type: OVERRIDE_PLI_RATING_SUCCESS,
        result: mockResult,
      });

      expect(result.overrideLoading).toBe(false);
      expect(result.ratings[0]).toEqual(mockResult);
    });
  });

  describe('OVERRIDE_PLI_RATING_ERROR', () => {
    it('should handle the overridePLIRatingError action correctly', () => {
      const error = new Error('Failed to override rating');
      const expected = {
        ...state,
        overrideLoading: false,
        overrideError: error,
      };

      expect(
        reducer(state, {
          type: OVERRIDE_PLI_RATING_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });

  describe('FINALIZE_PLI_RATING', () => {
    it('should handle the finalizePLIRating action correctly', () => {
      const expected = {
        ...state,
        finalizeLoading: true,
        finalizeError: null,
      };

      expect(
        reducer(state, {
          type: FINALIZE_PLI_RATING,
        }),
      ).toEqual(expected);
    });
  });

  describe('FINALIZE_PLI_RATING_SUCCESS', () => {
    it('should handle the finalizePLIRatingSuccess action correctly', () => {
      const stateWithRatings = {
        ...state,
        ratings: [{ _id: 1, status: 'Draft' }],
      };
      const mockResult = { _id: 1, status: 'Finalized' };

      const result = reducer(stateWithRatings, {
        type: FINALIZE_PLI_RATING_SUCCESS,
        result: mockResult,
      });

      expect(result.finalizeLoading).toBe(false);
      expect(result.ratings[0]).toEqual(mockResult);
    });
  });

  describe('FINALIZE_PLI_RATING_ERROR', () => {
    it('should handle the finalizePLIRatingError action correctly', () => {
      const error = new Error('Failed to finalize rating');
      const expected = {
        ...state,
        finalizeLoading: false,
        finalizeError: error,
      };

      expect(
        reducer(state, {
          type: FINALIZE_PLI_RATING_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });

  describe('SHOW_MODAL', () => {
    it('should handle the showModal action correctly', () => {
      const mockRating = { id: 1, name: 'Test Rating' };
      const expected = {
        ...state,
        modalVisible: true,
        currentRating: mockRating,
      };

      expect(
        reducer(state, {
          type: SHOW_MODAL,
          rating: mockRating,
        }),
      ).toEqual(expected);
    });
  });

  describe('HIDE_MODAL', () => {
    it('should handle the hideModal action correctly', () => {
      const stateWithModal = {
        ...state,
        modalVisible: true,
        currentRating: { id: 1 },
      };

      const result = reducer(stateWithModal, {
        type: HIDE_MODAL,
      });

      expect(result.modalVisible).toBe(false);
      // The reducer doesn't clear currentRating, so we should check it remains unchanged
      expect(result.currentRating).toEqual({ id: 1 });
    });
  });

  describe('PROCESS_BULK_ASSIGNMENTS', () => {
    it('should handle the processBulkAssignments action correctly', () => {
      const expected = {
        ...state,
        processingLoading: true,
        processingError: null,
      };

      expect(
        reducer(state, {
          type: PROCESS_BULK_ASSIGNMENTS,
        }),
      ).toEqual(expected);
    });
  });

  describe('PROCESS_BULK_ASSIGNMENTS_SUCCESS', () => {
    it('should handle the processBulkAssignmentsSuccess action correctly', () => {
      const mockResults = { success: true };
      const expected = {
        ...state,
        processingLoading: false,
        processingResults: mockResults,
        resultsModalVisible: true,
      };

      expect(
        reducer(state, {
          type: PROCESS_BULK_ASSIGNMENTS_SUCCESS,
          results: mockResults,
        }),
      ).toEqual(expected);
    });
  });

  describe('PROCESS_BULK_ASSIGNMENTS_ERROR', () => {
    it('should handle the processBulkAssignmentsError action correctly', () => {
      const error = new Error('Failed to process bulk assignments');
      const expected = {
        ...state,
        processingLoading: false,
        processingError: error,
        resultsModalVisible: true,
      };

      expect(
        reducer(state, {
          type: PROCESS_BULK_ASSIGNMENTS_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });

  describe('SHOW_RESULTS_MODAL', () => {
    it('should handle the showResultsModal action correctly', () => {
      const expected = {
        ...state,
        resultsModalVisible: true,
      };

      expect(
        reducer(state, {
          type: SHOW_RESULTS_MODAL,
        }),
      ).toEqual(expected);
    });
  });

  describe('HIDE_RESULTS_MODAL', () => {
    it('should handle the hideResultsModal action correctly', () => {
      const stateWithModal = {
        ...state,
        resultsModalVisible: true,
      };

      const result = reducer(stateWithModal, {
        type: HIDE_RESULTS_MODAL,
      });

      expect(result.resultsModalVisible).toBe(false);
    });
  });

  describe('FETCH_MENTOR_MENTEE_DATA', () => {
    it('should handle the fetchMentorMenteeData action correctly', () => {
      const expected = {
        ...state,
        mentorMenteeLoading: true,
        mentorMenteeError: null,
      };

      expect(
        reducer(state, {
          type: FETCH_MENTOR_MENTEE_DATA,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_MENTOR_MENTEE_DATA_SUCCESS', () => {
    it('should handle the fetchMentorMenteeDataSuccess action correctly', () => {
      const mockData = [{ id: 1, name: 'Mentor 1' }];
      const expected = {
        ...state,
        mentorMenteeLoading: false,
        mentorMenteeData: mockData,
      };

      expect(
        reducer(state, {
          type: FETCH_MENTOR_MENTEE_DATA_SUCCESS,
          data: mockData,
        }),
      ).toEqual(expected);
    });
  });

  describe('FETCH_MENTOR_MENTEE_DATA_ERROR', () => {
    it('should handle the fetchMentorMenteeDataError action correctly', () => {
      const error = new Error('Failed to fetch mentor-mentee data');
      const expected = {
        ...state,
        mentorMenteeLoading: false,
        mentorMenteeError: error,
      };

      expect(
        reducer(state, {
          type: FETCH_MENTOR_MENTEE_DATA_ERROR,
          error,
        }),
      ).toEqual(expected);
    });
  });
});
