---
description: 
globs: 
alwaysApply: true
---

# Backend Architecture
The backend is a Node.js application with a service-oriented architecture.
## Directory Structure
- @server/: Main server code
  - @models/: Database models
  - @routes/: API route definitions
  - @services/: Business logic services
  - @middleware/: Express middleware
  - @util/: Utility functions
## Services Pattern
Services in @server/services/ are organized by feature:
- Each service has its own directory
- Service directories contain test subdirectories
- Services implement business logic for specific features
Example services:
- @addUser: User creation
- @listProject: Project listing
- @signin: Authentication
## Testing
- Backend tests are located in service-specific test directories
- Tests use Mocha and Chai
- Run tests with appropriate npm scripts