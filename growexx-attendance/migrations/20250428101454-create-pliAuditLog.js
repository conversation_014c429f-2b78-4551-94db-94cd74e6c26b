/**
 * Migration for pliAuditLog collection
 * Up: Create collection if it doesn't exist
 * Down: Drop collection
 */
module.exports = {
  async up(db, client) {
    const collections = await db.listCollections({ name: 'pliAuditLog' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('pliAuditLog');
    }
  },

  async down(db, client) {
    const collections = await db.listCollections({ name: 'pliAuditLog' }).toArray();
    if (collections.length > 0) {
      await db.collection('pliAuditLog').drop();
    }
  }
};