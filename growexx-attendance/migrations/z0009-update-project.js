module.exports = {
    async up (db) {
        const businessUnitsData = await db.collection('businessunits').find().toArray();
        const project = 'Vonage';
        if (businessUnitsData && businessUnitsData.length > 0) {
            for (let i = 0;i < businessUnitsData.length;i++) {
                const data = businessUnitsData[i];
                let query;
                const businessId = data._id;
                if (data.name === 'Web & Mobile') {
                    query = { $ne: project };
                } else {
                    query = project ;
                }
                await db
                    .collection('projects')
                    .updateMany(
                        {
                            projectName: query
                        },
                        {
                            $set: {
                                businessUnitId: businessId
                            }
                        }
                    );
            }

        }
    }
};
