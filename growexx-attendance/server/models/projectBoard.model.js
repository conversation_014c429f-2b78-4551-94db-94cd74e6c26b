/**
 * @name projectBoard model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    projectId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'project'
    },
    portalId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'portal'
    },
    jiraBoardId: {
        type: String
    },
    jiraBoardName: {
        type: String
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('projectBoard', schema);
