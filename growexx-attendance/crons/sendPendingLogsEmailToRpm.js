const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });
const jwt = require('jsonwebtoken');

// Global Variables
global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');
const mongoose = require('mongoose');
mongoose.set('strictQuery', false);

const Logs = require('../server/models/logs.model');
const User = require('../server/models/user.model');
const Project = require('../server/models/project.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: true,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchLogsForLastWeek = async () => {
    try {
        const timezone = "Asia/Kolkata";
        await connectDB();
        const { endDate, startDate } = getLastWeekRange();
        const pipeline = [
            {
                $match: {
                    logStatus: 0,
                    logDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
                }
            },
            {
                $addFields: {
                    lowercaseJiraProjectName: { $toLower: "$jiraProjectName" }
                }
            },
            {
                $lookup: {
                    from: "projects",
                    let: { lowercaseJiraProjectName: "$lowercaseJiraProjectName" },
                    pipeline: [
                        {
                            $addFields: {
                                lowercaseProjectName: { $toLower: "$projectName" }
                            }
                        },
                        {
                            $match: {
                                $expr: { $eq: ["$lowercaseProjectName", "$$lowercaseJiraProjectName"] }
                            }
                        }
                    ],
                    as: "project"
                }
            },
            { $unwind: "$project" },
            {
                $lookup: {
                    from: "users",
                    localField: "project.pmUser",
                    foreignField: "_id",
                    as: "pmUserDetails"
                }
            },
            { $unwind: "$pmUserDetails" },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails"
                }
            },
            { $unwind: "$userDetails" },
            {
                $addFields: {
                    formattedDate: {
                        $dateToString: { format: "%Y-%m-%d", date: "$logDate", timezone: timezone }
                    },
                    jiraIssue: {
                        $arrayElemAt: [{ $split: ["$jiraIssueUrl", "/"] }, -1]
                    }
                }
            },
            { $sort: { "pmUserDetails.firstName": 1, "pmUserDetails.lastName": 1, "logDate": 1, } },
            {
                $group: {
                    _id: {
                        pmUserId: "$pmUserDetails._id",
                        projectId: "$project._id",
                        userId: "$userDetails._id"
                    },
                    pmUser: {
                        $first: {
                            name: { $concat: ["$pmUserDetails.firstName", " ", "$pmUserDetails.lastName"] },
                            email: "$pmUserDetails.email",
                            pmUserId: "$pmUserDetails._id"
                        }
                    },
                    project: {
                        $first: {
                            projectId: "$project._id",
                            projectName: "$project.projectName"
                        }
                    },
                    user: {
                        $first: {
                            userId: "$userDetails._id",
                            name: { $concat: ["$userDetails.firstName", " ", "$userDetails.lastName"] },
                            email: "$userDetails.email"
                        }
                    },
                    logs: {
                        $push: {
                            _id: "$_id",
                            jiraTitle: "$jiraTitle",
                            logStatus: "$logStatus",
                            logDate: "$logDate",
                            timeSpentSeconds: "$timeSpentSeconds",
                            timeSpentHours: { $divide: ["$timeSpentSeconds", 3600] },
                            daysDeviation: "$daysDeviation",
                            deviations: "$deviations",
                            jiraProjectName: "$jiraProjectName",
                            jiraIssueUrl: "$jiraIssueUrl",
                            isActive: "$isActive"
                        }
                    }
                }
            },
            {
                $group: {
                    _id: {
                        pmUserId: "$_id.pmUserId",
                        projectId: "$_id.projectId"
                    },
                    pmUser: { $first: "$pmUser" },
                    project: { $first: "$project" },
                    users: {
                        $push: {
                            userId: "$user.userId",
                            name: "$user.name",
                            email: "$user.email",
                            logs: "$logs"
                        }
                    }
                }
            },
            {
                $group: {
                    _id: "$_id.pmUserId",
                    pmUser: { $first: "$pmUser" },
                    projects: {
                        $push: {
                            projectId: "$project.projectId",
                            projectName: "$project.projectName",
                            users: "$users"
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    pmUser: 1,
                    projects: 1
                }
            }
        ];

        const result = await Logs.aggregate(pipeline);
        return result;
    } catch (error) {
        console.error("Error fetching logs grouped by PM user:", error);
        throw error;
    }
};



///// below two functions are to send all project in single mail to PM user

const notifyPendingLogs = async () => {
    try {
        await connectDB();
        const projectsWithLogs = await fetchLogsForLastWeek();
        const appUrl = process.env.FRONTEND_URL;
        await Promise.all(
            projectsWithLogs.map(async (pmUserData) => {
                const { pmUser, projects } = pmUserData;

                if (projects.length > 0) {
                    const tableData = bindProjectData(projects, pmUser);
                    
                    const templateVariables = {
                        userName: pmUser.name,
                        actionURL: `${appUrl}/review-logs`,
                        tableData: tableData,
                        content: 'You have <strong>pending</strong> timesheet requests that require your immediate attention for the following projects:',
                    };
                    const subject = 'Action Required: Pending Timesheet Requests';
                    const template = 'emailTemplates/pendingLogs.html';
                    try {
                        await Email.prepareAndSendEmail(
                            ['<EMAIL>', '<EMAIL>'],
                            subject,
                            template,
                            templateVariables
                        );
                    } catch (error) {
                        console.error(`Failed to send email to ${pmUser.email}:`, error);
                    }
                }
            })
        );

        console.log('Notification emails for pending projects sent successfully.');
        process.exit(0);
    } catch (error) {
        console.error('Error sending email notifications for pending projects:', error);
        process.exit(1);
    }
};

function bindProjectData(projects,pmUser) {
    const { endDate, startDate } = getLastWeekRange();
   
    let tableHtml = ""
    let pmUserId = pmUser.pmUserId;
    
    projects.forEach((project) => {
        // this will be usefull when you want to send by all logIDs
        // const allLogIds = project.users.flatMap(user =>
        //     user.logs.filter(log => log.logStatus === 0).map(log => log._id)
        // );
        const payload = {
            startDate,
            endDate,
            projectId: project.projectId,
            projectName: project.projectName,
            pmUserId,
            //usersLog: allLogIds
        };
        const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '23h' });

        tableHtml += `
      <div style="margin-bottom: 30px;">
        <h3 style="color: #333; margin-bottom: 15px;">${project.projectName}</h3>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="padding: 10px; border: 1px solid #ddd; text-align: left; width: 200px;">Name</th>
              ${getDatesArray(startDate, endDate)
        .map(
            (date) =>
                `<th style="padding: 10px; border: 1px solid #ddd; min-width: 100px;">
                ${new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })).toLocaleDateString("en-US", { weekday: "short" })}<br/>
                ${new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })).toLocaleDateString("en-US", { day: "2-digit", month: "short", year: "numeric" })}
                </th>`,
                )
                .join("")}
            </tr>
          </thead>
          <tbody>
            ${project.users
        .map(
            (user) => `
              <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">${user.name}</td>
                ${getDatesArray(startDate, endDate)
        .map((date) => {
            const dayLogs = user.logs.filter(log => {
            const logDate = new Date(log.logDate);
            const currentDate = new Date(date);
            // Convert both to IST for comparison
            const logDateIST = new Date(logDate.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
            const currentDateIST = new Date(currentDate.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
            return logDateIST.toDateString() === currentDateIST.toDateString();
            });

                                return `<td style="padding: 10px; border: 1px solid #ddd; vertical-align: top;">
                    ${dayLogs
                                    .map(
                                        (log) => `

                                        <a href="${log.jiraIssueUrl}" target="_blank" style="color: inherit; text-decoration: none;">
                                        <div style="
                                        margin-bottom: 4px;
                                        padding: 4px 8px;
                                        border-radius: 4px;
                                        border: 1px solid #d3d3d3;
                                        font-size: 12px;
                                        background-color: ${getStatusColor(log.logStatus)};
                                        color: ${log.logStatus === 0 ? "#000" : "#000"};
                                        ">
                                            ${log.jiraIssueUrl.split("/").pop()}
                                        
                                        <br/>
                                        ${(log.timeSpentHours).toFixed(1)}h
                                        </div></a>
                                    `,)
                                        .join("") || ""
                                    }
                  </td>`
                            })
                            .join("")}
              </tr>
            `,
                )
                .join("")}
          </tbody>
        </table>
        <!-- Action buttons with token -->
        <div style="display: flex; justify-content: center; gap: 10px; margin-top: 15px;border-bottom: 2px solid #4d186e;
  padding-bottom: 30px;">
          <a href="${process.env.BASE_URL}/user/logs/bulk-action?action=approve&token=${token}"
            style="text-decoration: none; margin-right: 20px;">
            <div style="
              padding: 12px 30px;
              background: #ffffff;
              color: #22c55e;
              font-weight: bold;
              text-align: center;
              cursor: pointer; border:1px solid #22c55e;">
              Approve All
            </div>
          </a>
          <a href="${process.env.BASE_URL}/user/logs/bulk-action?action=reject&token=${token}"
            style="text-decoration: none;">
            <div style="
              padding: 12px 30px;
              background: #ffffff;
              color: #dc2626;
              font-weight: bold;
              text-align: center;
              cursor: pointer; border:1px solid #dc2626;">
              Reject All
            </div>
          </a>
        </div>
      </div>
    `
    })

    return tableHtml
}
//// end single mail to PM user

//// below two functions are sending project wise mail 
// const notifyPendingLogs = async () => {
//     try {
//         await connectDB();
//         const projectsWithLogs = await fetchLogsForLastWeek();
//         await Promise.all(
//             projectsWithLogs.map(async (pmUserData) => {
//                 const { pmUser, projects } = pmUserData;

//                 if (projects.length > 0) {
//                     await bindProjectData(projects, pmUser);
//                 }
//             })
//         );

//         console.log('Notification emails for pending projects sent successfully.');
//         process.exit(0);
//     } catch (error) {
//         console.error('Error sending email notifications for pending projects:', error);
//         process.exit(1);
//     }
// };

// async function bindProjectData(projects, pmUser) {
//     const { endDate, startDate } = getLastWeekRange();
//     const appUrl = process.env.FRONTEND_URL;
   
//     let pmUserId = pmUser.pmUserId;
//     await Promise.all(projects.map(async (project) => {

//         // this will be usefull when you want to send by all logIDs
//         // const allLogIds = project.users.flatMap(user =>
//         //     user.logs.filter(log => log.logStatus === 0).map(log => log._id)
//         // );
//         const payload = {
//             startDate,
//             endDate,
//             projectId: project.projectId,
//             projectName: project.projectName,
//             pmUserId,
//             //usersLog: allLogIds
//         };
//         const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '23h' });

//         let tableHtml = `
//       <div style="margin-bottom: 30px;">
//         <h3 style="color: #333; margin-bottom: 15px;">${project.projectName}</h3>
//         <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
//           <thead>
//             <tr style="background-color: #f5f5f5;">
//               <th style="padding: 10px; border: 1px solid #ddd; text-align: left; width: 200px;">Name</th>
//               ${getDatesArray(startDate, endDate)
//                 .map(
//                     (date) =>
//                         `<th style="padding: 10px; border: 1px solid #ddd; text-align: center; min-width: 100px;">
//                   ${date.toLocaleDateString("en-US", { weekday: "short" })}<br/>
//                   ${date.toLocaleDateString("en-US", { day: "2-digit", month: "short", year: "numeric" })}
//                 </th>`,
//                 )
//                 .join("")}
//             </tr>
//           </thead>
//           <tbody>
//             ${project.users
//                 .map(
//                     (user) => `
//               <tr>
//                 <td style="padding: 10px; border: 1px solid #ddd;">${user.name}</td>
//                 ${getDatesArray(startDate, endDate)
//                             .map((date) => {
//                                 const dayLogs = user.logs.filter(
//                                     (log) => new Date(log.logDate).toDateString() === date.toDateString(),
//                                 )

//                                 return `<td style="padding: 10px; border: 1px solid #ddd; vertical-align: top;">
//                     ${dayLogs
//                                         .map(
//                                             (log) => `

//                                         <a href="${log.jiraIssueUrl}" target="_blank" style="color: inherit; text-decoration: none;">
//                                         <div style="
//                                         margin-bottom: 4px;
//                                         padding: 4px 8px;
//                                         border-radius: 4px;
//                                         border: 1px solid #d3d3d3;
//                                         font-size: 12px;
//                                         background-color: ${getStatusColor(log.logStatus)};
//                                         color: ${log.logStatus === 0 ? "#000" : "#000"};
//                                         ">
//                                             ${log.jiraIssueUrl.split("/").pop()}
                                        
//                                         <br/>
//                                         ${(log.timeSpentHours).toFixed(1)}h
//                                         </div></a>
//                                     `,)
//                                         .join("") || ""
//                                     }
//                   </td>`
//                             })
//                             .join("")}
//               </tr>
//             `,
//                 )
//                 .join("")}
//           </tbody>
//         </table>
//         <!-- Action buttons with token -->
//         <div style="display: flex; justify-content: space-between; gap: 10px; margin-top: 15px;">
//           <a href="${process.env.BASE_URL}/user/logs/bulk-action?action=approve&token=${token}"
//             style="text-decoration: none;">
//             <div style="
//               padding: 12px 30px;
//               background: #ffffff;
//               color: #22c55e;
//               font-weight: bold;
//               text-align: center;
//               cursor: pointer; border:1px solid #22c55e;">
//               Approve All
//             </div>
//           </a>
//           <a href="${process.env.BASE_URL}/user/logs/bulk-action?action=reject&token=${token}"
//             style="text-decoration: none;">
//             <div style="
//               padding: 12px 30px;
//               background: #ffffff;
//               color: #dc2626;
//               font-weight: bold;
//               text-align: center;
//               cursor: pointer; border:1px solid #dc2626;">
//               Reject All
//             </div>
//           </a>
//         </div>
//       </div>`

//         const templateVariables = {
//             userName: pmUser.name,
//             actionURL: `${appUrl}/review-logs`,
//             tableData: tableHtml,
//             content: 'You have <strong>pending</strong> timesheet requests that require your immediate attention for the following projects:',
//         };
//         const subject = 'Action Required: Pending Timesheet Requests';
//         const template = 'emailTemplates/pendingLogs.html';
//         try {
//             await Email.prepareAndSendEmail(
//                 ['<EMAIL>'],
//                 subject,
//                 template,
//                 templateVariables
//             );
//         } catch (error) {
//             console.error(`Failed to send email to ${pmUser.email}:`, error);
//         }
//     }));

//     //return tableHtml
// }
//// project wise mail end

function getDatesArray(startDate, endDate) {
    const dates = []
    const currentDate = new Date(startDate)

    while (currentDate <= endDate) {
        dates.push(new Date(currentDate))
        currentDate.setDate(currentDate.getDate() + 1)
    }

    return dates
}

function getStatusColor(status) {
    switch (status) {
        case 2:
            return "rgb(247,216,217)"
        case 1:
            return "rgb(173, 238, 184)"
        case 0:
            return "rgb(252, 277, 176)"
        default:
            return "#e5e7eb"
    }
}

// const getLastWeekRange = () => {
//     // Use a specific timezone by creating a date string with the timezone
//     const now = new Date();
    
//     // Get the current date in the Asia/Kolkata timezone
//     const options = { timeZone: 'Asia/Kolkata' };
//     const indiaDateStr = now.toLocaleString('en-US', options);
//     const indiaDate = new Date(indiaDateStr);
    
//     // Calculate the start of last week (Monday)
//     const day = indiaDate.getDay(); // 0 is Sunday, 1 is Monday
//     const diffToLastMonday = indiaDate.getDate() - day + (day === 0 ? -6 : 1) - 7;
    
//     // Create the start date for last Monday
//     const startDate = new Date(indiaDate);
//     startDate.setDate(diffToLastMonday);
//     startDate.setHours(0, 0, 0, 0);
    
//     // Create the end date for last Sunday
//     const endDate = new Date(startDate);
//     endDate.setDate(startDate.getDate() + 6);
//     endDate.setHours(23, 59, 59, 999);
    
//     // console.log("Last week range in IST:", 
//     //             startDate.toLocaleString('en-US', {timeZone: 'Asia/Kolkata'}), 
//     //             endDate.toLocaleString('en-US', {timeZone: 'Asia/Kolkata'}));
    
//     return { startDate, endDate };
// };

const getLastWeekRange = () => {
    // Create the current date in Indian time zone directly
    const now = new Date();
    // Adjust to Indian time zone (UTC +5:30)
    const indiaOffset = 5.5 * 60 * 60 * 1000; // 5 hours 30 minutes in milliseconds
    const indiaDate = new Date(now.getTime() + indiaOffset);

    // Calculate the start of last week (Monday)
    const day = indiaDate.getUTCDay(); // 0 = Sunday, 1 = Monday
    const diffToLastMonday = indiaDate.getUTCDate() - day + (day === 0 ? -6 : 1) - 7;

    // Create the start date for last Monday in IST
    const startDate = new Date(indiaDate);
    startDate.setUTCDate(diffToLastMonday);
    startDate.setUTCHours(0, 0, 0, 0);

    // Create the end date for last Sunday in IST
    const endDate = new Date(startDate);
    endDate.setUTCDate(startDate.getUTCDate() + 6);
    endDate.setUTCHours(23, 59, 59, 999);

    // Adjust back to IST
    startDate.setTime(startDate.getTime() - indiaOffset);
    endDate.setTime(endDate.getTime() - indiaOffset);

    return { startDate, endDate };
};

notifyPendingLogs();
